/**
 * Script de correction pour le problème "Payment method field is required"
 * À inclure dans les pages de paiement pour garantir le bon fonctionnement
 */

(function() {
    'use strict';
    
    // Configuration
    const CONFIG = {
        selectors: {
            paymentCards: '.payment-card',
            paymentRadios: 'input[name="payment_method"]',
            paymentForm: '#paymentForm',
            referenceField: '.reference-field',
            referenceInput: '#reference_number'
        },
        defaultMethod: 'cash',
        checkInterval: 3000,
        debug: true
    };
    
    // Utilitaires de logging
    const log = {
        info: (msg, ...args) => CONFIG.debug && console.log(`[PaymentFix] ${msg}`, ...args),
        warn: (msg, ...args) => CONFIG.debug && console.warn(`[PaymentFix] ${msg}`, ...args),
        error: (msg, ...args) => console.error(`[PaymentFix] ${msg}`, ...args)
    };
    
    // État du système
    let state = {
        initialized: false,
        lastSelectedMethod: null,
        checkInterval: null
    };
    
    /**
     * Initialise le système de correction des paiements
     */
    function init() {
        if (state.initialized) {
            log.warn('Système déjà initialisé');
            return;
        }
        
        log.info('Initialisation du système de correction des paiements');
        
        // Attendre que le DOM soit prêt
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupPaymentSystem);
        } else {
            setupPaymentSystem();
        }
    }
    
    /**
     * Configure le système de paiement
     */
    function setupPaymentSystem() {
        try {
            // Vérifier la présence des éléments requis
            if (!validateRequiredElements()) {
                log.warn('Éléments requis manquants - arrêt de l\'initialisation');
                return;
            }
            
            // Configurer les gestionnaires d'événements
            setupEventHandlers();
            
            // Initialiser la sélection par défaut
            initializeDefaultSelection();
            
            // Démarrer la surveillance
            startMonitoring();
            
            state.initialized = true;
            log.info('Système de correction des paiements initialisé avec succès');
            
        } catch (error) {
            log.error('Erreur lors de l\'initialisation:', error);
        }
    }
    
    /**
     * Valide la présence des éléments requis
     */
    function validateRequiredElements() {
        const cards = document.querySelectorAll(CONFIG.selectors.paymentCards);
        const radios = document.querySelectorAll(CONFIG.selectors.paymentRadios);
        const form = document.querySelector(CONFIG.selectors.paymentForm);
        
        if (cards.length === 0) {
            log.warn('Aucune carte de paiement trouvée');
            return false;
        }
        
        if (radios.length === 0) {
            log.warn('Aucun radio button de paiement trouvé');
            return false;
        }
        
        if (!form) {
            log.warn('Formulaire de paiement non trouvé');
            return false;
        }
        
        log.info(`Validation réussie: ${cards.length} cartes, ${radios.length} radios`);
        return true;
    }
    
    /**
     * Configure les gestionnaires d'événements
     */
    function setupEventHandlers() {
        const cards = document.querySelectorAll(CONFIG.selectors.paymentCards);
        const form = document.querySelector(CONFIG.selectors.paymentForm);
        
        // Gestionnaires pour les cartes de paiement
        cards.forEach(card => {
            card.addEventListener('click', handleCardClick);
        });
        
        // Gestionnaire pour la soumission du formulaire
        if (form) {
            form.addEventListener('submit', handleFormSubmit);
        }
        
        log.info('Gestionnaires d\'événements configurés');
    }
    
    /**
     * Gère le clic sur une carte de paiement
     */
    function handleCardClick(event) {
        try {
            const card = event.currentTarget;
            const method = card.dataset.method;
            
            if (!method) {
                log.warn('Méthode de paiement non définie pour la carte');
                return;
            }
            
            log.info(`Sélection de la méthode: ${method}`);
            
            // Désélectionner toutes les cartes
            document.querySelectorAll(CONFIG.selectors.paymentCards).forEach(c => {
                c.classList.remove('selected');
                const radio = c.querySelector('input[type="radio"]');
                if (radio) radio.checked = false;
            });
            
            // Sélectionner la carte cliquée
            card.classList.add('selected');
            const radio = card.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
                radio.dispatchEvent(new Event('change', { bubbles: true }));
            }
            
            // Gérer le champ de référence
            toggleReferenceField(method);
            
            // Mettre à jour l'état
            state.lastSelectedMethod = method;
            
        } catch (error) {
            log.error('Erreur lors du clic sur la carte:', error);
        }
    }
    
    /**
     * Gère la soumission du formulaire
     */
    function handleFormSubmit(event) {
        const selectedMethod = getSelectedMethod();
        
        if (!selectedMethod) {
            event.preventDefault();
            showError('Veuillez sélectionner une méthode de paiement.');
            highlightPaymentCards();
            return false;
        }
        
        // Vérifier la référence si nécessaire
        if (isReferenceRequired(selectedMethod)) {
            const reference = document.querySelector(CONFIG.selectors.referenceInput);
            if (!reference || !reference.value.trim()) {
                event.preventDefault();
                showError('Veuillez saisir un numéro de référence pour ce mode de paiement.');
                if (reference) reference.focus();
                return false;
            }
        }
        
        log.info('Validation du formulaire réussie');
        return true;
    }
    
    /**
     * Initialise la sélection par défaut
     */
    function initializeDefaultSelection() {
        const checkedRadio = document.querySelector(`${CONFIG.selectors.paymentRadios}:checked`);
        
        if (checkedRadio) {
            const card = checkedRadio.closest(CONFIG.selectors.paymentCards);
            if (card) {
                card.classList.add('selected');
                const method = card.dataset.method;
                if (method) {
                    toggleReferenceField(method);
                    state.lastSelectedMethod = method;
                    log.info(`Méthode par défaut initialisée: ${method}`);
                }
            }
        } else {
            // Sélectionner la méthode par défaut
            const defaultCard = document.querySelector(`${CONFIG.selectors.paymentCards}[data-method="${CONFIG.defaultMethod}"]`);
            if (defaultCard) {
                defaultCard.click();
                log.info(`Méthode par défaut sélectionnée: ${CONFIG.defaultMethod}`);
            }
        }
    }
    
    /**
     * Démarre la surveillance automatique
     */
    function startMonitoring() {
        if (state.checkInterval) {
            clearInterval(state.checkInterval);
        }
        
        state.checkInterval = setInterval(() => {
            const selectedMethod = getSelectedMethod();
            if (!selectedMethod) {
                log.warn('Aucune méthode sélectionnée - correction automatique');
                initializeDefaultSelection();
            }
        }, CONFIG.checkInterval);
        
        log.info('Surveillance automatique démarrée');
    }
    
    /**
     * Obtient la méthode de paiement sélectionnée
     */
    function getSelectedMethod() {
        const checkedRadio = document.querySelector(`${CONFIG.selectors.paymentRadios}:checked`);
        return checkedRadio ? checkedRadio.value : null;
    }
    
    /**
     * Vérifie si une référence est requise pour la méthode
     */
    function isReferenceRequired(method) {
        return ['bank_transfer', 'check', 'mobile_money'].includes(method);
    }
    
    /**
     * Gère l'affichage du champ de référence
     */
    function toggleReferenceField(method) {
        const referenceField = document.querySelector(CONFIG.selectors.referenceField);
        const referenceInput = document.querySelector(CONFIG.selectors.referenceInput);
        
        if (!referenceField || !referenceInput) return;
        
        if (method === 'cash') {
            referenceField.style.display = 'none';
            referenceInput.removeAttribute('required');
            referenceInput.value = '';
        } else {
            referenceField.style.display = 'block';
            referenceInput.setAttribute('required', 'required');
        }
    }
    
    /**
     * Affiche un message d'erreur
     */
    function showError(message) {
        // Supprimer les anciennes alertes
        document.querySelectorAll('.payment-error-alert').forEach(alert => alert.remove());
        
        const alertHtml = `
            <div class="alert alert-danger payment-error-alert" style="border-radius: 12px; border-left: 4px solid #F44336;">
                <h6 class="alert-heading d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span>Erreur de validation</span>
                </h6>
                <p class="mb-0">${message}</p>
            </div>
        `;
        
        const form = document.querySelector(CONFIG.selectors.paymentForm);
        if (form) {
            form.insertAdjacentHTML('beforebegin', alertHtml);
            
            // Supprimer l'alerte après 5 secondes
            setTimeout(() => {
                const alert = document.querySelector('.payment-error-alert');
                if (alert) {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }
            }, 5000);
        }
    }
    
    /**
     * Met en évidence les cartes de paiement
     */
    function highlightPaymentCards() {
        const cards = document.querySelectorAll(CONFIG.selectors.paymentCards);
        cards.forEach(card => {
            card.style.borderColor = '#F44336';
            card.style.animation = 'shake 0.5s ease-in-out';
        });
        
        setTimeout(() => {
            cards.forEach(card => {
                card.style.borderColor = '';
                card.style.animation = '';
            });
        }, 3000);
    }
    
    /**
     * API publique
     */
    window.PaymentMethodFix = {
        init: init,
        getSelectedMethod: getSelectedMethod,
        validateSelection: () => !!getSelectedMethod(),
        forceSelection: (method) => {
            const card = document.querySelector(`${CONFIG.selectors.paymentCards}[data-method="${method}"]`);
            if (card) card.click();
        },
        debug: () => {
            log.info('État du système:', {
                initialized: state.initialized,
                lastSelectedMethod: state.lastSelectedMethod,
                currentSelection: getSelectedMethod(),
                cardsCount: document.querySelectorAll(CONFIG.selectors.paymentCards).length,
                radiosCount: document.querySelectorAll(CONFIG.selectors.paymentRadios).length
            });
        }
    };
    
    // Auto-initialisation
    init();
    
})();

// Message de confirmation
console.log('✅ Script de correction des méthodes de paiement chargé. Utilisez PaymentMethodFix.debug() pour déboguer.');
