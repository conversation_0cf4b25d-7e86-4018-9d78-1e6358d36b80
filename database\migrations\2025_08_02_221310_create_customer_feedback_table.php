<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_feedback', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('cement_order_detail_id')->nullable()->constrained('cement_order_details')->onDelete('cascade');
            $table->string('type')->default('general'); // 'delivery', 'service', 'product', 'general'
            $table->string('category'); // 'suggestion', 'complaint', 'compliment', 'question'
            $table->string('subject');
            $table->text('message');
            $table->integer('delivery_rating')->nullable(); // 1-5 stars pour les livraisons
            $table->integer('service_rating')->nullable(); // 1-5 stars pour le service
            $table->integer('product_rating')->nullable(); // 1-5 stars pour le produit
            $table->integer('overall_rating')->nullable(); // 1-5 stars global
            $table->json('delivery_aspects')->nullable(); // Aspects spécifiques de la livraison
            $table->string('driver_name')->nullable();
            $table->string('truck_number')->nullable();
            $table->datetime('delivery_date')->nullable();
            $table->string('status')->default('pending'); // 'pending', 'reviewed', 'resolved', 'closed'
            $table->text('admin_response')->nullable();
            $table->foreignId('responded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('responded_at')->nullable();
            $table->boolean('is_anonymous')->default(false);
            $table->string('priority')->default('normal'); // 'low', 'normal', 'high', 'urgent'
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_feedback');
    }
};
