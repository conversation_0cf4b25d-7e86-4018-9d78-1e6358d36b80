# Déploiement Gradis sur Namecheap - Guide Complet

## 🎯 Résumé des Optimisations Effectuées

### ✅ Optimisations de Performance
- **Cache Redis** configuré pour les sessions et le cache applicatif
- **Compression GZIP** activée pour tous les assets
- **Cache des assets statiques** avec expiration longue (1 an)
- **Minification HTML** automatique en production
- **Optimisation des requêtes** avec mise en cache intelligente
- **Compilation optimisée** des assets avec Vite

### ✅ Sécurité Renforcée
- **Headers de sécurité** complets (CSP, HSTS, XSS Protection)
- **Rate limiting** par type de requête (login, API, upload, recherche)
- **Validation et sanitisation** automatique des entrées
- **Protection contre** SQL injection et XSS
- **Chiffrement des sessions** en production
- **Masquage des informations** serveur

### ✅ Monitoring et Maintenance
- **Tâches cron automatisées** pour la maintenance
- **Nettoyage automatique** des logs et fichiers temporaires
- **Sauvegardes quotidiennes** de la base de données
- **Surveillance de l'état** de l'application
- **Optimisation automatique** des images

## 🚀 Instructions de Déploiement Namecheap

### Étape 1: Préparation du Serveur Namecheap

#### Configuration Recommandée
- **Plan**: Business Hosting ou VPS (recommandé pour de meilleures performances)
- **PHP**: Version 8.1 ou 8.2
- **MySQL**: Version 8.0
- **SSL**: Activé (Let's Encrypt gratuit)
- **Redis**: Demander l'activation au support Namecheap

#### Extensions PHP Requises
Vérifiez que ces extensions sont activées dans cPanel > PHP Selector:
```
- BCMath
- Ctype
- Fileinfo
- JSON
- Mbstring
- OpenSSL
- PDO
- PDO MySQL
- Tokenizer
- XML
- GD
- Zip
- Redis (si disponible)
```

### Étape 2: Configuration de la Base de Données

1. **Créer la base de données** via cPanel > MySQL Databases
   - Nom: `gradis_production`
   - Utilisateur: `gradis_user`
   - Mot de passe: Générer un mot de passe fort

2. **Accorder tous les privilèges** à l'utilisateur sur la base

### Étape 3: Upload et Configuration

#### Upload des Fichiers
```bash
# Méthode 1: Git (si SSH disponible)
git clone https://github.com/votre-repo/gradis.git
cd gradis

# Méthode 2: FTP
# Compresser le projet localement (exclure node_modules, .git)
# Uploader via FileZilla ou cPanel File Manager
```

#### Configuration de l'Environnement
```bash
# Copier le fichier de production
cp .env.production .env

# Modifier les variables pour Namecheap
nano .env
```

**Variables importantes à configurer:**
```env
APP_NAME=Gradis
APP_ENV=production
APP_DEBUG=false
APP_URL=https://votre-domaine.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=votre_nom_db
DB_USERNAME=votre_user_db
DB_PASSWORD=votre_password_db

# Cache (Redis si disponible, sinon file)
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Mail (utiliser les paramètres Namecheap)
MAIL_MAILER=smtp
MAIL_HOST=mail.votre-domaine.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=votre_password_email
MAIL_ENCRYPTION=tls
```

### Étape 4: Installation et Déploiement

#### Exécution du Script de Déploiement
```bash
# Rendre le script exécutable
chmod +x deploy.sh

# Exécuter le déploiement
./deploy.sh production
```

#### Installation Manuelle (si le script ne fonctionne pas)
```bash
# 1. Installer les dépendances
composer install --no-dev --optimize-autoloader
npm ci --production

# 2. Générer la clé d'application
php artisan key:generate

# 3. Exécuter les migrations
php artisan migrate --force

# 4. Compiler les assets
npm run build

# 5. Optimiser Laravel
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 6. Configurer les permissions
chmod -R 775 storage bootstrap/cache

# 7. Exécuter le script post-déploiement
php scripts/post-deploy.php
```

### Étape 5: Configuration du Domaine

#### Pointer le Domaine
1. Dans cPanel > Subdomains ou Addon Domains
2. Pointer vers le dossier `public` de Laravel
3. Exemple: `/public_html/gradis/public`

#### Configuration SSL
1. cPanel > SSL/TLS
2. Activer "Let's Encrypt SSL"
3. Forcer HTTPS dans .env: `FORCE_HTTPS=true`

### Étape 6: Configuration des Tâches Cron

#### Ajouter dans cPanel > Cron Jobs
```bash
# Tâche principale Laravel (toutes les minutes)
* * * * * cd /home/<USER>/public_html/gradis && php artisan schedule:run >/dev/null 2>&1

# Sauvegarde quotidienne (optionnel)
0 3 * * * cd /home/<USER>/public_html/gradis && php scripts/backup.php >/dev/null 2>&1
```

### Étape 7: Tests et Vérification

#### Vérifications Post-Déploiement
1. **Accès au site**: https://votre-domaine.com
2. **Connexion admin**: Tester la connexion
3. **Fonctionnalités**: Tester les principales fonctions
4. **Performance**: Vérifier les temps de chargement
5. **Sécurité**: Tester les headers avec https://securityheaders.com

#### Monitoring
- **Logs Laravel**: `storage/logs/laravel.log`
- **Logs serveur**: Accessible via cPanel > Error Logs
- **Performance**: Utiliser Google PageSpeed Insights

## 🔧 Optimisations Spécifiques Namecheap

### Configuration .htaccess Optimisée
Le fichier `.htaccess` a été optimisé avec:
- Compression GZIP
- Cache des assets statiques
- Headers de sécurité
- Protection des fichiers sensibles

### Gestion de la Mémoire
```php
// Dans .htaccess ou demander au support
php_value memory_limit 256M
php_value max_execution_time 300
php_value upload_max_filesize 10M
php_value post_max_size 10M
```

### Optimisation MySQL
```sql
-- Optimiser les tables (à exécuter périodiquement)
OPTIMIZE TABLE cement_orders, users, products;

-- Analyser les performances
SHOW PROCESSLIST;
EXPLAIN SELECT * FROM cement_orders WHERE status = 'pending';
```

## 📊 Monitoring et Maintenance

### Surveillance Automatique
- **Vérification de l'état**: Toutes les 5 minutes
- **Nettoyage des logs**: Quotidien
- **Sauvegarde DB**: Quotidienne à 3h
- **Optimisation images**: Hebdomadaire

### Maintenance Manuelle
```bash
# Vérifier l'espace disque
df -h

# Vérifier les logs d'erreur
tail -f storage/logs/laravel.log

# Nettoyer manuellement
php artisan cache:clear
php artisan view:clear

# Optimiser la base de données
php artisan db:optimize
```

## 🆘 Dépannage

### Erreurs Communes

#### Erreur 500 - Internal Server Error
1. Vérifier `storage/logs/laravel.log`
2. Vérifier les permissions: `chmod -R 775 storage`
3. Vérifier la configuration .env
4. Vérifier que la clé APP_KEY est définie

#### Base de Données Inaccessible
1. Vérifier les informations de connexion dans .env
2. Tester la connexion depuis cPanel > phpMyAdmin
3. Vérifier que l'utilisateur a les bonnes permissions

#### Assets Non Chargés
1. Vérifier que `npm run build` a été exécuté
2. Vérifier APP_URL dans .env
3. Vérifier les permissions du dossier public

#### Performance Lente
1. Activer Redis si disponible
2. Vérifier les requêtes lentes dans les logs
3. Optimiser les images
4. Activer la compression GZIP

### Support Technique
- **Namecheap**: Support 24/7 via chat ou ticket
- **Laravel**: Documentation officielle
- **Logs**: Toujours vérifier les logs en premier

## 📋 Checklist Final

- [ ] Serveur Namecheap configuré (PHP 8.1+, MySQL 8.0)
- [ ] Extensions PHP activées
- [ ] Base de données créée et configurée
- [ ] Fichiers uploadés et .env configuré
- [ ] Dépendances installées (Composer + NPM)
- [ ] Migrations exécutées
- [ ] Assets compilés (npm run build)
- [ ] Optimisations Laravel appliquées
- [ ] Permissions configurées
- [ ] Domaine pointé vers /public
- [ ] SSL activé et forcé
- [ ] Tâches cron configurées
- [ ] Tests de fonctionnement OK
- [ ] Monitoring configuré
- [ ] Documentation équipe mise à jour

## 🎉 Félicitations !

Votre application Gradis est maintenant déployée et optimisée sur Namecheap avec:
- **Performance maximale** grâce aux optimisations de cache et compression
- **Sécurité renforcée** avec protection contre les attaques courantes
- **Maintenance automatisée** pour un fonctionnement optimal
- **Monitoring continu** pour détecter les problèmes rapidement

L'application est prête pour la production ! 🚀
