<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Configuration des Performances
    |--------------------------------------------------------------------------
    |
    | Ce fichier contient les paramètres de configuration pour optimiser
    | les performances de l'application, notamment pour les dashboards
    | Admin et Comptable.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Cache des Dashboards
    |--------------------------------------------------------------------------
    |
    | Durées de cache en secondes pour les différentes données des dashboards
    |
    */
    'dashboard_cache' => [
        'admin_stats' => env('ADMIN_STATS_CACHE', 600), // 10 minutes
        'admin_charts' => env('ADMIN_CHARTS_CACHE', 900), // 15 minutes
        'admin_recent_data' => env('ADMIN_RECENT_DATA_CACHE', 300), // 5 minutes
        'admin_vehicle_stats' => env('ADMIN_VEHICLE_STATS_CACHE', 300), // 5 minutes
        'admin_alerts_extras' => env('ADMIN_ALERTS_EXTRAS_CACHE', 300), // 5 minutes
        'admin_stock_data' => env('ADMIN_STOCK_DATA_CACHE', 600), // 10 minutes
        
        'accountant_stats' => env('ACCOUNTANT_STATS_CACHE', 300), // 5 minutes
        'accountant_recent' => env('ACCOUNTANT_RECENT_CACHE', 180), // 3 minutes
        'accountant_charts' => env('ACCOUNTANT_CHARTS_CACHE', 600), // 10 minutes
        'accountant_category_product' => env('ACCOUNTANT_CATEGORY_PRODUCT_CACHE', 900), // 15 minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache des Services
    |--------------------------------------------------------------------------
    |
    | Durées de cache pour les services lourds
    |
    */
    'service_cache' => [
        'stock_status' => env('STOCK_STATUS_CACHE', 600), // 10 minutes
        'stock_movements' => env('STOCK_MOVEMENTS_CACHE', 300), // 5 minutes
        'stock_alerts' => env('STOCK_ALERTS_CACHE', 300), // 5 minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Optimisations des Middlewares
    |--------------------------------------------------------------------------
    |
    | Configuration pour les middlewares d'optimisation
    |
    */
    'middleware' => [
        'enable_manual_compression' => env('ENABLE_MANUAL_COMPRESSION', false),
        'enable_html_minification' => env('ENABLE_HTML_MINIFICATION', false),
        'compression_threshold' => env('COMPRESSION_THRESHOLD', 4096), // 4KB
        'minification_threshold' => env('MINIFICATION_THRESHOLD', 8192), // 8KB
    ],

    /*
    |--------------------------------------------------------------------------
    | Optimisations des Requêtes
    |--------------------------------------------------------------------------
    |
    | Configuration pour optimiser les requêtes de base de données
    |
    */
    'database' => [
        'enable_query_cache' => env('ENABLE_QUERY_CACHE', true),
        'max_eager_load_relations' => env('MAX_EAGER_LOAD_RELATIONS', 3),
        'chunk_size' => env('DB_CHUNK_SIZE', 1000),
        'enable_query_log' => env('ENABLE_QUERY_LOG', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Optimisations des Vues
    |--------------------------------------------------------------------------
    |
    | Configuration pour optimiser le rendu des vues
    |
    */
    'views' => [
        'enable_view_cache' => env('ENABLE_VIEW_CACHE', true),
        'cache_compiled_views' => env('CACHE_COMPILED_VIEWS', true),
        'optimize_blade_directives' => env('OPTIMIZE_BLADE_DIRECTIVES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring des Performances
    |--------------------------------------------------------------------------
    |
    | Configuration pour surveiller les performances
    |
    */
    'monitoring' => [
        'enable_performance_logging' => env('ENABLE_PERFORMANCE_LOGGING', false),
        'slow_query_threshold' => env('SLOW_QUERY_THRESHOLD', 1000), // 1 seconde en ms
        'memory_usage_threshold' => env('MEMORY_USAGE_THRESHOLD', 128), // 128MB
        'log_slow_requests' => env('LOG_SLOW_REQUESTS', true),
        'slow_request_threshold' => env('SLOW_REQUEST_THRESHOLD', 2000), // 2 secondes en ms
    ],

    /*
    |--------------------------------------------------------------------------
    | Optimisations Spécifiques aux Dashboards
    |--------------------------------------------------------------------------
    |
    | Configuration spécifique pour optimiser les dashboards
    |
    */
    'dashboard_optimizations' => [
        'lazy_load_charts' => env('LAZY_LOAD_CHARTS', true),
        'paginate_large_datasets' => env('PAGINATE_LARGE_DATASETS', true),
        'max_dashboard_items' => env('MAX_DASHBOARD_ITEMS', 50),
        'enable_real_time_updates' => env('ENABLE_REAL_TIME_UPDATES', false),
        'update_interval' => env('DASHBOARD_UPDATE_INTERVAL', 30000), // 30 secondes en ms
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Tags
    |--------------------------------------------------------------------------
    |
    | Tags de cache pour une invalidation sélective
    |
    */
    'cache_tags' => [
        'admin_dashboard' => 'admin_dashboard',
        'accountant_dashboard' => 'accountant_dashboard',
        'stock_data' => 'stock_data',
        'user_data' => 'user_data',
        'order_data' => 'order_data',
        'supply_data' => 'supply_data',
    ],
];
