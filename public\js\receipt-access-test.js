/**
 * Script de test pour vérifier l'accès aux reçus
 */

function testReceiptAccess() {
    console.log('=== TEST D\'ACCÈS AUX REÇUS ===');
    
    // Récupérer tous les boutons de reçu
    const receiptButtons = document.querySelectorAll('a[href*="receipt"]');
    
    console.log(`Nombre de boutons de reçu trouvés: ${receiptButtons.length}`);
    
    receiptButtons.forEach((button, index) => {
        const href = button.getAttribute('href');
        const title = button.getAttribute('title') || 'Sans titre';
        
        console.log(`Bouton ${index + 1}: ${title}`);
        console.log(`  URL: ${href}`);
        
        // Vérifier le type de reçu
        if (href.includes('cashier-receipt')) {
            console.log('  Type: Reçu caissier');
        } else if (href.includes('payments') && href.includes('receipt')) {
            console.log('  Type: Reçu comptable');
        } else {
            console.log('  Type: Inconnu');
        }
    });
    
    // Tester l'accès à un reçu caissier si disponible
    const cashierReceiptButton = document.querySelector('a[href*="cashier-receipt"]');
    if (cashierReceiptButton) {
        console.log('\n🧪 Test d\'accès au reçu caissier...');
        const url = cashierReceiptButton.getAttribute('href');
        
        // Faire une requête HEAD pour tester l'accès sans ouvrir la page
        fetch(url, { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    console.log('✅ Accès au reçu caissier: OK');
                } else if (response.status === 403) {
                    console.log('❌ Accès au reçu caissier: REFUSÉ (403)');
                } else {
                    console.log(`⚠️ Accès au reçu caissier: ${response.status} ${response.statusText}`);
                }
            })
            .catch(error => {
                console.log('❌ Erreur lors du test d\'accès:', error);
            });
    }
    
    console.log('=== FIN DU TEST ===');
}

// Fonction pour tester l'ouverture d'un reçu spécifique
function testOpenReceipt(paymentId, type = 'auto') {
    console.log(`🧪 Test d'ouverture du reçu pour le paiement ${paymentId}`);
    
    let url;
    if (type === 'cashier') {
        url = `/accountant/cashier-receipt/${paymentId}`;
    } else if (type === 'accountant') {
        url = `/accountant/payments/${paymentId}/receipt`;
    } else {
        // Auto-détection basée sur la position
        const paymentRow = document.querySelector(`button[onclick*="${paymentId}"]`)?.closest('tr');
        if (paymentRow) {
            const position = paymentRow.cells[6]?.textContent.trim();
            if (position === 'Caissier' || position === 'cashier') {
                url = `/accountant/cashier-receipt/${paymentId}`;
                console.log('Auto-détection: Reçu caissier');
            } else {
                url = `/accountant/payments/${paymentId}/receipt`;
                console.log('Auto-détection: Reçu comptable');
            }
        } else {
            console.log('❌ Impossible de détecter le type de reçu');
            return;
        }
    }
    
    console.log(`URL de test: ${url}`);
    
    // Tester l'accès
    fetch(url, { method: 'HEAD' })
        .then(response => {
            if (response.ok) {
                console.log('✅ Test réussi - Ouverture du reçu...');
                window.open(url, '_blank');
            } else {
                console.log(`❌ Test échoué: ${response.status} ${response.statusText}`);
            }
        })
        .catch(error => {
            console.log('❌ Erreur lors du test:', error);
        });
}

// Fonction pour diagnostiquer les permissions utilisateur
function diagnoseUserPermissions() {
    console.log('=== DIAGNOSTIC DES PERMISSIONS UTILISATEUR ===');
    
    // Récupérer les informations utilisateur depuis les métadonnées de la page
    const userInfo = {
        currentUrl: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
    };
    
    console.log('Informations de base:', userInfo);
    
    // Tester l'accès aux différentes routes
    const testRoutes = [
        '/accountant/dashboard',
        '/accountant/recoveries',
        '/accountant/payments'
    ];
    
    console.log('\nTest d\'accès aux routes principales:');
    testRoutes.forEach(route => {
        fetch(route, { method: 'HEAD' })
            .then(response => {
                const status = response.ok ? '✅' : '❌';
                console.log(`${status} ${route}: ${response.status}`);
            })
            .catch(error => {
                console.log(`❌ ${route}: Erreur`);
            });
    });
    
    console.log('=== FIN DU DIAGNOSTIC ===');
}

// Fonction pour corriger automatiquement les liens de reçus
function fixReceiptLinks() {
    console.log('🔧 Correction automatique des liens de reçus...');
    
    const receiptButtons = document.querySelectorAll('a[href*="receipt"]');
    let fixedCount = 0;
    
    receiptButtons.forEach(button => {
        const href = button.getAttribute('href');
        
        // Vérifier si c'est un lien vers un reçu caissier qui utilise l'ancienne route
        if (href.includes('/cashier/payments/') && href.includes('/receipt')) {
            // Extraire l'ID du paiement
            const paymentId = href.match(/\/payments\/(\d+)\/receipt/)?.[1];
            if (paymentId) {
                // Corriger le lien
                const newHref = `/accountant/cashier-receipt/${paymentId}`;
                button.setAttribute('href', newHref);
                fixedCount++;
                console.log(`✅ Lien corrigé: ${href} → ${newHref}`);
            }
        }
    });
    
    console.log(`🔧 ${fixedCount} lien(s) corrigé(s)`);
}

// Exporter les fonctions pour utilisation dans la console
window.receiptAccessTest = {
    test: testReceiptAccess,
    openReceipt: testOpenReceipt,
    diagnose: diagnoseUserPermissions,
    fix: fixReceiptLinks
};

// Exécuter le test automatiquement au chargement
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(testReceiptAccess, 1000);
});

// Raccourci clavier pour les tests (Ctrl+Shift+R)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'R') {
        e.preventDefault();
        testReceiptAccess();
    }
});

console.log('🔧 Script de test d\'accès aux reçus chargé');
console.log('💡 Utilisez receiptAccessTest.test() pour tester l\'accès');
console.log('💡 Utilisez receiptAccessTest.openReceipt(paymentId) pour tester un reçu spécifique');
console.log('💡 Utilisez receiptAccessTest.diagnose() pour diagnostiquer les permissions');
console.log('💡 Utilisez receiptAccessTest.fix() pour corriger les liens automatiquement');
console.log('💡 Ou appuyez sur Ctrl+Shift+R pour lancer le test');
