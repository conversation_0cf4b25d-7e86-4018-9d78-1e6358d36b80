<!DOCTYPE html>
<html>
<head>
    <title>Test Minimal GRADIS</title>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>
</head>
<body>
    <h1>Test Minimal des Graphiques</h1>
    
    <div style="margin: 20px 0;">
        <h3>Graphique 1</h3>
        <div id="revenueChart" style="height: 300px; border: 1px solid #ccc;"></div>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Graphique 2</h3>
        <div id="resourcesChart" style="height: 300px; border: 1px solid #ccc;"></div>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Graphique 3</h3>
        <div id="categoryRevenueChart" style="height: 300px; border: 1px solid #ccc;"></div>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Graphique 4</h3>
        <div id="cementOrdersChart" style="height: 300px; border: 1px solid #ccc;"></div>
    </div>
    
    <div id="status" style="padding: 20px; background: #f0f0f0; margin: 20px 0;">
        Status: Chargement...
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const status = document.getElementById("status");
            
            if (typeof ApexCharts === "undefined") {
                status.innerHTML = "❌ ApexCharts non chargé";
                status.style.background = "#ffcccc";
                return;
            }
            
            status.innerHTML = "✅ ApexCharts chargé - Création des graphiques...";
            status.style.background = "#ccffcc";
            
            const data = [10, 20, 15, 25, 30, 20];
            const categories = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"];
            
            // Graphique 1
            setTimeout(() => {
                try {
                    new ApexCharts(document.querySelector("#revenueChart"), {
                        series: [{ name: "Revenus", data: data }],
                        chart: { type: "area", height: 300 },
                        xaxis: { categories: categories }
                    }).render();
                    console.log("✅ Graphique 1 OK");
                } catch(e) { console.error("❌ Graphique 1:", e); }
            }, 100);
            
            // Graphique 2
            setTimeout(() => {
                try {
                    new ApexCharts(document.querySelector("#resourcesChart"), {
                        series: [{ name: "Ressources", data: data }],
                        chart: { type: "bar", height: 300 },
                        xaxis: { categories: categories }
                    }).render();
                    console.log("✅ Graphique 2 OK");
                } catch(e) { console.error("❌ Graphique 2:", e); }
            }, 200);
            
            // Graphique 3
            setTimeout(() => {
                try {
                    new ApexCharts(document.querySelector("#categoryRevenueChart"), {
                        series: [44, 55, 13, 43],
                        chart: { type: "donut", height: 300 },
                        labels: ["A", "B", "C", "D"]
                    }).render();
                    console.log("✅ Graphique 3 OK");
                } catch(e) { console.error("❌ Graphique 3:", e); }
            }, 300);
            
            // Graphique 4
            setTimeout(() => {
                try {
                    new ApexCharts(document.querySelector("#cementOrdersChart"), {
                        series: [{ name: "Ciment", data: data }],
                        chart: { type: "bar", height: 300 },
                        xaxis: { categories: categories }
                    }).render();
                    console.log("✅ Graphique 4 OK");
                } catch(e) { console.error("❌ Graphique 4:", e); }
            }, 400);
            
            setTimeout(() => {
                status.innerHTML = "🎉 Tous les graphiques créés! Vérifiez la console (F12) pour les détails.";
            }, 1000);
        });
    </script>
</body>
</html>