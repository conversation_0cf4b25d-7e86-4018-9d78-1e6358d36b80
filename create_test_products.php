<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "Création de produits de test...\n";
    
    // Créer une catégorie si elle n'existe pas
    $category = App\Models\Category::firstOrCreate(
        ['name' => 'Test'],
        ['description' => 'Catégorie de test']
    );
    
    // Créer quelques produits de test
    $products = [
        ['name' => 'Ciment 50kg', 'stock' => 0, 'price' => 4500],
        ['name' => 'Sable fin', 'stock' => 5, 'price' => 15000],
        ['name' => 'Gravier', 'stock' => 8, 'price' => 18000],
        ['name' => 'Fer 12mm', 'stock' => 150, 'price' => 650],
        ['name' => 'Pelle', 'stock' => 25, 'price' => 8500],
    ];
    
    foreach ($products as $productData) {
        $product = App\Models\Product::firstOrCreate(
            ['name' => $productData['name']],
            [
                'category_id' => $category->id,
                'stock_quantity' => $productData['stock'],
                'price' => $productData['price'],
                'unit' => 'unité',
                'is_active' => true,
                'description' => 'Produit de test'
            ]
        );
        
        echo "✅ Produit créé: {$product->name} (Stock: {$product->stock_quantity})\n";
    }
    
    echo "\n🎉 Produits de test créés avec succès!\n";
    echo "Maintenant, actualisez le dashboard: http://127.0.0.1:8000/admin/dashboard\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
