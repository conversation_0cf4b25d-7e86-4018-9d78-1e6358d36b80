/**
 * GRADIS Admin - Graphiques Optimisés
 * Chargement asynchrone et fluide des graphiques ApexCharts
 * Version: 1.0 - Performance optimisée
 */

console.log('📊 Script de graphiques optimisés chargé');

/**
 * Configuration optimisée pour les graphiques
 */
const CHART_CONFIG = {
    // Chargement asynchrone
    ASYNC_LOADING: true,
    LAZY_LOADING: true,
    
    // Délais optimisés
    RENDER_DELAY: 50,
    BATCH_DELAY: 100,
    
    // Performances
    ANIMATION_SPEED: 300,
    REDUCE_ANIMATIONS: true,
    
    // Gestion d'erreurs
    FALLBACK_ENABLED: true,
    RETRY_COUNT: 2
};

/**
 * Gestionnaire de graphiques optimisé
 */
class OptimizedChartManager {
    constructor() {
        this.charts = new Map();
        this.loadingQueue = [];
        this.isProcessing = false;
        this.observer = null;
        
        this.initIntersectionObserver();
    }
    
    /**
     * Initialise l'observation pour le lazy loading
     */
    initIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const chartId = entry.target.id;
                        if (chartId) {
                            this.loadChartWhenVisible(chartId);
                            this.observer.unobserve(entry.target);
                        }
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });
        }
    }
    
    /**
     * Charge un graphique de manière asynchrone et optimisée
     */
    async loadChart(containerId, options, priority = 'normal') {
        const container = document.getElementById(containerId);
        if (!container) {
            console.warn(`📊 Conteneur ${containerId} non trouvé`);
            return null;
        }
        
        // Vérifier si ApexCharts est disponible
        if (typeof ApexCharts === 'undefined') {
            console.error('📊 ApexCharts non disponible - Chargement du fallback');
            this.showFallback(container);
            return null;
        }
        
        // Ajouter à la queue de chargement
        const chartTask = {
            containerId,
            container,
            options: this.optimizeChartOptions(options),
            priority,
            retries: 0
        };
        
        if (priority === 'high') {
            this.loadingQueue.unshift(chartTask);
        } else {
            this.loadingQueue.push(chartTask);
        }
        
        // Démarrer le traitement si pas déjà en cours
        if (!this.isProcessing) {
            this.processQueue();
        }
        
        return chartTask;
    }
    
    /**
     * Optimise les options du graphique pour de meilleures performances
     */
    optimizeChartOptions(options) {
        const optimized = { ...options };
        
        // Optimisations de performance
        if (optimized.chart) {
            optimized.chart.animations = optimized.chart.animations || {};
            optimized.chart.animations.enabled = !CHART_CONFIG.REDUCE_ANIMATIONS;
            optimized.chart.animations.speed = CHART_CONFIG.ANIMATION_SPEED;
            
            // Désactiver les ombres pour de meilleures performances
            optimized.chart.dropShadow = { enabled: false };
            
            // Optimiser le rendu
            optimized.chart.redrawOnParentResize = false;
            optimized.chart.redrawOnWindowResize = true;
        }
        
        // Optimiser les tooltips
        if (optimized.tooltip) {
            optimized.tooltip.enabled = true;
            optimized.tooltip.shared = false; // Plus performant
        }
        
        // Optimiser les légendes
        if (optimized.legend) {
            optimized.legend.showForSingleSeries = false;
        }
        
        return optimized;
    }
    
    /**
     * Traite la queue de chargement de manière asynchrone
     */
    async processQueue() {
        if (this.isProcessing || this.loadingQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        console.log(`📊 Traitement de ${this.loadingQueue.length} graphique(s)...`);
        
        while (this.loadingQueue.length > 0) {
            const task = this.loadingQueue.shift();
            
            try {
                await this.renderChart(task);
                
                // Petit délai entre les graphiques pour éviter le blocage
                if (this.loadingQueue.length > 0) {
                    await this.delay(CHART_CONFIG.BATCH_DELAY);
                }
                
            } catch (error) {
                console.error(`📊 Erreur lors du rendu de ${task.containerId}:`, error);
                
                // Retry logic
                if (task.retries < CHART_CONFIG.RETRY_COUNT) {
                    task.retries++;
                    this.loadingQueue.push(task);
                    console.log(`📊 Retry ${task.retries}/${CHART_CONFIG.RETRY_COUNT} pour ${task.containerId}`);
                } else {
                    this.showFallback(task.container, error.message);
                }
            }
        }
        
        this.isProcessing = false;
        console.log('📊 Tous les graphiques traités');
    }
    
    /**
     * Rend un graphique de manière asynchrone
     */
    async renderChart(task) {
        const { containerId, container, options } = task;
        
        // Afficher un loader
        this.showLoader(container);
        
        // Créer le graphique
        const chart = new ApexCharts(container, options);
        
        // Rendu asynchrone
        await new Promise((resolve, reject) => {
            chart.render()
                .then(() => {
                    this.charts.set(containerId, chart);
                    console.log(`📊 ✅ Graphique ${containerId} rendu avec succès`);
                    resolve();
                })
                .catch(reject);
        });
        
        return chart;
    }
    
    /**
     * Charge un graphique quand il devient visible (lazy loading)
     */
    loadChartWhenVisible(containerId) {
        const container = document.getElementById(containerId);
        if (container && container.dataset.chartOptions) {
            try {
                const options = JSON.parse(container.dataset.chartOptions);
                this.loadChart(containerId, options, 'high');
            } catch (error) {
                console.error(`📊 Erreur parsing options pour ${containerId}:`, error);
            }
        }
    }
    
    /**
     * Active le lazy loading pour un conteneur
     */
    enableLazyLoading(containerId, options) {
        const container = document.getElementById(containerId);
        if (container && this.observer) {
            container.dataset.chartOptions = JSON.stringify(options);
            this.observer.observe(container);
            this.showPlaceholder(container);
        }
    }
    
    /**
     * Affiche un loader pendant le chargement
     */
    showLoader(container) {
        container.innerHTML = `
            <div class="chart-loader" style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 300px;
                flex-direction: column;
                color: #6b7280;
            ">
                <div style="
                    width: 40px;
                    height: 40px;
                    border: 3px solid #e5e7eb;
                    border-top: 3px solid #3b82f6;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin-bottom: 10px;
                "></div>
                <div>Chargement du graphique...</div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
    }
    
    /**
     * Affiche un placeholder pour le lazy loading
     */
    showPlaceholder(container) {
        container.innerHTML = `
            <div class="chart-placeholder" style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 300px;
                background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
                background-size: 200% 100%;
                animation: shimmer 2s infinite;
                border-radius: 8px;
                color: #9ca3af;
            ">
                📊 Graphique en attente de chargement
            </div>
            <style>
                @keyframes shimmer {
                    0% { background-position: -200% 0; }
                    100% { background-position: 200% 0; }
                }
            </style>
        `;
    }
    
    /**
     * Affiche un fallback en cas d'erreur
     */
    showFallback(container, errorMessage = '') {
        container.innerHTML = `
            <div class="chart-fallback" style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 300px;
                flex-direction: column;
                color: #ef4444;
                background: #fef2f2;
                border: 1px solid #fecaca;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
            ">
                <div style="font-size: 2rem; margin-bottom: 10px;">📊</div>
                <div style="font-weight: 600; margin-bottom: 5px;">Graphique indisponible</div>
                <div style="font-size: 0.875rem; color: #7f1d1d;">
                    ${errorMessage || 'Erreur de chargement'}
                </div>
            </div>
        `;
    }
    
    /**
     * Utilitaire pour créer des délais
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Détruit un graphique et libère la mémoire
     */
    destroyChart(containerId) {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.destroy();
            this.charts.delete(containerId);
            console.log(`📊 Graphique ${containerId} détruit`);
        }
    }
    
    /**
     * Détruit tous les graphiques
     */
    destroyAllCharts() {
        console.log('📊 Destruction de tous les graphiques...');
        this.charts.forEach((chart, id) => {
            chart.destroy();
        });
        this.charts.clear();
        this.loadingQueue = [];
        console.log('📊 Tous les graphiques détruits');
    }
}

// Initialiser le gestionnaire de graphiques
const chartManager = new OptimizedChartManager();

/**
 * API publique pour les graphiques optimisés
 */
window.OptimizedCharts = {
    // Charger un graphique
    load: (containerId, options, priority = 'normal') => {
        return chartManager.loadChart(containerId, options, priority);
    },
    
    // Activer le lazy loading
    lazy: (containerId, options) => {
        chartManager.enableLazyLoading(containerId, options);
    },
    
    // Détruire un graphique
    destroy: (containerId) => {
        chartManager.destroyChart(containerId);
    },
    
    // Détruire tous les graphiques
    destroyAll: () => {
        chartManager.destroyAllCharts();
    },
    
    // Accès au gestionnaire
    manager: chartManager
};

/**
 * Initialisation automatique
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('📊 Gestionnaire de graphiques optimisés initialisé');
    
    // Nettoyer les graphiques avant de quitter la page
    window.addEventListener('beforeunload', function() {
        chartManager.destroyAllCharts();
    });
    
    // Nettoyer quand la page devient invisible
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            // Pause les animations pour économiser les ressources
            chartManager.charts.forEach(chart => {
                if (chart.toggleDataPointSelection) {
                    // Pause les interactions
                }
            });
        }
    });
});

console.log('📊 Script de graphiques optimisés prêt');
