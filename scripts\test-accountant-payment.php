<?php

/**
 * Script de test pour la page de paiement du comptable
 * Vérifie que tous les assets se chargent correctement
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Créer l'application Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "🔍 Test de la page de paiement du comptable\n";
echo "==========================================\n\n";

try {
    // Simuler une requête vers la page de paiement de démonstration
    $request = Request::create('/demo/accountant/payment', 'GET');
    $response = $kernel->handle($request);
    
    echo "✅ Statut de la réponse: " . $response->getStatusCode() . "\n";
    
    // Vérifier le contenu de la réponse
    $content = $response->getContent();
    
    // Vérifications des assets
    $checks = [
        'jQuery CDN' => 'code.jquery.com/jquery-3.7.1.min.js',
        'Bootstrap CSS' => 'bootstrap@5.3.2/dist/css/bootstrap.min.css',
        'Bootstrap JS' => 'bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js',
        'SweetAlert2 CSS' => 'sweetalert2@11/dist/sweetalert2.min.css',
        'SweetAlert2 JS' => 'sweetalert2@11',
        'CSRF Token' => 'csrf-token',
        'Permissions Policy' => 'Permissions-Policy',
        'Script de diagnostic' => 'diagnostic.js'
    ];
    
    echo "\n📋 Vérification des assets:\n";
    echo "----------------------------\n";
    
    foreach ($checks as $name => $pattern) {
        if (strpos($content, $pattern) !== false) {
            echo "✅ $name: Présent\n";
        } else {
            echo "❌ $name: Manquant\n";
        }
    }
    
    // Vérifier les scripts jQuery spécifiques
    echo "\n🔧 Vérification des scripts jQuery:\n";
    echo "------------------------------------\n";
    
    $jqueryChecks = [
        'Fallback jQuery' => 'typeof jQuery === \'undefined\'',
        'CSRF Setup' => '$.ajaxSetup',
        'Payment card click' => '.payment-card',
        'SweetAlert usage' => 'Swal.fire'
    ];
    
    foreach ($jqueryChecks as $name => $pattern) {
        if (strpos($content, $pattern) !== false) {
            echo "✅ $name: Présent\n";
        } else {
            echo "❌ $name: Manquant\n";
        }
    }
    
    // Vérifier les headers de sécurité
    echo "\n🔒 Vérification des headers de sécurité:\n";
    echo "---------------------------------------\n";
    
    $headers = $response->headers->all();
    
    $securityHeaders = [
        'Content-Security-Policy' => 'CSP',
        'X-Content-Type-Options' => 'MIME Type Protection',
        'X-Frame-Options' => 'Clickjacking Protection',
        'Permissions-Policy' => 'Permissions Policy'
    ];
    
    foreach ($securityHeaders as $header => $description) {
        if (isset($headers[strtolower($header)])) {
            echo "✅ $description: Configuré\n";
            if ($header === 'Content-Security-Policy') {
                $csp = $headers[strtolower($header)][0];
                if (strpos($csp, 'unsafe-inline') !== false) {
                    echo "   ℹ️ CSP permet les scripts inline (bon pour le développement)\n";
                }
                if (strpos($csp, 'code.jquery.com') !== false) {
                    echo "   ✅ CSP autorise jQuery CDN\n";
                } else {
                    echo "   ⚠️ CSP pourrait bloquer jQuery CDN\n";
                }
            }
        } else {
            echo "❌ $description: Non configuré\n";
        }
    }
    
    // Analyser les erreurs potentielles
    echo "\n🚨 Analyse des erreurs potentielles:\n";
    echo "------------------------------------\n";
    
    $potentialIssues = [];
    
    // Vérifier si les assets sont bien référencés
    if (strpos($content, 'code.jquery.com') === false) {
        $potentialIssues[] = "jQuery CDN non référencé";
    }
    
    if (strpos($content, 'typeof jQuery === \'undefined\'') === false) {
        $potentialIssues[] = "Pas de fallback jQuery";
    }
    
    if (strpos($content, '$.ajaxSetup') === false) {
        $potentialIssues[] = "Configuration CSRF AJAX manquante";
    }
    
    if (empty($potentialIssues)) {
        echo "✅ Aucun problème détecté\n";
    } else {
        foreach ($potentialIssues as $issue) {
            echo "⚠️ $issue\n";
        }
    }
    
    echo "\n💡 Recommandations:\n";
    echo "-------------------\n";
    echo "1. Ouvrez la console du navigateur pour vérifier les erreurs JavaScript\n";
    echo "2. Testez la fonction diagnostic.testAjax() dans la console\n";
    echo "3. Vérifiez que les événements de clic fonctionnent sur les cartes de paiement\n";
    echo "4. Assurez-vous que SweetAlert2 s'affiche correctement\n";
    
    echo "\n🌐 URL de test: http://127.0.0.1:8000/demo/accountant/payment\n";
    
} catch (Exception $e) {
    echo "❌ Erreur lors du test: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . ":" . $e->getLine() . "\n";
    
    if ($e->getCode() === 404) {
        echo "\n💡 La route n'existe peut-être pas. Vérifiez:\n";
        echo "   - Que la route est définie dans routes/web.php\n";
        echo "   - Qu'un enregistrement avec l'ID 1 existe\n";
        echo "   - Que l'utilisateur a les permissions nécessaires\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Test terminé - " . date('Y-m-d H:i:s') . "\n";
