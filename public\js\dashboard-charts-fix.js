/**
 * GRADIS Dashboard - Correctif pour les Graphiques
 * Script spécialisé pour restaurer les graphiques ApexCharts
 * Version: 1.0 - Correctif graphiques
 */

console.log('📊 GRADIS Dashboard - Correctif graphiques chargé');

/**
 * Fonction principale pour restaurer les graphiques
 */
function restoreCharts() {
    console.log('⚡ Restauration des graphiques du dashboard...');
    
    // Attendre que ApexCharts soit disponible
    if (typeof ApexCharts === 'undefined') {
        console.log('⏳ Attente d\'ApexCharts...');
        setTimeout(restoreCharts, 500);
        return;
    }
    
    console.log('✅ ApexCharts disponible, initialisation des graphiques...');
    
    // Données de démonstration optimisées
    const chartData = {
        revenue: [45000, 52000, 48000, 61000, 55000, 67000, 73000, 69000, 78000, 85000, 92000, 88000],
        months: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
        resources: [12, 8, 15, 10, 6, 9],
        resourceLabels: ['Chauffeurs', 'Véhicules', 'Commandes', 'Livraisons', 'En attente', 'Terminées'],
        categories: [45, 30, 15, 10],
        categoryLabels: ['Ciment', 'Fer', 'Sable', 'Gravier'],
        cement: [120, 150, 135, 180, 200, 175, 190, 165, 210, 185, 225, 195]
    };
    
    // Configuration commune pour tous les graphiques
    const commonConfig = {
        chart: {
            toolbar: { show: false },
            animations: { enabled: true, speed: 800 },
            fontFamily: 'Inter, sans-serif'
        },
        dataLabels: { enabled: false },
        stroke: { curve: 'smooth', width: 2 },
        grid: {
            borderColor: '#e7e7e7',
            strokeDashArray: 5
        }
    };
    
    // Créer les graphiques un par un avec des délais
    setTimeout(() => createRevenueChart(chartData, commonConfig), 100);
    setTimeout(() => createResourcesChart(chartData, commonConfig), 300);
    setTimeout(() => createCategoryChart(chartData, commonConfig), 500);
    setTimeout(() => createCementChart(chartData, commonConfig), 700);
    
    console.log('🎯 Tous les graphiques programmés pour création');
}

/**
 * Créer le graphique des revenus
 */
function createRevenueChart(data, commonConfig) {
    const element = document.getElementById('revenueChart');
    if (!element) {
        console.warn('⚠️ Élément revenueChart non trouvé');
        return;
    }
    
    try {
        element.innerHTML = ''; // Nettoyer l'élément
        
        const options = {
            ...commonConfig,
            series: [{
                name: 'Revenus (FCFA)',
                data: data.revenue
            }],
            chart: {
                ...commonConfig.chart,
                type: 'area',
                height: 350
            },
            xaxis: {
                categories: data.months,
                labels: {
                    style: { colors: '#8e8da4' }
                }
            },
            yaxis: {
                labels: {
                    formatter: function(val) {
                        return new Intl.NumberFormat('fr-FR').format(val);
                    },
                    style: { colors: '#8e8da4' }
                }
            },
            colors: ['#007bff'],
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.3,
                    stops: [0, 100]
                }
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return new Intl.NumberFormat('fr-FR').format(val) + ' FCFA';
                    }
                }
            }
        };
        
        const chart = new ApexCharts(element, options);
        chart.render();
        console.log('✅ Graphique des revenus créé');
        
    } catch (error) {
        console.error('❌ Erreur création graphique revenus:', error);
    }
}

/**
 * Créer le graphique des ressources
 */
function createResourcesChart(data, commonConfig) {
    const element = document.getElementById('resourcesChart');
    if (!element) {
        console.warn('⚠️ Élément resourcesChart non trouvé');
        return;
    }
    
    try {
        element.innerHTML = '';
        
        const options = {
            ...commonConfig,
            series: [{
                name: 'Quantité',
                data: data.resources
            }],
            chart: {
                ...commonConfig.chart,
                type: 'bar',
                height: 350
            },
            xaxis: {
                categories: data.resourceLabels,
                labels: {
                    style: { colors: '#8e8da4' }
                }
            },
            yaxis: {
                labels: {
                    style: { colors: '#8e8da4' }
                }
            },
            colors: ['#28a745'],
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    horizontal: false
                }
            }
        };
        
        const chart = new ApexCharts(element, options);
        chart.render();
        console.log('✅ Graphique des ressources créé');
        
    } catch (error) {
        console.error('❌ Erreur création graphique ressources:', error);
    }
}

/**
 * Créer le graphique des catégories
 */
function createCategoryChart(data, commonConfig) {
    const element = document.getElementById('categoryRevenueChart');
    if (!element) {
        console.warn('⚠️ Élément categoryRevenueChart non trouvé');
        return;
    }
    
    try {
        element.innerHTML = '';
        
        const options = {
            series: data.categories,
            chart: {
                type: 'donut',
                height: 300,
                animations: { enabled: true, speed: 800 }
            },
            labels: data.categoryLabels,
            colors: ['#007bff', '#28a745', '#ffc107', '#dc3545'],
            legend: {
                position: 'bottom',
                horizontalAlign: 'center'
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%'
                    }
                }
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + '%';
                    }
                }
            }
        };
        
        const chart = new ApexCharts(element, options);
        chart.render();
        console.log('✅ Graphique des catégories créé');
        
    } catch (error) {
        console.error('❌ Erreur création graphique catégories:', error);
    }
}

/**
 * Créer le graphique du ciment
 */
function createCementChart(data, commonConfig) {
    const element = document.getElementById('cementOrdersChart');
    if (!element) {
        console.warn('⚠️ Élément cementOrdersChart non trouvé');
        return;
    }
    
    try {
        element.innerHTML = '';
        
        const options = {
            ...commonConfig,
            series: [{
                name: 'Tonnage',
                data: data.cement
            }],
            chart: {
                ...commonConfig.chart,
                type: 'bar',
                height: 300
            },
            xaxis: {
                categories: data.months,
                labels: {
                    style: { colors: '#8e8da4' }
                }
            },
            yaxis: {
                labels: {
                    formatter: function(val) {
                        return val + 'T';
                    },
                    style: { colors: '#8e8da4' }
                }
            },
            colors: ['#17a2b8'],
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    horizontal: false
                }
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val + ' tonnes';
                    }
                }
            }
        };
        
        const chart = new ApexCharts(element, options);
        chart.render();
        console.log('✅ Graphique du ciment créé');
        
    } catch (error) {
        console.error('❌ Erreur création graphique ciment:', error);
    }
}

/**
 * Fonction de diagnostic pour les graphiques
 */
function diagnoseCharts() {
    console.log('🔍 Diagnostic des graphiques...');
    
    const chartElements = [
        'revenueChart',
        'resourcesChart', 
        'categoryRevenueChart',
        'cementOrdersChart'
    ];
    
    chartElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            console.log(`✅ Élément ${id} trouvé`);
        } else {
            console.warn(`⚠️ Élément ${id} manquant`);
        }
    });
    
    if (typeof ApexCharts !== 'undefined') {
        console.log('✅ ApexCharts disponible');
    } else {
        console.warn('⚠️ ApexCharts non disponible');
    }
}

/**
 * Initialisation automatique
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initialisation du correctif graphiques...');
    
    // Diagnostic initial
    setTimeout(diagnoseCharts, 100);
    
    // Restauration des graphiques
    setTimeout(restoreCharts, 500);
});

// Initialisation immédiate si le DOM est déjà prêt
if (document.readyState !== 'loading') {
    setTimeout(diagnoseCharts, 100);
    setTimeout(restoreCharts, 500);
}

// Export global pour utilisation manuelle
window.DashboardChartsFix = {
    restore: restoreCharts,
    diagnose: diagnoseCharts
};

console.log('📊 Script de correctif graphiques prêt');
