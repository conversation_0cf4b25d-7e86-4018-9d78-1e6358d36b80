<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\CheckCategories::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Nettoyage quotidien des logs anciens (garder 30 jours)
        $schedule->command('log:clear --days=30')
                 ->daily()
                 ->at('02:00')
                 ->description('Nettoyer les anciens logs');

        // Nettoyage des sessions expirées
        $schedule->command('session:gc')
                 ->hourly()
                 ->description('Nettoyer les sessions expirées');

        // Sauvegarde quotidienne de la base de données
        $schedule->exec('mysqldump -u ' . config('database.connections.mysql.username') .
                       ' -p' . config('database.connections.mysql.password') .
                       ' ' . config('database.connections.mysql.database') .
                       ' > ' . storage_path('backups/db_backup_' . date('Y-m-d') . '.sql'))
                 ->daily()
                 ->at('03:00')
                 ->description('Sauvegarde quotidienne de la base de données');

        // Nettoyage des fichiers temporaires
        $schedule->call(function () {
            $assetService = app(\App\Services\AssetOptimizationService::class);
            $assetService->cleanupTempFiles(24);
        })->daily()
          ->at('04:00')
          ->description('Nettoyer les fichiers temporaires');

        // Optimisation des images (hebdomadaire)
        $schedule->call(function () {
            $assetService = app(\App\Services\AssetOptimizationService::class);
            $assetService->optimizeAllAssets();
        })->weekly()
          ->sundays()
          ->at('05:00')
          ->description('Optimisation hebdomadaire des images');

        // Nettoyage du cache des requêtes (quotidien)
        $schedule->call(function () {
            $dbService = app(\App\Services\DatabaseOptimizationService::class);
            $dbService->clearQueryCache();
        })->daily()
          ->at('01:00')
          ->description('Nettoyer le cache des requêtes');

        // Vérification de l'état de l'application (toutes les 5 minutes)
        $schedule->call(function () {
            // Vérifier la connectivité de la base de données
            try {
                \DB::connection()->getPdo();
            } catch (\Exception $e) {
                \Log::error('Database connection failed: ' . $e->getMessage());
            }

            // Vérifier l'espace disque
            $freeSpace = disk_free_space(base_path());
            $totalSpace = disk_total_space(base_path());
            $usedPercentage = (($totalSpace - $freeSpace) / $totalSpace) * 100;

            if ($usedPercentage > 90) {
                \Log::warning('Low disk space: ' . $usedPercentage . '% used');
            }
        })->everyFiveMinutes()
          ->description('Vérification de l\'état de l\'application');

        // Nettoyage des notifications anciennes (mensuel)
        $schedule->call(function () {
            \DB::table('notifications')
                ->where('created_at', '<', now()->subMonths(3))
                ->delete();
        })->monthly()
          ->description('Nettoyer les anciennes notifications');

        // Génération de rapports mensuels
        $schedule->call(function () {
            // Générer des statistiques mensuelles
            $stats = [
                'orders_count' => \DB::table('cement_orders')->whereMonth('created_at', now()->month)->count(),
                'revenue' => \DB::table('cement_orders')->whereMonth('created_at', now()->month)->sum('total_amount'),
                'customers_count' => \DB::table('users')->where('role', 'customer')->whereMonth('created_at', now()->month)->count(),
            ];

            \Log::info('Monthly stats generated', $stats);
        })->monthlyOn(1, '06:00')
          ->description('Génération de rapports mensuels');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
