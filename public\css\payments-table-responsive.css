/* 
 * CSS pour l'affichage responsif du tableau des paiements
 * Assure que toutes les colonnes sont visibles
 */

/* Conteneur du tableau des paiements */
.payments-table-container {
    position: relative;
    margin-bottom: 1.5rem;
}

/* Indicateur de défilement */
.scroll-indicator {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border: 1px solid #2196f3;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #1565c0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.scroll-indicator i {
    color: #1976d2;
    font-size: 1rem;
}

/* Styles pour le tableau des paiements */
.payments-table {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
}

.payments-table::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 100%;
    background: linear-gradient(to left, rgba(255, 255, 255, 0.9), transparent);
    pointer-events: none;
    z-index: 1;
}

.payments-table .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #1976d2 #f5f5f5;
}

.payments-table .table-responsive::-webkit-scrollbar {
    height: 8px;
}

.payments-table .table-responsive::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
}

.payments-table .table-responsive::-webkit-scrollbar-thumb {
    background: #1976d2;
    border-radius: 4px;
}

.payments-table .table-responsive::-webkit-scrollbar-thumb:hover {
    background: #1565c0;
}

/* Tableau principal */
.payments-table table {
    min-width: 1300px; /* Largeur minimale pour toutes les colonnes */
    width: 100%;
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
}

/* En-têtes du tableau */
.payments-table thead th {
    background: linear-gradient(135deg, #1976d2, #1565c0);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
    text-align: left;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 2;
    border: none;
}

.payments-table thead th:first-child {
    border-top-left-radius: 12px;
}

.payments-table thead th:last-child {
    border-top-right-radius: 12px;
}

/* Cellules du tableau */
.payments-table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #e0e0e0;
    white-space: nowrap;
    font-size: 0.875rem;
}

.payments-table tbody tr:last-child td {
    border-bottom: none;
}

.payments-table tbody tr:hover {
    background-color: rgba(25, 118, 210, 0.04);
}

/* Largeurs spécifiques pour les colonnes */
.payments-table th:nth-child(1), .payments-table td:nth-child(1) { 
    min-width: 130px; 
    width: 130px;
} /* Date */

.payments-table th:nth-child(2), .payments-table td:nth-child(2) { 
    min-width: 150px; 
    width: 150px;
} /* Réf. Paiement */

.payments-table th:nth-child(3), .payments-table td:nth-child(3) { 
    min-width: 120px; 
    width: 120px;
    text-align: right;
} /* Montant */

.payments-table th:nth-child(4), .payments-table td:nth-child(4) { 
    min-width: 120px; 
    width: 120px;
} /* Méthode */

.payments-table th:nth-child(5), .payments-table td:nth-child(5) { 
    min-width: 140px; 
    width: 140px;
} /* Réf. Transaction */

.payments-table th:nth-child(6), .payments-table td:nth-child(6) { 
    min-width: 110px; 
    width: 110px;
} /* Agent */

.payments-table th:nth-child(7), .payments-table td:nth-child(7) { 
    min-width: 100px; 
    width: 100px;
} /* Poste */

.payments-table th:nth-child(8), .payments-table td:nth-child(8) { 
    min-width: 120px; 
    width: 120px;
} /* Statut */

.payments-table th:nth-child(9), .payments-table td:nth-child(9) { 
    min-width: 180px; 
    width: 180px;
    white-space: normal;
    word-wrap: break-word;
} /* Commentaire */

.payments-table th:nth-child(10), .payments-table td:nth-child(10) { 
    min-width: 120px; 
    width: 120px;
} /* Actions */

/* Styles pour les badges */
.payments-table .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
}

/* Responsive pour tablettes */
@media (max-width: 992px) {
    .payments-table table {
        min-width: 1200px;
    }
    
    .payments-table th, .payments-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Responsive pour mobiles */
@media (max-width: 768px) {
    .payments-table-container {
        margin: 0 -1rem;
    }
    
    .payments-table {
        border-radius: 0;
        box-shadow: none;
        border-top: 1px solid #e0e0e0;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .payments-table table {
        min-width: 1100px;
    }
    
    .payments-table th, .payments-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .scroll-indicator {
        margin: 0 1rem 1rem 1rem;
        font-size: 0.8rem;
    }
}

/* Animation pour attirer l'attention sur le défilement */
@keyframes scrollHint {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(5px); }
}

.scroll-indicator.animate {
    animation: scrollHint 2s ease-in-out infinite;
}

/* Amélioration de l'accessibilité */
.payments-table table:focus {
    outline: 2px solid #1976d2;
    outline-offset: 2px;
}

/* Indicateur de position de défilement */
.scroll-position-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: #f5f5f5;
    z-index: 1;
}

.scroll-position-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #1976d2;
    transition: width 0.2s ease;
    width: var(--scroll-progress, 0%);
}
