<?php

/**
 * Script de test rapide pour vérifier le dashboard admin
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Cache;

class DashboardTest
{
    private $app;
    
    public function __construct()
    {
        $this->initializeLaravel();
    }
    
    /**
     * Initialise l'application Laravel
     */
    private function initializeLaravel()
    {
        $this->app = require_once __DIR__ . '/../bootstrap/app.php';
        $this->app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    }
    
    /**
     * Teste les fonctionnalités du dashboard
     */
    public function run()
    {
        $this->log("🔍 Test du Dashboard Admin...");
        
        try {
            // Test 1: Vérifier que Cache fonctionne
            $this->testCache();
            
            // Test 2: Vérifier les modèles
            $this->testModels();
            
            // Test 3: Tester le cache des statistiques
            $this->testDashboardCache();
            
            $this->log("🎉 Tous les tests sont passés avec succès!");
            $this->log("💡 Votre dashboard admin devrait maintenant fonctionner correctement.");
            
        } catch (Exception $e) {
            $this->error("❌ Erreur lors du test: " . $e->getMessage());
            $this->error("📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine());
        }
    }
    
    /**
     * Teste le système de cache
     */
    private function testCache()
    {
        $this->log("🔄 Test du système de cache...");
        
        // Test d'écriture et lecture
        $testKey = 'test_dashboard_' . time();
        $testValue = 'test_value_' . rand(1000, 9999);
        
        Cache::put($testKey, $testValue, 60);
        $retrievedValue = Cache::get($testKey);
        
        if ($retrievedValue === $testValue) {
            $this->log("✅ Cache fonctionne correctement");
        } else {
            throw new Exception("Le cache ne fonctionne pas correctement");
        }
        
        // Nettoyer
        Cache::forget($testKey);
    }
    
    /**
     * Teste les modèles utilisés par le dashboard
     */
    private function testModels()
    {
        $this->log("🔄 Test des modèles...");
        
        $models = [
            'App\Models\User',
            'App\Models\Product', 
            'App\Models\Order',
            'App\Models\Supply',
            'App\Models\Driver',
            'App\Models\Truck'
        ];
        
        foreach ($models as $model) {
            if (class_exists($model)) {
                $this->log("✅ Modèle {$model} disponible");
                
                // Test de base - compter les enregistrements
                try {
                    $count = $model::count();
                    $this->log("   📊 {$count} enregistrements trouvés");
                } catch (Exception $e) {
                    $this->log("   ⚠️  Erreur lors du comptage: " . $e->getMessage());
                }
            } else {
                $this->log("⚠️  Modèle {$model} non trouvé");
            }
        }
    }
    
    /**
     * Teste le cache des statistiques du dashboard
     */
    private function testDashboardCache()
    {
        $this->log("🔄 Test du cache des statistiques...");
        
        // Simuler le cache des statistiques comme dans le DashboardController
        $cacheKey = 'admin_dashboard_stats';
        
        // Vider le cache existant
        Cache::forget($cacheKey);
        
        // Tester Cache::remember
        $stats = Cache::remember($cacheKey, 300, function () {
            $this->log("   🔄 Génération des statistiques (première fois)...");
            
            return [
                'users_count' => \App\Models\User::count(),
                'products_count' => \App\Models\Product::count(),
                'test_timestamp' => time()
            ];
        });
        
        $this->log("✅ Statistiques générées et mises en cache");
        $this->log("   👥 Utilisateurs: " . $stats['users_count']);
        $this->log("   📦 Produits: " . $stats['products_count']);
        
        // Tester que le cache fonctionne (deuxième appel)
        $stats2 = Cache::remember($cacheKey, 300, function () {
            $this->log("   ⚠️  Cette fonction ne devrait pas être appelée (cache hit attendu)");
            return ['error' => 'cache_miss'];
        });
        
        if ($stats['test_timestamp'] === $stats2['test_timestamp']) {
            $this->log("✅ Cache hit confirmé - les statistiques sont bien mises en cache");
        } else {
            throw new Exception("Le cache ne fonctionne pas - cache miss inattendu");
        }
        
        // Nettoyer
        Cache::forget($cacheKey);
    }
    
    /**
     * Log un message
     */
    private function log($message)
    {
        echo $message . "\n";
    }
    
    /**
     * Log une erreur
     */
    private function error($message)
    {
        echo $message . "\n";
    }
}

// Exécuter le test
if (php_sapi_name() === 'cli') {
    $tester = new DashboardTest();
    $tester->run();
} else {
    echo "Ce script doit être exécuté en ligne de commande.\n";
    exit(1);
}
