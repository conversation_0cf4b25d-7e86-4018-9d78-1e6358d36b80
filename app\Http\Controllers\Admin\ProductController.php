<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\IronSpecification;
use App\Models\Region;
use App\Models\City;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class ProductController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    public function index()
    {
        $products = Product::with(['category', 'prices', 'ironSpecification'])
            ->latest()
            ->paginate(10);

        return view('admin.products.index', compact('products'));
    }

    public function create()
    {
        $categories = Category::all();
        $regions = Region::with('cities')->get();
        return view('admin.products.create', compact('categories', 'regions'));
    }

    public function store(Request $request)
    {
        try {
            DB::beginTransaction();

            // Récupérer la catégorie
            $category = Category::findOrFail($request->category_id);
            $categoryName = strtolower($category->name);

            // Vérifier le type de produit
            $isCement = $this->isCategoryExactly($categoryName, 'ciment');
            $isIron = $this->isCategoryExactly($categoryName, 'fer');

            // Validation de base pour tous les produits
            $baseValidation = [
                'name' => 'required|string|max:255',
                'category_id' => 'required|exists:categories,id',
                'unit' => ['required', 'string', 'in:Tonne,Kg,Barre,Unité,Pièce,Mètre cube,Mètre carré'],
                'stock_quantity' => 'required|integer|min:0',
                'is_active' => 'boolean'
            ];

            // Ajouter des règles de validation spécifiques selon la catégorie
            if ($isCement) {
                // Pour le ciment, pas de prix de base car géré par région
                $validated = $request->validate($baseValidation);
                $this->validateCementProduct($request);
                $price = 0; // Le prix est géré par région
            } elseif ($isIron) {
                // Pour le fer, le prix de base est le prix unitaire
                $validated = $request->validate($baseValidation);
                $this->validateIronProduct($request);
                $price = $request->iron_unit_price;
            } else {
                // Pour les autres produits, validation standard avec prix
                $baseValidation['base_price'] = 'required|numeric|min:0';
                $validated = $request->validate($baseValidation);
                $price = $request->base_price;
            }

            // Générer le slug
            $slug = Str::slug($request->name);
            $baseSlug = $slug;
            $counter = 1;

            while (Product::where('slug', $slug)->exists()) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }

            // Slug généré

            // Générer le code du produit
            $code = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $request->name), 0, 3));
            $baseCode = $code;
            $codeCounter = 1;

            while (Product::where('code', $code)->exists()) {
                $code = $this->generateUniqueCode();
            }

            // Créer le produit de base
            $product = Product::create([
                'name' => $validated['name'],
                'slug' => $slug,
                'code' => $code,
                'description' => $validated['description'] ?? null,
                'category_id' => $validated['category_id'],
                'unit' => $validated['unit'],
                'is_active' => $validated['is_active'] ?? true,
            ]);

            // Traitement spécifique selon la catégorie
            if ($this->isCategoryExactly($product->category->name, 'ciment')) {
                $this->handleCementPrices($product, $validated);
            } elseif ($this->isCategoryExactly($product->category->name, 'fer')) {
                $this->handleIronSpecifications($product, $validated);
            }

            DB::commit();

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Produit créé avec succès',
                    'product' => $product->load('category')
                ], 201);
            }
            return redirect()->route('admin.products.index')
                ->with('success', [
                    'title' => 'Produit créé avec succès !',
                    'message' => "Le produit {$product->name} a été ajouté à votre catalogue.",
                    'icon' => 'success'
                ]);

        } catch (ValidationException $e) {
            DB::rollBack();
            // Erreur de validation

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'errors' => $e->errors()
                ], 422);
            }
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Une erreur est survenue lors de la création du produit: ' . $e->getMessage()
                ], 500);
            }
            return redirect()->back()
                ->with('error', 'Une erreur est survenue lors de la création du produit: ' . $e->getMessage())
                ->withInput();
        }
    }

    private function validateIronProduct(Request $request)
    {
        $request->validate([
            'iron_diameter' => 'required|numeric|in:6,8,10,12,14,16',
            'iron_length' => 'required|numeric|min:1',
            'iron_unit_weight' => 'required|numeric|min:0',
            'iron_unit_price' => 'required|numeric|min:0',
            'iron_ton_price' => 'required|numeric|min:0',
        ], [
            'iron_diameter.required' => 'Le diamètre est requis.',
            'iron_diameter.numeric' => 'Le diamètre doit être un nombre.',
            'iron_diameter.in' => 'Le diamètre sélectionné n\'est pas valide.',
            'iron_length.required' => 'La longueur est requise.',
            'iron_length.numeric' => 'La longueur doit être un nombre.',
            'iron_length.min' => 'La longueur doit être au moins :min.',
            'iron_unit_weight.required' => 'Le poids par unité est requis.',
            'iron_unit_weight.numeric' => 'Le poids par unité doit être un nombre.',
            'iron_unit_weight.min' => 'Le poids par unité doit être au moins :min.',
            'iron_unit_price.required' => 'Le prix unitaire est requis.',
            'iron_unit_price.numeric' => 'Le prix unitaire doit être un nombre.',
            'iron_unit_price.min' => 'Le prix unitaire doit être au moins :min.',
            'iron_ton_price.required' => 'Le prix par tonne est requis.',
            'iron_ton_price.numeric' => 'Le prix par tonne doit être un nombre.',
            'iron_ton_price.min' => 'Le prix par tonne doit être au moins :min.',
        ]);

        // Calculer automatiquement les unités par tonne basé sur le diamètre
        $diameterSpecs = [
            6 => 750,
            8 => 422,
            10 => 270,
            12 => 188,
            14 => 138,
            16 => 106
        ];

        $diameter = (int)$request->iron_diameter;
        if (!isset($diameterSpecs[$diameter])) {
            throw ValidationException::withMessages([
                'iron_diameter' => ['Diamètre non supporté.']
            ]);
        }
    }

    private function storeIronSpecifications(Product $product, Request $request)
    {
        // Calculer automatiquement les unités par tonne basé sur le diamètre
        $diameterSpecs = [
            6 => 750,
            8 => 422,
            10 => 270,
            12 => 188,
            14 => 138,
            16 => 106
        ];

        $diameter = (int)$request->iron_diameter;
        $unitsPerTon = $diameterSpecs[$diameter];

        $ironSpec = new IronSpecification([
            'product_id' => $product->id,
            'diameter' => $request->iron_diameter,
            'length' => $request->iron_length,
            'units_per_ton' => $unitsPerTon,
            'weight_per_unit' => $request->iron_unit_weight,
            'unit_price' => $request->iron_unit_price,
            'ton_price' => $request->iron_ton_price
        ]);

        $ironSpec->save();

        return $ironSpec;
    }

    private function validateCementProduct(Request $request)
    {
        // Validation du produit ciment

        // Si des villes sont sélectionnées, valider leurs prix
        if ($request->has('cities') && is_array($request->cities)) {
            foreach ($request->cities as $cityId => $cityData) {
                $request->validate([
                    "cities.{$cityId}.price" => 'required|numeric|min:0',
                ], [
                    "cities.{$cityId}.price.required" => 'Le prix est requis pour les villes sélectionnées.',
                    "cities.{$cityId}.price.numeric" => 'Le prix doit être un nombre.',
                    "cities.{$cityId}.price.min" => 'Le prix doit être supérieur ou égal à 0.',
                ]);
            }
        }
    }

    private function processCementPrices(Product $product, Request $request)
    {
        try {
            $pricesProcessed = false;

            // Format 1: données envoyées avec 'cities' (création)
            if ($request->has('cities') && is_array($request->cities)) {
                foreach ($request->cities as $cityId => $cityData) {
                    if (isset($cityData['price']) && is_numeric($cityData['price'])) {
                        // Création du prix pour la ville

                        $product->prices()->create([
                            'city_id' => $cityId,
                            'price' => $cityData['price']
                        ]);
                        $pricesProcessed = true;
                    } else {
                        // Données de prix invalides
                    }
                }
            }

            // Format 2: données envoyées avec 'prices' (modification)
            if ($request->has('prices') && is_array($request->prices)) {
                foreach ($request->prices as $cityId => $price) {
                    if (is_numeric($price) && $price > 0) {
                        // Création du prix

                        $product->prices()->create([
                            'city_id' => $cityId,
                            'price' => $price
                        ]);
                        $pricesProcessed = true;
                    } else {
                        // Prix invalide
                    }
                }
            }

            if (!$pricesProcessed) {
                // Aucune ville sélectionnée
            }

            // Prix du ciment traités
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function show(Product $product)
    {
        // Charger toutes les relations nécessaires pour l'affichage
        $product->load([
            'category',
            'prices.city.region',
            'ironSpecification',
            'sales' => function($query) {
                $query->latest()->limit(10);
            }
        ]);

        // Calculer les statistiques du produit
        $stats = [
            'total_sales' => $product->sales()->count(),
            'total_revenue' => $product->sales()->sum('total_amount'),
            'average_sale_price' => $product->sales()->avg('unit_price'),
            'last_sale_date' => $product->sales()->latest()->first()?->created_at,
            'stock_value' => $product->stock_quantity * ($product->price ?? 0)
        ];

        return view('admin.products.show', compact('product', 'stats'));
    }

    public function edit(Product $product)
    {
        // Charger les relations nécessaires
        $product->load(['category', 'prices.city.region', 'ironSpecification']);

        // Récupérer toutes les catégories et régions
        $categories = Category::all();
        $regions = Region::with('cities')->get();

        // Préparer les données pour le ciment
        $selectedCities = [];
        if (strtolower($product->category->name) === 'ciment') {
            foreach ($product->prices as $price) {
                $selectedCities[$price->city_id] = [
                    'price' => $price->price,
                    'region_id' => $price->city->region_id,
                    'city_name' => $price->city->name
                ];
            }
        }

        // Préparer les unités selon la catégorie
        $units = $this->getUnitsForCategory($product->category->name);

        return view('admin.products.edit', compact('product', 'categories', 'regions', 'selectedCities', 'units'));
    }

    private function getUnitsForCategory($categoryName)
    {
        $categoryName = strtolower($categoryName);
        return match($categoryName) {
            'ciment' => ['Tonne', 'Kg'],
            'fer' => ['Barre'],
            'fil de fer' => ['Kg'],
            default => ['Unité', 'Pièce', 'Mètre cube', 'Mètre carré']
        };
    }

    public function update(Request $request, Product $product)
    {
        try {
            DB::beginTransaction();

            // Récupérer la catégorie
            $category = Category::findOrFail($request->category_id);
            $categoryName = strtolower($category->name);

            // Vérifier le type de produit
            $isCement = $this->isCategoryExactly($categoryName, 'ciment');
            $isIron = $this->isCategoryExactly($categoryName, 'fer');

            // Validation de base pour tous les produits
            $baseValidation = [
                'name' => 'required|string|max:255',
                'category_id' => 'required|exists:categories,id',
                'unit' => ['required', 'string', 'in:Tonne,Kg,Barre,Unité,Pièce,Mètre cube,Mètre carré'],
                'stock_quantity' => 'required|integer|min:0',
            ];

            // Ajouter la validation du prix selon le type de produit
            if (!$isCement) {
                $baseValidation['price'] = 'required|numeric|min:0';
            }

            $validated = $request->validate($baseValidation);

            // Valider selon le type de produit
            if ($isCement) {
                $this->validateCementProduct($request);
                $price = 0; // Le prix est géré par ville
            } elseif ($isIron) {
                $this->validateIronProduct($request);
                $price = $request->iron_unit_price; // Utiliser le bon nom de champ
            } else {
                $price = $request->price;
            }

            // Mettre à jour le produit
            $product->update([
                'name' => $request->name,
                'category_id' => $request->category_id,
                'unit' => $request->unit,
                'stock_quantity' => $request->stock_quantity,
                'price' => $price,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Traiter les spécifications spéciales selon le type de produit
            if ($isCement) {
                // Supprimer les anciens prix
                $product->prices()->delete();
                // Ajouter les nouveaux prix
                $this->processCementPrices($product, $request);
            } elseif ($isIron) {
                // Supprimer les anciennes spécifications
                $product->ironSpecification()->delete();
                // Ajouter les nouvelles spécifications
                $this->storeIronSpecifications($product, $request);
            }

            DB::commit();

            return redirect()->route('admin.products.index')
                ->with('success', [
                    'title' => 'Produit mis à jour avec succès !',
                    'message' => "Le produit {$product->name} a été mis à jour avec succès.",
                    'icon' => 'success'
                ]);

        } catch (ValidationException $e) {
            DB::rollBack();
            // Erreur
            
            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();

        } catch (\Exception $e) {
            DB::rollBack();
            // Erreur
            
            return redirect()->back()
                ->with('error', 'Une erreur est survenue lors de la mise à jour du produit')
                ->withInput();
        }
    }

    public function destroy(Product $product)
    {
        try {
            DB::beginTransaction();

            // Supprimer les prix associés pour le ciment
            if ($this->isCategoryExactly($product->category->name, 'ciment')) {
                $product->prices()->delete();
            }

            // Supprimer les spécifications pour le fer
            if ($this->isCategoryExactly($product->category->name, 'fer')) {
                $product->ironSpecification()->delete();
            }

            // Supprimer le produit
            $product->delete();

            DB::commit();

            return redirect()->route('admin.products.index')
                ->with('success', [
                    'title' => 'Produit supprimé avec succès !',
                    'message' => "Le produit {$product->name} a été supprimé avec succès.",
                    'icon' => 'success'
                ]);

        } catch (\Exception $e) {
            DB::rollBack();
            // Erreur

            return redirect()->back()
                ->with('error', 'Une erreur est survenue lors de la suppression du produit');
        }
    }

    public function getCities(Region $region)
    {
        try {
            $cities = $region->cities()->select('id', 'name')->get();
            return response()->json($cities);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Impossible de charger les villes'], 500);
        }
    }

    public function getCitiesByRegionId($regionId)
    {
        $region = Region::findOrFail($regionId);
        return response()->json($this->getCities($region));
    }

    private function isCategoryExactly($categoryName, $target)
    {
        return trim(strtolower($categoryName)) === trim(strtolower($target));
    }
}
