<?php

/**
 * Script de vérification de la configuration pour Gradis
 * Vérifie que tous les paramètres sont correctement configurés
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class ConfigVerification
{
    private $app;
    private $errors = [];
    private $warnings = [];
    private $success = [];
    
    public function __construct()
    {
        $this->initializeLaravel();
    }
    
    /**
     * Initialise l'application Laravel
     */
    private function initializeLaravel()
    {
        $this->app = require_once __DIR__ . '/../bootstrap/app.php';
        $this->app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    }
    
    /**
     * Exécute toutes les vérifications
     */
    public function run()
    {
        $this->log("🔍 Vérification de la configuration Gradis pour gradis-togo.com");
        $this->log("=" . str_repeat("=", 60));
        
        $this->checkEnvironment();
        $this->checkDatabase();
        $this->checkCache();
        $this->checkMail();
        $this->checkSecurity();
        $this->checkFiles();
        
        $this->displayResults();
    }
    
    /**
     * Vérifie l'environnement général
     */
    private function checkEnvironment()
    {
        $this->log("\n📋 Vérification de l'environnement...");
        
        // APP_URL
        $appUrl = config('app.url');
        if ($appUrl === 'https://gradis-togo.com') {
            $this->success[] = "✅ APP_URL correctement configuré: {$appUrl}";
        } else {
            $this->errors[] = "❌ APP_URL incorrect: {$appUrl} (attendu: https://gradis-togo.com)";
        }
        
        // APP_ENV
        $appEnv = config('app.env');
        if ($appEnv === 'production') {
            $this->success[] = "✅ APP_ENV en mode production";
        } else {
            $this->warnings[] = "⚠️  APP_ENV n'est pas en production: {$appEnv}";
        }
        
        // APP_DEBUG
        $appDebug = config('app.debug');
        if (!$appDebug) {
            $this->success[] = "✅ APP_DEBUG désactivé (sécurisé)";
        } else {
            $this->errors[] = "❌ APP_DEBUG activé (dangereux en production)";
        }
        
        // APP_KEY
        $appKey = config('app.key');
        if (!empty($appKey)) {
            $this->success[] = "✅ APP_KEY définie";
        } else {
            $this->errors[] = "❌ APP_KEY non définie (exécuter: php artisan key:generate)";
        }
        
        // Timezone
        $timezone = config('app.timezone');
        if ($timezone === 'Africa/Lome') {
            $this->success[] = "✅ Timezone configurée pour le Togo: {$timezone}";
        } else {
            $this->warnings[] = "⚠️  Timezone: {$timezone} (recommandé: Africa/Lome)";
        }
    }
    
    /**
     * Vérifie la configuration de la base de données
     */
    private function checkDatabase()
    {
        $this->log("\n🗄️  Vérification de la base de données...");
        
        // Configuration
        $dbConfig = config('database.connections.mysql');
        
        if ($dbConfig['database'] === 'gradfkxi_gradis_db') {
            $this->success[] = "✅ Nom de base de données: {$dbConfig['database']}";
        } else {
            $this->errors[] = "❌ Nom de base de données incorrect: {$dbConfig['database']}";
        }
        
        if ($dbConfig['username'] === 'gradfkxi_kamal_gradis') {
            $this->success[] = "✅ Utilisateur de base de données: {$dbConfig['username']}";
        } else {
            $this->errors[] = "❌ Utilisateur de base de données incorrect: {$dbConfig['username']}";
        }
        
        // Test de connexion
        try {
            DB::connection()->getPdo();
            $this->success[] = "✅ Connexion à la base de données réussie";
            
            // Vérifier les tables principales
            $tables = ['users', 'cement_orders', 'products', 'categories'];
            foreach ($tables as $table) {
                try {
                    DB::table($table)->limit(1)->get();
                    $this->success[] = "✅ Table '{$table}' accessible";
                } catch (Exception $e) {
                    $this->warnings[] = "⚠️  Table '{$table}' non trouvée ou inaccessible";
                }
            }
            
        } catch (Exception $e) {
            $this->errors[] = "❌ Impossible de se connecter à la base de données: " . $e->getMessage();
        }
    }
    
    /**
     * Vérifie la configuration du cache
     */
    private function checkCache()
    {
        $this->log("\n🚀 Vérification du cache...");
        
        $cacheDriver = config('cache.default');
        $sessionDriver = config('session.driver');
        
        if ($cacheDriver === 'redis') {
            $this->success[] = "✅ Cache configuré avec Redis";
        } else {
            $this->warnings[] = "⚠️  Cache non configuré avec Redis: {$cacheDriver}";
        }
        
        if ($sessionDriver === 'redis') {
            $this->success[] = "✅ Sessions configurées avec Redis";
        } else {
            $this->warnings[] = "⚠️  Sessions non configurées avec Redis: {$sessionDriver}";
        }
        
        // Test Redis
        if ($cacheDriver === 'redis') {
            try {
                Cache::store('redis')->put('test_key', 'test_value', 60);
                $value = Cache::store('redis')->get('test_key');
                if ($value === 'test_value') {
                    $this->success[] = "✅ Redis fonctionne correctement";
                    Cache::store('redis')->forget('test_key');
                } else {
                    $this->errors[] = "❌ Redis ne fonctionne pas correctement";
                }
            } catch (Exception $e) {
                $this->errors[] = "❌ Erreur Redis: " . $e->getMessage();
            }
        }
    }
    
    /**
     * Vérifie la configuration mail
     */
    private function checkMail()
    {
        $this->log("\n📧 Vérification de la configuration mail...");
        
        $mailHost = config('mail.mailers.smtp.host');
        $mailFrom = config('mail.from.address');
        
        if ($mailHost === 'mail.gradis-togo.com') {
            $this->success[] = "✅ Serveur mail configuré: {$mailHost}";
        } else {
            $this->warnings[] = "⚠️  Serveur mail: {$mailHost} (attendu: mail.gradis-togo.com)";
        }
        
        if ($mailFrom === '<EMAIL>') {
            $this->success[] = "✅ Adresse expéditeur: {$mailFrom}";
        } else {
            $this->warnings[] = "⚠️  Adresse expéditeur: {$mailFrom} (attendu: <EMAIL>)";
        }
    }
    
    /**
     * Vérifie la configuration de sécurité
     */
    private function checkSecurity()
    {
        $this->log("\n🔒 Vérification de la sécurité...");
        
        // Session sécurisée
        $sessionSecure = config('session.secure');
        if ($sessionSecure) {
            $this->success[] = "✅ Cookies de session sécurisés activés";
        } else {
            $this->warnings[] = "⚠️  Cookies de session non sécurisés";
        }
        
        // SameSite
        $sessionSameSite = config('session.same_site');
        if ($sessionSameSite === 'strict') {
            $this->success[] = "✅ SameSite configuré en mode strict";
        } else {
            $this->warnings[] = "⚠️  SameSite: {$sessionSameSite} (recommandé: strict)";
        }
        
        // HTTPS forcé
        $forceHttps = config('app.force_https', false);
        if ($forceHttps) {
            $this->success[] = "✅ HTTPS forcé";
        } else {
            $this->warnings[] = "⚠️  HTTPS non forcé (recommandé en production)";
        }
    }
    
    /**
     * Vérifie les fichiers et permissions
     */
    private function checkFiles()
    {
        $this->log("\n📁 Vérification des fichiers...");
        
        // Fichier .env
        if (file_exists(base_path('.env'))) {
            $this->success[] = "✅ Fichier .env présent";
        } else {
            $this->errors[] = "❌ Fichier .env manquant";
        }
        
        // Dossiers de stockage
        $directories = [
            'storage/app',
            'storage/framework',
            'storage/logs',
            'bootstrap/cache'
        ];
        
        foreach ($directories as $dir) {
            $path = base_path($dir);
            if (is_dir($path) && is_writable($path)) {
                $this->success[] = "✅ Dossier {$dir} accessible en écriture";
            } else {
                $this->errors[] = "❌ Dossier {$dir} non accessible en écriture";
            }
        }
        
        // Assets compilés
        $manifestPath = public_path('build/manifest.json');
        if (file_exists($manifestPath)) {
            $this->success[] = "✅ Assets compilés détectés";
        } else {
            $this->warnings[] = "⚠️  Assets non compilés (exécuter: npm run build)";
        }
    }
    
    /**
     * Affiche les résultats
     */
    private function displayResults()
    {
        $this->log("\n" . str_repeat("=", 60));
        $this->log("📊 RÉSULTATS DE LA VÉRIFICATION");
        $this->log(str_repeat("=", 60));
        
        if (!empty($this->success)) {
            $this->log("\n✅ SUCCÈS (" . count($this->success) . "):");
            foreach ($this->success as $message) {
                $this->log("  " . $message);
            }
        }
        
        if (!empty($this->warnings)) {
            $this->log("\n⚠️  AVERTISSEMENTS (" . count($this->warnings) . "):");
            foreach ($this->warnings as $message) {
                $this->log("  " . $message);
            }
        }
        
        if (!empty($this->errors)) {
            $this->log("\n❌ ERREURS (" . count($this->errors) . "):");
            foreach ($this->errors as $message) {
                $this->log("  " . $message);
            }
        }
        
        $this->log("\n" . str_repeat("=", 60));
        
        if (empty($this->errors)) {
            $this->log("🎉 CONFIGURATION PRÊTE POUR LE DÉPLOIEMENT !");
            $this->log("   Votre application Gradis est correctement configurée pour gradis-togo.com");
        } else {
            $this->log("🔧 ACTIONS REQUISES AVANT LE DÉPLOIEMENT");
            $this->log("   Corrigez les erreurs ci-dessus avant de déployer");
        }
        
        $this->log(str_repeat("=", 60));
    }
    
    /**
     * Log un message
     */
    private function log($message)
    {
        echo $message . "\n";
    }
}

// Exécuter le script
if (php_sapi_name() === 'cli') {
    $verification = new ConfigVerification();
    $verification->run();
} else {
    echo "Ce script doit être exécuté en ligne de commande.\n";
    exit(1);
}
