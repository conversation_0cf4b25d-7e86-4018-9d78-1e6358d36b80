<?php
/**
 * Script final de nettoyage des logs admin
 * Supprime TOUS les logs restants pour optimisation maximale
 */

echo "🧹 NETTOYAGE FINAL DES LOGS ADMIN\n";
echo "=================================\n\n";

$controllersPath = __DIR__ . '/app/Http/Controllers/Admin/';
$totalLogsRemoved = 0;
$filesOptimized = 0;

// Parcourir tous les fichiers PHP dans le dossier Admin
$files = glob($controllersPath . '*.php');

foreach ($files as $filePath) {
    $filename = basename($filePath);
    echo "🔍 Analyse de $filename...\n";
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Compter les logs avant
    $logsBefore = substr_count($content, 'Log::') + substr_count($content, '\\Log::');
    
    if ($logsBefore > 0) {
        echo "   📊 $logsBefore logs trouvés\n";
        
        // Supprimer tous les types de logs
        $patterns = [
            // Supprimer Log::info avec tous ses paramètres
            '/\s*Log::info\([^;]*\);\s*\n/s' => '',
            '/\s*\\\\Log::info\([^;]*\);\s*\n/s' => '',
            '/\s*\\\\Illuminate\\\\Support\\\\Facades\\\\Log::info\([^;]*\);\s*\n/s' => '',
            
            // Supprimer Log::warning avec tous ses paramètres
            '/\s*Log::warning\([^;]*\);\s*\n/s' => '',
            '/\s*\\\\Log::warning\([^;]*\);\s*\n/s' => '',
            '/\s*\\\\Illuminate\\\\Support\\\\Facades\\\\Log::warning\([^;]*\);\s*\n/s' => '',
            
            // Simplifier les logs d'erreur (garder seulement le message principal)
            '/Log::error\([^,]+,\s*\[[^\]]*\]\);/s' => 'Log::error($1);',
            '/\\\\Log::error\([^,]+,\s*\[[^\]]*\]\);/s' => '\\Log::error($1);',
            '/\\\\Illuminate\\\\Support\\\\Facades\\\\Log::error\([^,]+,\s*\[[^\]]*\]\);/s' => '\\Log::error($1);',
            
            // Supprimer les logs d'erreur avec trace
            '/\s*Log::error\([^;]*getTraceAsString[^;]*\);\s*\n/s' => '',
            '/\s*\\\\Log::error\([^;]*getTraceAsString[^;]*\);\s*\n/s' => '',
            
            // Nettoyer les commentaires de logs
            '/\s*\/\/ Log [^\n]*\n/' => '',
            '/\s*\/\/ Logs? [^\n]*\n/' => '',
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            $newContent = preg_replace($pattern, $replacement, $content);
            if ($newContent !== null) {
                $content = $newContent;
            }
        }
        
        // Compter les logs après
        $logsAfter = substr_count($content, 'Log::') + substr_count($content, '\\Log::');
        $logsRemoved = $logsBefore - $logsAfter;
        
        if ($content !== $originalContent) {
            file_put_contents($filePath, $content);
            echo "   ✅ $logsRemoved logs supprimés\n";
            $totalLogsRemoved += $logsRemoved;
            $filesOptimized++;
        } else {
            echo "   ℹ️  Aucune modification nécessaire\n";
        }
    } else {
        echo "   ✅ Déjà optimisé\n";
    }
}

echo "\n🎯 RÉSULTATS FINAUX\n";
echo "==================\n";
echo "Fichiers optimisés: $filesOptimized\n";
echo "Total logs supprimés: $totalLogsRemoved\n";
echo "✅ NETTOYAGE TERMINÉ!\n\n";

echo "🚀 PERFORMANCE ADMIN MAXIMISÉE!\n";
echo "Les fonctionnalités admin devraient maintenant être ultra-rapides.\n";
