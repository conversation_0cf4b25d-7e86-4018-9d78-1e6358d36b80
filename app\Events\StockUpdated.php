<?php

namespace App\Events;

use App\Models\Product;
use App\Models\StockHistory;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StockUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $product;
    public $stockHistory;
    public $previousStock;
    public $newStock;
    public $quantityChange;
    public $type;

    /**
     * Create a new event instance.
     */
    public function __construct(Product $product, StockHistory $stockHistory)
    {
        $this->product = $product;
        $this->stockHistory = $stockHistory;
        $this->previousStock = $stockHistory->previous_stock;
        $this->newStock = $stockHistory->new_stock;
        $this->quantityChange = $stockHistory->quantity;
        $this->type = $stockHistory->type;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('admin-dashboard'),
            new Channel('stock-updates')
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'product' => [
                'id' => $this->product->id,
                'name' => $this->product->name,
                'category' => $this->product->category->name ?? 'N/A',
                'current_stock' => $this->newStock,
                'unit' => $this->product->unit,
                'status' => $this->getStockStatus()
            ],
            'movement' => [
                'type' => $this->type,
                'quantity_change' => $this->quantityChange,
                'previous_stock' => $this->previousStock,
                'new_stock' => $this->newStock,
                'timestamp' => $this->stockHistory->created_at->toISOString(),
                'formatted_date' => $this->stockHistory->created_at->format('d/m/Y H:i')
            ],
            'alert' => $this->getAlertData()
        ];
    }

    /**
     * Détermine le statut du stock
     */
    private function getStockStatus(): string
    {
        if ($this->newStock <= 0) {
            return 'out_of_stock';
        } elseif ($this->newStock <= 10) {
            return 'low_stock';
        }
        return 'normal';
    }

    /**
     * Génère les données d'alerte si nécessaire
     */
    private function getAlertData(): ?array
    {
        $status = $this->getStockStatus();
        
        if ($status === 'out_of_stock') {
            return [
                'type' => 'danger',
                'title' => 'Stock épuisé',
                'message' => "Le produit {$this->product->name} est en rupture de stock !",
                'action_url' => route('admin.products.edit', $this->product->id)
            ];
        } elseif ($status === 'low_stock') {
            return [
                'type' => 'warning',
                'title' => 'Stock faible',
                'message' => "Le produit {$this->product->name} a un stock faible ({$this->newStock} {$this->product->unit})",
                'action_url' => route('admin.products.edit', $this->product->id)
            ];
        }

        return null;
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'stock.updated';
    }
}
