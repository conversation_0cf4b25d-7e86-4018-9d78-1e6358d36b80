<?php

namespace App\Http\Controllers\Cashier;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Sale;
use App\Models\PaymentSchedule;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:cashier');
    }
    
    /**
     * Affiche la liste des paiements effectués
     */
    public function index()
    {
        $payments = Payment::with(['sale', 'cashier'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('cashier.payments.index', compact('payments'));
    }

    /**
     * Affiche les détails d'un paiement spécifique
     */
    public function show(Payment $payment)
    {
        $payment->load(['sale.supply.details.product', 'cashier']);
        return view('cashier.payments.show', compact('payment'));
    }
    
    /**
     * Affiche la liste des ventes en attente de paiement
     */
    public function pendingSales()
    {
        // Récupérer les ventes éligibles pour le caissier
        $sales = Sale::with(['supply.details.product', 'city', 'createdBy', 'driver'])
            ->where(function($query) {
                // Ventes sans remise ni augmentation de prix
                $query->where(function($q) {
                    $q->where('discount_per_ton', 0)
                      ->where('price_modified', false);
                })
                // OU ventes avec remise/augmentation validées par l'admin
                ->orWhere('admin_validation_status', 'approved');
            })
            ->whereIn('status', ['pending_payment', 'partially_paid'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);
            
        return view('cashier.payments.pending_sales', compact('sales'));
    }
    
    /**
     * Affiche le formulaire de paiement pour une vente spécifique
     */
    public function processSale(Sale $sale)
    {
        // Vérifier si la vente est éligible pour le paiement
        if (!$sale->is_eligible_for_cashier) {
            return redirect()->route('cashier.payments.pending')
                ->with('error', 'Cette vente n\'est pas éligible pour le paiement.');
        }
        
        $sale->load(['supply.details.product', 'city', 'createdBy', 'driver', 'payments']);
        
        // Calculer le montant restant à payer
        $sale->updateAmountPaid();
        
        return view('cashier.payments.process', compact('sale'));
    }
    
    /**
     * Enregistre un nouveau paiement pour une vente
     */
    public function storePayment(Request $request, Sale $sale)
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:1',
            'payment_method' => 'required|in:cash,bank_transfer,check,mobile_money',
            'reference_number' => 'nullable|string|max:50',
            'notes' => 'nullable|string|max:500',
            'payment_date' => 'required|date'
            // 'payment_schedule_id' => 'nullable|exists:payment_schedules,id' // Commenté car la table n'existe pas encore
        ]);
        
        // Vérifier si le montant ne dépasse pas le montant restant à payer
        $remainingAmount = $sale->total_amount - $sale->amount_paid;
        
        if ($validated['amount'] > $remainingAmount) {
            return redirect()->back()
                ->with('error', 'Le montant du paiement ne peut pas dépasser le montant restant à payer.')
                ->withInput();
        }
        
        DB::beginTransaction();
        
        try {
            // Déterminer le poste de l'utilisateur (pour la référence)
            $position = Auth::user()->position ?? 'Caissier';
            // Utiliser le nom complet du poste sans espaces ni caractères spéciaux
            $positionCode = strtoupper(preg_replace('/[^a-zA-Z0-9]/', '', $position));
            
            // Obtenir le dernier rang de paiement
            $lastPayment = Payment::latest('id')->first();
            $lastId = $lastPayment ? $lastPayment->id + 1 : 1;
            $paymentRank = str_pad($lastId, 6, '0', STR_PAD_LEFT);
            
            // Générer la référence unique du paiement
            $reference = 'RGMT' . $positionCode . $paymentRank;
            
            // Générer un numéro de reçu unique
            $receiptNumber = 'REC-' . date('Ymd') . '-' . strtoupper(Str::random(6));
            
            // Créer le paiement
            $payment = Payment::create([
                'reference' => $reference, // Référence unique générée automatiquement
                'sale_id' => $sale->id,
                'cashier_id' => Auth::id(),
                'position' => $position, // Poste de l'utilisateur
                'amount' => $validated['amount'],
                'payment_method' => $validated['payment_method'],
                'reference_number' => $validated['reference_number'],
                'notes' => $validated['notes'] ?? ('Paiement de ' . number_format($validated['amount'], 0, ',', ' ') . ' FCFA'),
                'payment_date' => $validated['payment_date'],
                'status' => 'completed'
            ]);
            
            // Mettre à jour le montant payé et le statut de la vente
            $sale->updateAmountPaid();
            
            // Code pour les échéanciers de paiement commenté car la table n'existe pas encore
            /*
            // Si un échéancier est spécifié, mettre à jour son statut
            if ($validated['payment_schedule_id']) {
                $schedule = PaymentSchedule::find($validated['payment_schedule_id']);
                $paidAmount = $schedule->payments()->where('status', 'completed')->sum('amount');
                
                if ($paidAmount >= $schedule->amount) {
                    $schedule->status = 'paid';
                } elseif ($paidAmount > 0) {
                    $schedule->status = 'partially_paid';
                }
                
                $schedule->save();
            }
            */
            
            DB::commit();
            
            return redirect()->route('cashier.payments.receipt', $payment->id)
                ->with('success', 'Paiement enregistré avec succès.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Une erreur est survenue lors de l\'enregistrement du paiement: ' . $e->getMessage())
                ->withInput();
        }
    }
    
    /**
     * Affiche le reçu de paiement
     */
    public function showReceipt(Payment $payment)
    {
        $payment->load(['sale.supply.details.product', 'sale.city', 'cashier']);
        return view('cashier.payments.receipt', compact('payment'));
    }
    
    /**
     * Affiche le formulaire de création d'un échéancier de paiement
     */
    public function createSchedule(Sale $sale)
    {
        $sale->load(['supply.details.product', 'city', 'payments']);
        return view('cashier.payments.create_schedule', compact('sale'));
    }
    
    /**
     * Enregistre un nouvel échéancier de paiement
     */
    public function storeSchedule(Request $request, Sale $sale)
    {
        // Informer l'utilisateur que cette fonctionnalité n'est pas encore disponible
        return redirect()->route('cashier.payments.process', $sale->id)
            ->with('warning', 'La fonctionnalité d\'\u00e9chéancier de paiement n\'est pas encore disponible. Veuillez effectuer un paiement direct.');
        
        /* Code commenté car la table payment_schedules n'existe pas encore
        $validated = $request->validate([
            'schedules' => 'required|array|min:1',
            'schedules.*.due_date' => 'required|date',
            'schedules.*.amount' => 'required|numeric|min:1',
            'schedules.*.notes' => 'nullable|string|max:500'
        ]);
        
        // Vérifier si le total des échéanciers ne dépasse pas le montant restant à payer
        $remainingAmount = $sale->total_amount - $sale->amount_paid;
        $totalScheduledAmount = array_sum(array_column($validated['schedules'], 'amount'));
        
        if ($totalScheduledAmount > $remainingAmount) {
            return redirect()->back()
                ->with('error', 'Le montant total des échéanciers ne peut pas dépasser le montant restant à payer.')
                ->withInput();
        }
        
        DB::beginTransaction();
        
        try {
            foreach ($validated['schedules'] as $scheduleData) {
                PaymentSchedule::create([
                    'sale_id' => $sale->id,
                    'due_date' => $scheduleData['due_date'],
                    'amount' => $scheduleData['amount'],
                    'status' => 'pending',
                    'notes' => $scheduleData['notes'] ?? null,
                    'created_by' => Auth::id()
                ]);
            }
            
            DB::commit();
            
            return redirect()->route('cashier.payments.process', $sale->id)
                ->with('success', 'Échéancier de paiement créé avec succès.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Une erreur est survenue lors de la création de l\'\u00e9chéancier: ' . $e->getMessage())
                ->withInput();
        }
        */
    }
}
