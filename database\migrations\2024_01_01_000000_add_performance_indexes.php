<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Index pour optimiser les requêtes de dashboard Admin/Comptable
        
        // Index sur orders pour les requêtes de revenus et statistiques
        Schema::table('orders', function (Blueprint $table) {
            $table->index(['status', 'created_at'], 'idx_orders_status_created');
            $table->index(['created_at', 'total_amount'], 'idx_orders_created_amount');
            $table->index(['user_id', 'status'], 'idx_orders_user_status');
        });

        // Index sur supplies pour les requêtes d'approvisionnements
        Schema::table('supplies', function (Blueprint $table) {
            $table->index(['status', 'created_at'], 'idx_supplies_status_created');
            $table->index(['created_by', 'status'], 'idx_supplies_created_by_status');
            $table->index(['created_by', 'created_at'], 'idx_supplies_created_by_date');
        });

        // Index sur products pour les requêtes de stock
        Schema::table('products', function (Blueprint $table) {
            $table->index(['stock_quantity', 'is_active'], 'idx_products_stock_active');
            $table->index(['category_id', 'is_active'], 'idx_products_category_active');
            $table->index(['is_active', 'created_at'], 'idx_products_active_created');
        });

        // Index sur cement_orders pour les requêtes de ciment
        if (Schema::hasTable('cement_orders')) {
            Schema::table('cement_orders', function (Blueprint $table) {
                $table->index(['status', 'created_at'], 'idx_cement_orders_status_created');
                $table->index(['created_at', 'total_amount'], 'idx_cement_orders_created_amount');
            });
        }

        // Index sur drivers pour les requêtes de statut
        if (Schema::hasTable('drivers')) {
            Schema::table('drivers', function (Blueprint $table) {
                $table->index(['status', 'is_active'], 'idx_drivers_status_active');
            });
        }

        // Index sur trucks pour les requêtes de statut
        if (Schema::hasTable('trucks')) {
            Schema::table('trucks', function (Blueprint $table) {
                $table->index('status', 'idx_trucks_status');
            });
        }

        // Index sur stock_histories pour les mouvements récents
        if (Schema::hasTable('stock_histories')) {
            Schema::table('stock_histories', function (Blueprint $table) {
                $table->index(['created_at', 'product_id'], 'idx_stock_histories_created_product');
                $table->index(['product_id', 'type'], 'idx_stock_histories_product_type');
            });
        }

        // Index sur order_items pour les requêtes de produits populaires
        if (Schema::hasTable('order_items')) {
            Schema::table('order_items', function (Blueprint $table) {
                $table->index(['product_id', 'created_at'], 'idx_order_items_product_created');
            });
        }

        // Index sur model_has_roles pour les requêtes de rôles (Spatie Permission)
        if (Schema::hasTable('model_has_roles')) {
            Schema::table('model_has_roles', function (Blueprint $table) {
                $table->index(['model_type', 'role_id'], 'idx_model_has_roles_type_role');
            });
        }

        // Index sur users pour les requêtes d'utilisateurs actifs
        Schema::table('users', function (Blueprint $table) {
            $table->index(['is_active', 'created_at'], 'idx_users_active_created');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Supprimer les index dans l'ordre inverse
        
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_active_created');
        });

        if (Schema::hasTable('model_has_roles')) {
            Schema::table('model_has_roles', function (Blueprint $table) {
                $table->dropIndex('idx_model_has_roles_type_role');
            });
        }

        if (Schema::hasTable('order_items')) {
            Schema::table('order_items', function (Blueprint $table) {
                $table->dropIndex('idx_order_items_product_created');
            });
        }

        if (Schema::hasTable('stock_histories')) {
            Schema::table('stock_histories', function (Blueprint $table) {
                $table->dropIndex('idx_stock_histories_created_product');
                $table->dropIndex('idx_stock_histories_product_type');
            });
        }

        if (Schema::hasTable('trucks')) {
            Schema::table('trucks', function (Blueprint $table) {
                $table->dropIndex('idx_trucks_status');
            });
        }

        if (Schema::hasTable('drivers')) {
            Schema::table('drivers', function (Blueprint $table) {
                $table->dropIndex('idx_drivers_status_active');
            });
        }

        if (Schema::hasTable('cement_orders')) {
            Schema::table('cement_orders', function (Blueprint $table) {
                $table->dropIndex('idx_cement_orders_status_created');
                $table->dropIndex('idx_cement_orders_created_amount');
            });
        }

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('idx_products_stock_active');
            $table->dropIndex('idx_products_category_active');
            $table->dropIndex('idx_products_active_created');
        });

        Schema::table('supplies', function (Blueprint $table) {
            $table->dropIndex('idx_supplies_status_created');
            $table->dropIndex('idx_supplies_created_by_status');
            $table->dropIndex('idx_supplies_created_by_date');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_status_created');
            $table->dropIndex('idx_orders_created_amount');
            $table->dropIndex('idx_orders_user_status');
        });
    }
};
