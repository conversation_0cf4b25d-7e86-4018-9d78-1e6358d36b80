<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Vide complètement les tables drivers et trucks
     */
    public function up(): void
    {
        // Désactiver temporairement les contraintes de clé étrangère
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Vider complètement les tables (y compris les soft deleted)
        DB::table('drivers')->truncate();
        DB::table('trucks')->truncate();

        // Réactiver les contraintes de clé étrangère
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        echo "✅ Tables drivers et trucks vidées complètement\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Cette migration ne peut pas être annulée car les données sont supprimées définitivement
        // Vous devrez restaurer les données depuis une sauvegarde si nécessaire
        echo "⚠️  Cette migration ne peut pas être annulée - restaurez depuis une sauvegarde si nécessaire\n";
    }
};
