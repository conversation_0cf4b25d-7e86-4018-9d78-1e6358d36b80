#!/bin/bash

# Script de déploiement pour Gradis sur Namecheap
# Usage: ./deploy.sh [environment]

set -e

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_NAME="gradis"
BACKUP_DIR="backups"
LOG_FILE="deployment.log"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a $LOG_FILE
}

# Vérification des prérequis
check_requirements() {
    log "Vérification des prérequis..."
    
    # Vérifier PHP
    if ! command -v php &> /dev/null; then
        error "PHP n'est pas installé"
    fi
    
    # Vérifier Composer
    if ! command -v composer &> /dev/null; then
        error "Composer n'est pas installé"
    fi
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js n'est pas installé"
    fi
    
    # Vérifier NPM
    if ! command -v npm &> /dev/null; then
        error "NPM n'est pas installé"
    fi
    
    log "Tous les prérequis sont satisfaits"
}

# Sauvegarde de la base de données
backup_database() {
    log "Création d'une sauvegarde de la base de données..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p $BACKUP_DIR
    fi
    
    BACKUP_FILE="$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # Lire les informations de la base de données depuis .env
    if [ -f ".env" ]; then
        DB_HOST=$(grep DB_HOST .env | cut -d '=' -f2)
        DB_DATABASE=$(grep DB_DATABASE .env | cut -d '=' -f2)
        DB_USERNAME=$(grep DB_USERNAME .env | cut -d '=' -f2)
        DB_PASSWORD=$(grep DB_PASSWORD .env | cut -d '=' -f2)
        
        mysqldump -h$DB_HOST -u$DB_USERNAME -p$DB_PASSWORD $DB_DATABASE > $BACKUP_FILE
        log "Sauvegarde créée: $BACKUP_FILE"
    else
        warning "Fichier .env non trouvé, sauvegarde ignorée"
    fi
}

# Mise en mode maintenance
enable_maintenance() {
    log "Activation du mode maintenance..."
    php artisan down --message="Mise à jour en cours..." --retry=60
}

# Désactivation du mode maintenance
disable_maintenance() {
    log "Désactivation du mode maintenance..."
    php artisan up
}

# Installation des dépendances
install_dependencies() {
    log "Installation des dépendances PHP..."
    composer install --no-dev --optimize-autoloader --no-interaction
    
    log "Installation des dépendances Node.js..."
    npm ci --production
}

# Configuration de l'environnement
setup_environment() {
    log "Configuration de l'environnement $ENVIRONMENT..."
    
    # Copier le fichier d'environnement approprié
    if [ "$ENVIRONMENT" = "production" ]; then
        if [ -f ".env.production" ]; then
            cp .env.production .env
            log "Fichier .env.production copié vers .env"
        else
            warning "Fichier .env.production non trouvé"
        fi
    fi
    
    # Générer la clé d'application si nécessaire
    if ! grep -q "APP_KEY=" .env || [ -z "$(grep APP_KEY .env | cut -d '=' -f2)" ]; then
        log "Génération de la clé d'application..."
        php artisan key:generate --force
    fi
}

# Optimisation Laravel
optimize_laravel() {
    log "Optimisation de Laravel..."
    
    # Nettoyer les caches
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    php artisan cache:clear
    
    # Optimiser pour la production
    if [ "$ENVIRONMENT" = "production" ]; then
        php artisan config:cache
        php artisan route:cache
        php artisan view:cache
        php artisan event:cache
    fi
    
    # Optimiser l'autoloader
    composer dump-autoload --optimize
}

# Migration de la base de données
migrate_database() {
    log "Exécution des migrations..."
    php artisan migrate --force
    
    # Seeder pour la production (si nécessaire)
    if [ "$ENVIRONMENT" = "production" ]; then
        log "Exécution des seeders de production..."
        php artisan db:seed --class=ProductionSeeder --force || true
    fi
}

# Compilation des assets
build_assets() {
    log "Compilation des assets..."
    npm run build
    
    # Optimiser les images (si le service est disponible)
    if [ -f "artisan" ]; then
        php artisan optimize:images || true
    fi
}

# Configuration des permissions
set_permissions() {
    log "Configuration des permissions..."
    
    # Permissions pour les dossiers de stockage
    chmod -R 775 storage
    chmod -R 775 bootstrap/cache
    
    # Permissions pour les logs
    if [ -d "storage/logs" ]; then
        chmod -R 775 storage/logs
    fi
    
    # Propriétaire des fichiers (ajuster selon votre configuration serveur)
    # chown -R www-data:www-data storage bootstrap/cache
}

# Nettoyage post-déploiement
cleanup() {
    log "Nettoyage post-déploiement..."
    
    # Supprimer les fichiers temporaires
    rm -rf node_modules/.cache
    rm -rf storage/framework/cache/data/*
    rm -rf storage/framework/sessions/*
    rm -rf storage/framework/views/*
    
    # Nettoyer les anciens logs (garder les 30 derniers jours)
    find storage/logs -name "*.log" -mtime +30 -delete || true
    
    # Nettoyer les anciennes sauvegardes (garder les 7 dernières)
    if [ -d "$BACKUP_DIR" ]; then
        ls -t $BACKUP_DIR/*.sql | tail -n +8 | xargs rm -f || true
    fi
}

# Vérification post-déploiement
verify_deployment() {
    log "Vérification du déploiement..."
    
    # Vérifier que l'application répond
    if command -v curl &> /dev/null; then
        if curl -f -s http://localhost > /dev/null; then
            log "Application accessible"
        else
            warning "L'application ne semble pas accessible"
        fi
    fi
    
    # Vérifier les permissions
    if [ ! -w "storage/logs" ]; then
        warning "Le dossier storage/logs n'est pas accessible en écriture"
    fi
    
    log "Vérification terminée"
}

# Fonction principale
main() {
    log "Début du déploiement de $PROJECT_NAME en environnement $ENVIRONMENT"
    
    check_requirements
    backup_database
    enable_maintenance
    
    # Piège pour désactiver la maintenance en cas d'erreur
    trap disable_maintenance EXIT
    
    install_dependencies
    setup_environment
    migrate_database
    build_assets
    optimize_laravel
    set_permissions
    cleanup
    
    disable_maintenance
    verify_deployment
    
    log "Déploiement terminé avec succès!"
    info "Logs disponibles dans: $LOG_FILE"
}

# Exécution du script principal
main "$@"
