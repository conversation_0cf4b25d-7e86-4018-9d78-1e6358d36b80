<?php

/**
 * Script de sauvegarde automatique pour Gradis
 * Sauvegarde la base de données et les fichiers importants
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class BackupScript
{
    private $app;
    private $backupPath;
    private $maxBackups = 7; // Garder 7 sauvegardes
    
    public function __construct()
    {
        $this->initializeLaravel();
        $this->backupPath = storage_path('backups');
        $this->ensureBackupDirectory();
    }
    
    /**
     * Initialise l'application Laravel
     */
    private function initializeLaravel()
    {
        $this->app = require_once __DIR__ . '/../bootstrap/app.php';
        $this->app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    }
    
    /**
     * S'assure que le dossier de sauvegarde existe
     */
    private function ensureBackupDirectory()
    {
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }
    
    /**
     * Exécute la sauvegarde complète
     */
    public function run()
    {
        $this->log("🔄 Début de la sauvegarde automatique");
        
        try {
            $timestamp = date('Y-m-d_H-i-s');
            
            // Sauvegarde de la base de données
            $dbBackupFile = $this->backupDatabase($timestamp);
            
            // Sauvegarde des fichiers importants
            $filesBackupFile = $this->backupFiles($timestamp);
            
            // Nettoyage des anciennes sauvegardes
            $this->cleanupOldBackups();
            
            $this->log("✅ Sauvegarde terminée avec succès");
            $this->log("📁 Base de données: {$dbBackupFile}");
            $this->log("📁 Fichiers: {$filesBackupFile}");
            
        } catch (Exception $e) {
            $this->error("❌ Erreur lors de la sauvegarde: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Sauvegarde la base de données
     */
    private function backupDatabase($timestamp)
    {
        $this->log("💾 Sauvegarde de la base de données...");
        
        $config = config('database.connections.mysql');
        $filename = "db_backup_{$timestamp}.sql";
        $filepath = $this->backupPath . '/' . $filename;
        
        // Construire la commande mysqldump
        $command = sprintf(
            'mysqldump -h%s -P%s -u%s -p%s %s > %s',
            escapeshellarg($config['host']),
            escapeshellarg($config['port']),
            escapeshellarg($config['username']),
            escapeshellarg($config['password']),
            escapeshellarg($config['database']),
            escapeshellarg($filepath)
        );
        
        // Exécuter la commande
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception("Erreur lors de la sauvegarde de la base de données");
        }
        
        // Vérifier que le fichier a été créé et n'est pas vide
        if (!file_exists($filepath) || filesize($filepath) === 0) {
            throw new Exception("Le fichier de sauvegarde de la base de données est vide ou n'existe pas");
        }
        
        // Compresser le fichier SQL
        $compressedFile = $filepath . '.gz';
        if (function_exists('gzencode')) {
            $data = file_get_contents($filepath);
            $compressed = gzencode($data, 9);
            file_put_contents($compressedFile, $compressed);
            unlink($filepath); // Supprimer le fichier non compressé
            $filepath = $compressedFile;
        }
        
        $size = $this->formatBytes(filesize($filepath));
        $this->log("✅ Base de données sauvegardée ({$size})");
        
        return $filename;
    }
    
    /**
     * Sauvegarde les fichiers importants
     */
    private function backupFiles($timestamp)
    {
        $this->log("📁 Sauvegarde des fichiers...");
        
        $filename = "files_backup_{$timestamp}.tar.gz";
        $filepath = $this->backupPath . '/' . $filename;
        
        // Dossiers et fichiers à sauvegarder
        $itemsToBackup = [
            '.env',
            'storage/app',
            'public/uploads',
            'public/images',
            'config',
        ];
        
        // Filtrer les éléments qui existent
        $existingItems = [];
        foreach ($itemsToBackup as $item) {
            $fullPath = base_path($item);
            if (file_exists($fullPath)) {
                $existingItems[] = $item;
            }
        }
        
        if (empty($existingItems)) {
            $this->log("⚠️  Aucun fichier à sauvegarder trouvé");
            return null;
        }
        
        // Créer l'archive tar.gz
        $command = sprintf(
            'cd %s && tar -czf %s %s',
            escapeshellarg(base_path()),
            escapeshellarg($filepath),
            implode(' ', array_map('escapeshellarg', $existingItems))
        );
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception("Erreur lors de la création de l'archive des fichiers");
        }
        
        if (!file_exists($filepath)) {
            throw new Exception("L'archive des fichiers n'a pas été créée");
        }
        
        $size = $this->formatBytes(filesize($filepath));
        $this->log("✅ Fichiers sauvegardés ({$size})");
        
        return $filename;
    }
    
    /**
     * Nettoie les anciennes sauvegardes
     */
    private function cleanupOldBackups()
    {
        $this->log("🧹 Nettoyage des anciennes sauvegardes...");
        
        $files = glob($this->backupPath . '/*');
        
        // Trier par date de modification (plus récent en premier)
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        $deletedCount = 0;
        
        // Supprimer les fichiers au-delà de la limite
        for ($i = $this->maxBackups; $i < count($files); $i++) {
            if (unlink($files[$i])) {
                $deletedCount++;
            }
        }
        
        if ($deletedCount > 0) {
            $this->log("✅ {$deletedCount} anciennes sauvegardes supprimées");
        } else {
            $this->log("✅ Aucune ancienne sauvegarde à supprimer");
        }
    }
    
    /**
     * Formate la taille en bytes en format lisible
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Obtient la liste des sauvegardes disponibles
     */
    public function listBackups()
    {
        $files = glob($this->backupPath . '/*');
        $backups = [];
        
        foreach ($files as $file) {
            $backups[] = [
                'name' => basename($file),
                'size' => $this->formatBytes(filesize($file)),
                'date' => date('Y-m-d H:i:s', filemtime($file)),
                'path' => $file
            ];
        }
        
        // Trier par date (plus récent en premier)
        usort($backups, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });
        
        return $backups;
    }
    
    /**
     * Restaure une sauvegarde de base de données
     */
    public function restoreDatabase($backupFile)
    {
        $this->log("🔄 Restauration de la base de données depuis: {$backupFile}");
        
        $filepath = $this->backupPath . '/' . $backupFile;
        
        if (!file_exists($filepath)) {
            throw new Exception("Le fichier de sauvegarde n'existe pas: {$backupFile}");
        }
        
        $config = config('database.connections.mysql');
        
        // Décompresser si nécessaire
        $sqlFile = $filepath;
        if (pathinfo($filepath, PATHINFO_EXTENSION) === 'gz') {
            $sqlFile = str_replace('.gz', '', $filepath);
            $compressed = file_get_contents($filepath);
            $decompressed = gzdecode($compressed);
            file_put_contents($sqlFile, $decompressed);
        }
        
        // Restaurer la base de données
        $command = sprintf(
            'mysql -h%s -P%s -u%s -p%s %s < %s',
            escapeshellarg($config['host']),
            escapeshellarg($config['port']),
            escapeshellarg($config['username']),
            escapeshellarg($config['password']),
            escapeshellarg($config['database']),
            escapeshellarg($sqlFile)
        );
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        // Nettoyer le fichier temporaire si décompressé
        if ($sqlFile !== $filepath) {
            unlink($sqlFile);
        }
        
        if ($returnCode !== 0) {
            throw new Exception("Erreur lors de la restauration de la base de données");
        }
        
        $this->log("✅ Base de données restaurée avec succès");
    }
    
    /**
     * Log un message
     */
    private function log($message)
    {
        echo $message . "\n";
        Log::info($message);
    }
    
    /**
     * Log une erreur
     */
    private function error($message)
    {
        echo $message . "\n";
        Log::error($message);
    }
}

// Exécuter le script si appelé en ligne de commande
if (php_sapi_name() === 'cli') {
    $action = $argv[1] ?? 'backup';
    
    $backup = new BackupScript();
    
    switch ($action) {
        case 'backup':
            $backup->run();
            break;
            
        case 'list':
            $backups = $backup->listBackups();
            echo "📋 Sauvegardes disponibles:\n";
            foreach ($backups as $backup) {
                echo "  - {$backup['name']} ({$backup['size']}) - {$backup['date']}\n";
            }
            break;
            
        case 'restore':
            if (!isset($argv[2])) {
                echo "Usage: php backup.php restore <nom_fichier_sauvegarde>\n";
                exit(1);
            }
            $backup->restoreDatabase($argv[2]);
            break;
            
        default:
            echo "Usage: php backup.php [backup|list|restore]\n";
            exit(1);
    }
} else {
    echo "Ce script doit être exécuté en ligne de commande.\n";
    exit(1);
}
