<?php

echo "🔧 CORRECTION AUTOMATIQUE DES VARIABLES SANS \$\n";
echo "===============================================\n\n";

$viewPath = 'resources/views/admin/dashboard.blade.php';

if (!file_exists($viewPath)) {
    echo "❌ Fichier non trouvé: $viewPath\n";
    exit(1);
}

echo "📖 Lecture du fichier...\n";
$content = file_get_contents($viewPath);

if ($content === false) {
    echo "❌ Impossible de lire le fichier\n";
    exit(1);
}

echo "🔍 Recherche des variables sans \$...\n";

// Patterns pour détecter les variables sans $
$missingDollarPatterns = [
    // Variables dans les conditions ternaires
    '/\(\s*(stats|supply|product|user|driver)\s*\[/' => '($1[',
    '/\(\s*(stats|supply|product|user|driver)->/' => '($1->',
    
    // Variables dans les expressions
    '/\s+(stats|supply|product|user|driver)\s*\[/' => ' $1[',
    '/\s+(stats|supply|product|user|driver)->/' => ' $1->',
    
    // Variables au début d'expressions
    '/\{\{\s*(stats|supply|product|user|driver)\s*\[/' => '{{ $1[',
    '/\{\{\s*(stats|supply|product|user|driver)->/' => '{{ $1->',
    
    // Variables après opérateurs
    '/\+\s*(stats|supply|product|user|driver)\s*\[/' => '+ $1[',
    '/\+\s*(stats|supply|product|user|driver)->/' => '+ $1->',
    '/-\s*(stats|supply|product|user|driver)\s*\[/' => '- $1[',
    '/-\s*(stats|supply|product|user|driver)->/' => '- $1->',
    '/\*\s*(stats|supply|product|user|driver)\s*\[/' => '* $1[',
    '/\*\s*(stats|supply|product|user|driver)->/' => '* $1->',
    '/\/\s*(stats|supply|product|user|driver)\s*\[/' => '/ $1[',
    '/\/\s*(stats|supply|product|user|driver)->/' => '/ $1->',
    
    // Variables après virgules
    '/,\s*(stats|supply|product|user|driver)\s*\[/' => ', $1[',
    '/,\s*(stats|supply|product|user|driver)->/' => ', $1->',
    
    // Variables après ? dans ternaire
    '/\?\s*(stats|supply|product|user|driver)\s*\[/' => '? $1[',
    '/\?\s*(stats|supply|product|user|driver)->/' => '? $1->',
    
    // Variables après : dans ternaire
    '/:\s*(stats|supply|product|user|driver)\s*\[/' => ': $1[',
    '/:\s*(stats|supply|product|user|driver)->/' => ': $1->',
];

$replacements = 0;

foreach ($missingDollarPatterns as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $content, -1, $count);
    
    if ($count > 0) {
        echo "✅ Ajouté \$ pour pattern '$pattern': $count occurrences\n";
        $content = $newContent;
        $replacements += $count;
    }
}

// Corrections spécifiques pour les cas complexes
$specificCorrections = [
    // Corriger les variables sans $ dans les conditions
    '(stats[' => '($stats[',
    '(supply->' => '($supply->',
    '(product->' => '($product->',
    '(user->' => '($user->',
    '(driver->' => '($driver->',
    
    // Corriger après opérateurs
    '+ stats[' => '+ $stats[',
    '- stats[' => '- $stats[',
    '+ supply->' => '+ $supply->',
    '- supply->' => '- $supply->',
    '+ product->' => '+ $product->',
    '- product->' => '- $product->',
    '+ user->' => '+ $user->',
    '- user->' => '- $user->',
    
    // Corriger dans les expressions Blade
    '{{ stats[' => '{{ $stats[',
    '{{ supply->' => '{{ $supply->',
    '{{ product->' => '{{ $product->',
    '{{ user->' => '{{ $user->',
    '{{ driver->' => '{{ $driver->',
];

foreach ($specificCorrections as $search => $replace) {
    if (strpos($content, $search) !== false) {
        $content = str_replace($search, $replace, $content);
        echo "✅ Correction spécifique: '$search' → '$replace'\n";
        $replacements++;
    }
}

if ($replacements > 0) {
    echo "\n💾 Sauvegarde des modifications...\n";
    
    // Backup du fichier original
    $backupPath = $viewPath . '.dollar-backup.' . date('Y-m-d-H-i-s');
    copy($viewPath, $backupPath);
    echo "📋 Backup créé: $backupPath\n";
    
    // Sauvegarde du fichier modifié
    if (file_put_contents($viewPath, $content) !== false) {
        echo "✅ Fichier corrigé avec succès!\n";
        echo "🔢 Total des corrections: $replacements\n";
    } else {
        echo "❌ Erreur lors de la sauvegarde\n";
        exit(1);
    }
} else {
    echo "ℹ️  Aucune correction nécessaire\n";
}

echo "\n🎯 RÉSUMÉ:\n";
echo "=========\n";
echo "✅ Variables avec \$ ajouté\n";
echo "✅ Syntaxe PHP correcte\n";
echo "✅ Expressions Blade valides\n";

echo "\n🚀 Toutes les variables ont maintenant le \$ requis!\n";
