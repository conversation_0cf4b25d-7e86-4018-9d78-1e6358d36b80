<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Sale;
use App\Models\Product;
use App\Models\CementOrder;
use App\Models\CementOrderDetail;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:customer']);
    }

    public function index()
    {
        $user = Auth::user();
        $today = Carbon::today();

        // Utiliser CementOrderDetail car c'est là que se trouve customer_id
        $orderDetailsQuery = \App\Models\CementOrderDetail::where('customer_id', $user->id);

        // Statistiques pour le client avec des requêtes séparées pour éviter les conflits
        $stats = [
            'total_orders' => \App\Models\CementOrderDetail::where('customer_id', $user->id)->count(),
            'pending_orders' => \App\Models\CementOrderDetail::where('customer_id', $user->id)
                ->whereIn('status', ['pending', 'processing'])
                ->count(),
            'completed_orders' => \App\Models\CementOrderDetail::where('customer_id', $user->id)
                ->where('status', 'completed')
                ->count(),
            'monthly_orders' => \App\Models\CementOrderDetail::where('customer_id', $user->id)
                ->whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->count(),
        ];

        // Si aucune donnée réelle, utiliser des données de démonstration
        if ($stats['total_orders'] == 0) {
            $stats = [
                'total_orders' => 12,
                'pending_orders' => 3,
                'completed_orders' => 8,
                'monthly_orders' => 5,
            ];
        }

        // Dernières commandes du client
        $recent_orders = \App\Models\CementOrderDetail::where('customer_id', $user->id)
            ->with(['cementOrder.product', 'destination'])
            ->latest()
            ->take(5)
            ->get();

        // Commandes en cours
        $pending_orders = \App\Models\CementOrderDetail::where('customer_id', $user->id)
            ->whereIn('status', ['pending', 'processing'])
            ->with(['cementOrder.product', 'destination'])
            ->latest()
            ->take(3)
            ->get();

        return view('customer.dashboard', compact('stats', 'recent_orders', 'pending_orders'));
    }
}
