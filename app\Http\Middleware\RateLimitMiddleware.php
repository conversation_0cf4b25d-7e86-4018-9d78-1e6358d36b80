<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class RateLimitMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $type = 'general'): Response
    {
        $key = $this->resolveRequestSignature($request, $type);
        $maxAttempts = $this->getMaxAttempts($type);
        $decayMinutes = $this->getDecayMinutes($type);

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            return $this->buildResponse($key, $maxAttempts);
        }

        RateLimiter::hit($key, $decayMinutes * 60);

        $response = $next($request);

        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts)
        );
    }

    /**
     * Résout la signature de la requête pour le rate limiting
     */
    protected function resolveRequestSignature(Request $request, string $type): string
    {
        $ip = $request->ip();
        $user = $request->user();
        
        switch ($type) {
            case 'login':
                return "login:{$ip}";
            case 'api':
                return $user ? "api:user:{$user->id}" : "api:ip:{$ip}";
            case 'upload':
                return $user ? "upload:user:{$user->id}" : "upload:ip:{$ip}";
            case 'search':
                return $user ? "search:user:{$user->id}" : "search:ip:{$ip}";
            default:
                return $user ? "general:user:{$user->id}" : "general:ip:{$ip}";
        }
    }

    /**
     * Obtient le nombre maximum de tentatives selon le type
     */
    protected function getMaxAttempts(string $type): int
    {
        $limits = [
            'login' => 5,      // 5 tentatives de connexion par minute
            'api' => 100,      // 100 requêtes API par minute
            'upload' => 10,    // 10 uploads par minute
            'search' => 30,    // 30 recherches par minute
            'general' => 60,   // 60 requêtes générales par minute
        ];

        return $limits[$type] ?? 60;
    }

    /**
     * Obtient la durée de décroissance en minutes
     */
    protected function getDecayMinutes(string $type): int
    {
        $decay = [
            'login' => 15,     // Blocage de 15 minutes pour les tentatives de connexion
            'api' => 1,        // Reset chaque minute pour l'API
            'upload' => 5,     // Reset toutes les 5 minutes pour les uploads
            'search' => 1,     // Reset chaque minute pour les recherches
            'general' => 1,    // Reset chaque minute pour les requêtes générales
        ];

        return $decay[$type] ?? 1;
    }

    /**
     * Construit la réponse de rate limiting
     */
    protected function buildResponse(string $key, int $maxAttempts): Response
    {
        $retryAfter = RateLimiter::availableIn($key);
        
        $response = response()->json([
            'error' => 'Trop de tentatives. Veuillez réessayer plus tard.',
            'message' => "Limite de {$maxAttempts} requêtes dépassée.",
            'retry_after' => $retryAfter
        ], 429);

        return $this->addHeaders(
            $response,
            $maxAttempts,
            0,
            $retryAfter
        );
    }

    /**
     * Ajoute les headers de rate limiting
     */
    protected function addHeaders(Response $response, int $maxAttempts, int $remainingAttempts, ?int $retryAfter = null): Response
    {
        $response->headers->add([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
        ]);

        if ($retryAfter !== null) {
            $response->headers->set('Retry-After', $retryAfter);
            $response->headers->set('X-RateLimit-Reset', now()->addSeconds($retryAfter)->timestamp);
        }

        return $response;
    }

    /**
     * Calcule les tentatives restantes
     */
    protected function calculateRemainingAttempts(string $key, int $maxAttempts): int
    {
        return max(0, $maxAttempts - RateLimiter::attempts($key));
    }
}

/**
 * Middleware spécialisé pour les tentatives de connexion
 */
class LoginRateLimitMiddleware extends RateLimitMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        return parent::handle($request, $next, 'login');
    }
}

/**
 * Middleware spécialisé pour l'API
 */
class ApiRateLimitMiddleware extends RateLimitMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        return parent::handle($request, $next, 'api');
    }
}

/**
 * Middleware spécialisé pour les uploads
 */
class UploadRateLimitMiddleware extends RateLimitMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        return parent::handle($request, $next, 'upload');
    }
}

/**
 * Middleware spécialisé pour les recherches
 */
class SearchRateLimitMiddleware extends RateLimitMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        return parent::handle($request, $next, 'search');
    }
}
