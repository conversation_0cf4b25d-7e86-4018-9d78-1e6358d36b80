<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Supply;
use App\Models\Sale;
use App\Models\StockHistory;
use App\Services\StockService;
use App\Events\StockUpdated;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;

class StockTrackingTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $stockService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer un utilisateur admin pour les tests
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Admin Test'
        ]);
        
        $this->stockService = new StockService();
    }

    /** @test */
    public function it_can_update_stock_and_record_history()
    {
        // Créer un produit de test
        $product = Product::factory()->create([
            'name' => 'Produit Test',
            'stock_quantity' => 100,
            'price' => 1000
        ]);

        $initialStock = $product->stock_quantity;
        $quantityToAdd = 50;

        // Tester la mise à jour de stock
        $result = $this->stockService->updateStock($product, $quantityToAdd, 'supply');

        $this->assertTrue($result);
        
        // Vérifier que le stock a été mis à jour
        $product->refresh();
        $this->assertEquals($initialStock + $quantityToAdd, $product->stock_quantity);

        // Vérifier qu'un historique a été créé
        $this->assertDatabaseHas('stock_histories', [
            'product_id' => $product->id,
            'type' => 'supply',
            'quantity' => $quantityToAdd,
            'previous_stock' => $initialStock,
            'new_stock' => $initialStock + $quantityToAdd
        ]);
    }

    /** @test */
    public function it_triggers_stock_updated_event()
    {
        Event::fake();

        $product = Product::factory()->create([
            'stock_quantity' => 100
        ]);

        $this->stockService->updateStock($product, 25, 'supply');

        Event::assertDispatched(StockUpdated::class, function ($event) use ($product) {
            return $event->product->id === $product->id &&
                   $event->movement['type'] === 'supply' &&
                   $event->movement['quantity'] === 25;
        });
    }

    /** @test */
    public function it_can_get_stock_status()
    {
        // Créer des produits avec différents niveaux de stock
        Product::factory()->create(['stock_quantity' => 0, 'price' => 1000]); // Rupture
        Product::factory()->create(['stock_quantity' => 5, 'price' => 1500]);  // Stock faible
        Product::factory()->create(['stock_quantity' => 50, 'price' => 2000]); // Stock normal

        $stockStatus = $this->stockService->getStockStatus();

        $this->assertArrayHasKey('summary', $stockStatus);
        $this->assertArrayHasKey('products', $stockStatus);
        
        $summary = $stockStatus['summary'];
        $this->assertEquals(3, $summary['total_products']);
        $this->assertEquals(1, $summary['out_of_stock_count']);
        $this->assertEquals(1, $summary['low_stock_count']);
        $this->assertEquals(1, $summary['normal_stock_count']);
    }

    /** @test */
    public function it_can_get_recent_stock_movements()
    {
        $product = Product::factory()->create();
        
        // Créer quelques mouvements de stock
        StockHistory::factory()->count(5)->create([
            'product_id' => $product->id
        ]);

        $movements = $this->stockService->getRecentStockMovements(3);

        $this->assertCount(3, $movements);
        $this->assertArrayHasKey('product', $movements[0]);
        $this->assertArrayHasKey('type', $movements[0]);
        $this->assertArrayHasKey('quantity', $movements[0]);
    }

    /** @test */
    public function it_can_get_stock_alerts()
    {
        // Créer des produits avec stock faible et rupture
        Product::factory()->create(['stock_quantity' => 0]);
        Product::factory()->create(['stock_quantity' => 3]);
        Product::factory()->create(['stock_quantity' => 100]);

        $alerts = $this->stockService->getStockAlerts();

        $this->assertArrayHasKey('out_of_stock', $alerts);
        $this->assertArrayHasKey('low_stock', $alerts);
        $this->assertCount(1, $alerts['out_of_stock']);
        $this->assertCount(1, $alerts['low_stock']);
    }

    /** @test */
    public function dashboard_displays_stock_data()
    {
        $this->actingAs($this->admin);

        // Créer quelques produits
        Product::factory()->count(3)->create();

        $response = $this->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertViewHas('stockStatus');
        $response->assertViewHas('stockStats');
        $response->assertViewHas('recentStockMovements');
        $response->assertViewHas('stockAlerts');
    }

    /** @test */
    public function dashboard_stock_api_returns_json()
    {
        $this->actingAs($this->admin);

        Product::factory()->count(2)->create();

        $response = $this->get(route('admin.dashboard.stock-data'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'stock_status' => [
                'summary',
                'products'
            ],
            'recent_movements',
            'alerts',
            'timestamp'
        ]);
    }

    /** @test */
    public function supply_validation_updates_stock()
    {
        $this->actingAs($this->admin);

        $product = Product::factory()->create(['stock_quantity' => 10]);
        $supply = Supply::factory()->create(['status' => 'pending']);
        
        // Simuler la validation d'approvisionnement
        $initialStock = $product->stock_quantity;
        
        // Utiliser le StockService pour traiter la validation
        $result = $this->stockService->processSupplyValidation($supply);

        $this->assertTrue($result);
        
        // Vérifier qu'un historique a été créé
        $this->assertDatabaseHas('stock_histories', [
            'product_id' => $product->id,
            'type' => 'supply',
            'supply_id' => $supply->id
        ]);
    }
}
