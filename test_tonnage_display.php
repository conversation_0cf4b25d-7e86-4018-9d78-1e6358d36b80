<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "🏋️  Test de l'affichage des tonnages...\n\n";
    
    // Test du StockService avec conversion de tonnage
    $stockService = new App\Services\StockService();
    $stockStatus = $stockService->getStockStatus();
    
    echo "📊 RÉSUMÉ DES STOCKS EN TONNAGE:\n";
    echo "===============================\n";
    echo "Total produits: " . $stockStatus['summary']['total_products'] . "\n";
    echo "Tonnage total: " . $stockStatus['summary']['total_tonnage_display'] . "\n";
    echo "Tonnage brut: " . number_format($stockStatus['summary']['total_tonnage'], 3) . " tonnes\n";
    echo "Stock normal: " . $stockStatus['summary']['normal_stock_count'] . "\n";
    echo "Stock faible: " . $stockStatus['summary']['low_stock_count'] . "\n";
    echo "Rupture de stock: " . $stockStatus['summary']['out_of_stock_count'] . "\n";
    echo "Valeur totale: " . number_format($stockStatus['summary']['total_value'], 0, ',', ' ') . " FCFA\n\n";
    
    echo "📦 DÉTAIL DES PRODUITS:\n";
    echo "=======================\n";
    
    foreach ($stockStatus['products'] as $product) {
        $statusIcon = match($product['status']) {
            'out_of_stock' => '🔴',
            'low_stock' => '🟡',
            default => '🟢'
        };
        
        echo sprintf(
            "%s %-30s | %s | %s (%d %s) | %s FCFA\n",
            $statusIcon,
            $product['name'],
            $product['category'],
            $product['tonnage_display'],
            $product['current_stock'],
            $product['unit'],
            number_format($product['total_value'], 0, ',', ' ')
        );
    }
    
    echo "\n🔄 TEST DES CONVERSIONS:\n";
    echo "========================\n";
    
    // Créer quelques produits de test avec différentes unités
    $testProducts = [
        ['name' => 'Ciment Portland', 'quantity' => 100, 'unit' => 'sacs'],
        ['name' => 'Sable fin', 'quantity' => 10, 'unit' => 'm³'],
        ['name' => 'Gravier', 'quantity' => 8, 'unit' => 'm³'],
        ['name' => 'Fer à béton 12mm', 'quantity' => 2000, 'unit' => 'kg'],
        ['name' => 'Ciment en vrac', 'quantity' => 5, 'unit' => 'tonnes'],
        ['name' => 'Peinture', 'quantity' => 50, 'unit' => 'litres'],
    ];
    
    // Utiliser la méthode de conversion via réflexion
    $reflection = new ReflectionClass($stockService);
    $convertMethod = $reflection->getMethod('convertToTonnage');
    $convertMethod->setAccessible(true);
    
    foreach ($testProducts as $testProduct) {
        $conversion = $convertMethod->invoke($stockService, $testProduct['quantity'], $testProduct['unit']);
        
        echo sprintf(
            "%-25s: %3d %-8s → %s (%.3f T)\n",
            $testProduct['name'],
            $testProduct['quantity'],
            $testProduct['unit'],
            $conversion['display'],
            $conversion['raw_tonnage']
        );
    }
    
    echo "\n📋 RÈGLES DE CONVERSION:\n";
    echo "========================\n";
    echo "• 1 sac de ciment = 50 kg = 0.05 T\n";
    echo "• 1 m³ sable/gravier = 1.5 T\n";
    echo "• 1 kg = 0.001 T\n";
    echo "• 1 tonne = 1 T\n";
    echo "• 1 litre = 1 kg = 0.001 T\n";
    echo "• 1 unité (défaut) = 50 kg = 0.05 T\n\n";
    
    echo "🎯 AFFICHAGE INTELLIGENT:\n";
    echo "=========================\n";
    echo "• ≥ 1 tonne → Affichage en tonnes (ex: 2.5 T)\n";
    echo "• < 1 tonne → Affichage en kg (ex: 750 kg)\n\n";
    
    echo "✅ TESTS TERMINÉS!\n";
    echo "==================\n";
    echo "1. Videz le cache: php artisan view:clear\n";
    echo "2. Actualisez le dashboard: http://127.0.0.1:8000/admin/dashboard\n";
    echo "3. Vérifiez l'onglet 'État des Stocks'\n";
    echo "4. Les quantités devraient maintenant s'afficher en tonnage!\n\n";
    
    // Créer des produits de test avec différentes unités si nécessaire
    $productCount = App\Models\Product::count();
    if ($productCount === 0) {
        echo "⚠️  Aucun produit trouvé. Création de produits de test...\n";
        
        $category = App\Models\Category::firstOrCreate(
            ['name' => 'Matériaux'],
            ['description' => 'Matériaux de construction']
        );
        
        foreach ($testProducts as $testProduct) {
            App\Models\Product::create([
                'name' => $testProduct['name'],
                'slug' => Str::slug($testProduct['name']),
                'category_id' => $category->id,
                'stock_quantity' => $testProduct['quantity'],
                'unit' => $testProduct['unit'],
                'price' => rand(1000, 50000),
                'is_active' => true,
                'description' => 'Produit de test pour tonnage'
            ]);
        }
        
        echo "✅ Produits de test créés!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . "\n";
    echo "Ligne: " . $e->getLine() . "\n";
}
