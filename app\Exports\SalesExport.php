<?php

namespace App\Exports;

use App\Models\Sale;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class SalesExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->data['sales'];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Date',
            'Client',
            'Téléphone',
            'Produit',
            'Quantité (T)',
            'Prix Unitaire',
            'Remise/T',
            'Total',
            'Montant Payé',
            'Statut Paiement',
            'Ville',
            'Statut Livraison',
            'Créé par',
            'Statut'
        ];
    }

    /**
     * @param Sale $sale
     * @return array
     */
    public function map($sale): array
    {
        return [
            $sale->id,
            $sale->created_at->format('d/m/Y H:i'),
            $sale->customer_name,
            $sale->customer_phone,
            $sale->supply->details->first()->product->name ?? 'N/A',
            number_format($sale->quantity, 2),
            number_format($sale->unit_price, 0) . ' FCFA',
            number_format($sale->discount_per_ton ?? 0, 0) . ' FCFA',
            number_format($sale->total_amount, 0) . ' FCFA',
            number_format($sale->amount_paid, 0) . ' FCFA',
            $this->getPaymentStatus($sale->payment_status),
            $sale->city->name ?? 'N/A',
            $this->getDeliveryStatus($sale->delivery_status),
            $sale->createdBy->name ?? 'N/A',
            $this->getSaleStatus($sale->status)
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style pour l'en-tête
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF']
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '667eea']
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER
                ]
            ]
        ];
    }

    private function getPaymentStatus($status)
    {
        $statuses = [
            'pending' => 'En attente',
            'partial' => 'Partiel',
            'paid' => 'Payé',
            'overdue' => 'En retard'
        ];

        return $statuses[$status] ?? $status;
    }

    private function getDeliveryStatus($status)
    {
        $statuses = [
            'pending' => 'En attente',
            'in_progress' => 'En cours',
            'completed' => 'Terminé',
            'cancelled' => 'Annulé'
        ];

        return $statuses[$status] ?? $status;
    }

    private function getSaleStatus($status)
    {
        $statuses = [
            'pending' => 'En attente',
            'confirmed' => 'Confirmé',
            'cancelled' => 'Annulé',
            'completed' => 'Terminé'
        ];

        return $statuses[$status] ?? $status;
    }
}
