<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Graphiques GRADIS</title>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart-container { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🧪 Test des Graphiques GRADIS</h1>
    
    <div id="status" class="status">⏳ Chargement...</div>
    
    <div class="chart-container">
        <h3>📊 Graphique Test 1</h3>
        <div id="testChart1" style="height: 300px;"></div>
    </div>
    
    <div class="chart-container">
        <h3>📈 Graphique Test 2</h3>
        <div id="testChart2" style="height: 300px;"></div>
    </div>

    <script>
        console.log("🚀 Début du test des graphiques");
        
        // Vérifier que ApexCharts est chargé
        if (typeof ApexCharts === "undefined") {
            document.getElementById("status").innerHTML = "❌ ApexCharts non chargé";
            document.getElementById("status").className = "status error";
            console.error("❌ ApexCharts non disponible");
        } else {
            console.log("✅ ApexCharts chargé:", ApexCharts.version);
            document.getElementById("status").innerHTML = "✅ ApexCharts chargé (v" + ApexCharts.version + ")";
            document.getElementById("status").className = "status success";
            
            // Test graphique 1 - Simple
            try {
                const options1 = {
                    series: [{
                        name: "Test",
                        data: [10, 20, 15, 25, 30, 20]
                    }],
                    chart: {
                        type: "line",
                        height: 300
                    },
                    xaxis: {
                        categories: ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"]
                    }
                };
                
                const chart1 = new ApexCharts(document.querySelector("#testChart1"), options1);
                chart1.render();
                console.log("✅ Graphique 1 créé");
                
            } catch (error) {
                console.error("❌ Erreur graphique 1:", error);
            }
            
            // Test graphique 2 - Donut
            try {
                const options2 = {
                    series: [44, 55, 13, 43],
                    chart: {
                        type: "donut",
                        height: 300
                    },
                    labels: ["Ciment", "Fer", "Sable", "Gravier"]
                };
                
                const chart2 = new ApexCharts(document.querySelector("#testChart2"), options2);
                chart2.render();
                console.log("✅ Graphique 2 créé");
                
            } catch (error) {
                console.error("❌ Erreur graphique 2:", error);
            }
        }
    </script>
</body>
</html>