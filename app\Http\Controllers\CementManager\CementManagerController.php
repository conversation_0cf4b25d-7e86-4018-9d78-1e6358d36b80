<?php

namespace App\Http\Controllers\CementManager;

use App\Http\Controllers\Controller;
use App\Models\CementOrder;
use App\Models\CementOrderDetail;
use App\Models\Driver;
use App\Models\Trip;
use App\Models\TripAssignment;
use App\Models\Truck;
use App\Models\Stock;
use App\Models\CreditSale;
use App\Models\Supply;
use App\Models\Sale;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CementManagerController extends Controller
{
    public function index(Request $request)
    {
        // Récupérer les approvisionnements validés par l'admin
        $supplies = Supply::with([
            'supplier', 
            'details.product.category', 
            'details.product.prices',
            'cities.city',
            'createdBy',
            'validator'
        ])
            ->where('status', 'validated')
            ->whereNotNull('validator_id')
            ->orderBy('created_at', 'desc')
            ->get();

        // Calculer le montant total de vente pour chaque approvisionnement
        $supplies->each(function ($supply) {
            $totalSaleAmount = 0;
            foreach ($supply->cities as $city) {
                $item = $supply->details->first();
                if (!$item || !$city->city_id || !$item->product_id) continue;
                
                $price = $item->product->prices
                    ->where('city_id', $city->city_id)
                    ->first();
                    
                $salePrice = $price ? $price->price : $item->product->price;
                $quantity = $city->quantity ?? 0;
                $totalSaleAmount += $quantity * $salePrice;
            }
            $supply->total_sale_amount = $totalSaleAmount;
        });

        // Statistiques des approvisionnements
        $stats = [
            'total_supplies' => $supplies->count(),
            'total_amount' => $supplies->sum('total_sale_amount'),
            'total_products' => $supplies->sum(function ($supply) {
                return $supply->details->count();
            })
        ];

        // Ajouter les statistiques pour les graphiques (similaire à la méthode dashboard)
        $totalSales = DB::table('sales')
            ->where('created_by', auth()->id())
            ->count();

        $totalRevenue = DB::table('sales')
            ->where('created_by', auth()->id())
            ->sum('total_amount');

        $salesPending = DB::table('sales')
            ->where('created_by', auth()->id())
            ->where('status', 'pending')
            ->count();

        $salesCompleted = DB::table('sales')
            ->where('created_by', auth()->id())
            ->where('status', 'completed')
            ->count();

        // Ventes récentes
        $recentSales = Sale::with(['supply', 'city'])
            ->where('created_by', auth()->id())
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Générer des références temporaires pour l'affichage si elles n'existent pas
        foreach ($recentSales as $sale) {
            if (empty($sale->reference)) {
                $sale->reference = 'VNT-' . str_pad($sale->id, 6, '0', STR_PAD_LEFT);
            }
        }

        // Ventes par mois (pour le graphique)
        $salesByMonth = DB::table('sales')
            ->select(DB::raw('MONTH(created_at) as month'), DB::raw('SUM(total_amount) as total'))
            ->where('created_by', auth()->id())
            ->whereYear('created_at', date('Y'))
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy('month')
            ->get()
            ->keyBy('month')
            ->map(function ($item) {
                return $item->total;
            })
            ->toArray();

        // Compléter les mois manquants
        $monthsData = [];
        $monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];

        for ($i = 1; $i <= 12; $i++) {
            $monthsData[$i] = [
                'name' => $monthNames[$i-1],
                'value' => $salesByMonth[$i] ?? 0
            ];
        }

        // Statistiques pour le tableau de bord
        $dashboardStats = [
            'total_sales' => $totalSales,
            'total_revenue' => $totalRevenue,
            'sales_pending' => $salesPending,
            'sales_completed' => $salesCompleted,
            'recent_sales' => $recentSales,
            'sales_by_month' => $monthsData
        ];

        return view('cement-manager.dashboard', compact('supplies', 'stats', 'dashboardStats'));
    }
    
    public function dashboard(Request $request)
    {
        // Statistiques des ventes
        $totalSales = DB::table('sales')
            ->where('created_by', auth()->id())
            ->count();
            
        $totalRevenue = DB::table('sales')
            ->where('created_by', auth()->id())
            ->sum('total_amount');
        
        $salesPending = DB::table('sales')
            ->where('created_by', auth()->id())
            ->where('status', 'pending')
            ->count();
            
        $salesCompleted = DB::table('sales')
            ->where('created_by', auth()->id())
            ->where('status', 'completed')
            ->count();
        
        // Statistiques des livraisons
        $deliveriesPending = DB::table('sales')
            ->where('created_by', auth()->id())
            ->where('delivery_status', 'pending')
            ->count();
            
        $deliveriesInProgress = DB::table('sales')
            ->where('created_by', auth()->id())
            ->where('delivery_status', 'in_progress')
            ->count();
            
        $deliveriesCompleted = DB::table('sales')
            ->where('created_by', auth()->id())
            ->where('delivery_status', 'completed')
            ->count();
        
        // Statistiques des camions et chauffeurs
        $totalTrucks = Truck::count();
        $availableTrucks = Truck::where('status', 'available')->count();
        $busyTrucks = Truck::where('status', 'busy')->count();
        $maintenanceTrucks = Truck::where('status', 'maintenance')->count();
        
        $totalDrivers = Driver::count();
        $availableDrivers = Driver::where('status', 'available')->count();
        
        // Statistiques des approvisionnements
        $totalSupplies = Supply::where('supplies.status', 'validated')->count();
        $totalSupplyAmount = Supply::where('supplies.status', 'validated')
            ->join('supply_details', 'supplies.id', '=', 'supply_details.supply_id')
            ->sum(DB::raw('supply_details.quantity * supply_details.unit_price'));
        
        // Ventes récentes
        $recentSales = Sale::with(['supply', 'city'])
            ->where('created_by', auth()->id())
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
            
        // Générer des références temporaires pour l'affichage si elles n'existent pas
        foreach ($recentSales as $sale) {
            if (empty($sale->reference)) {
                $sale->reference = 'VNT-' . str_pad($sale->id, 6, '0', STR_PAD_LEFT);
            }
        }
            
        // Ventes par mois (pour le graphique)
        $salesByMonth = DB::table('sales')
            ->select(DB::raw('MONTH(created_at) as month'), DB::raw('SUM(total_amount) as total'))
            ->where('created_by', auth()->id())
            ->whereYear('created_at', date('Y'))
            ->groupBy(DB::raw('MONTH(created_at)'))
            ->orderBy('month')
            ->get()
            ->keyBy('month')
            ->map(function ($item) {
                return $item->total;
            })
            ->toArray();
            
        // Compléter les mois manquants
        $monthsData = [];
        $monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
        
        for ($i = 1; $i <= 12; $i++) {
            $monthsData[$i] = [
                'name' => $monthNames[$i-1],
                'value' => $salesByMonth[$i] ?? 0
            ];
        }
        
        // Statistiques pour le tableau de bord
        $dashboardStats = [
            'total_sales' => $totalSales,
            'total_revenue' => $totalRevenue,
            'sales_pending' => $salesPending,
            'sales_completed' => $salesCompleted,
            'deliveries_pending' => $deliveriesPending,
            'deliveries_in_progress' => $deliveriesInProgress,
            'deliveries_completed' => $deliveriesCompleted,
            'total_trucks' => $totalTrucks,
            'available_trucks' => $availableTrucks,
            'busy_trucks' => $busyTrucks,
            'maintenance_trucks' => $maintenanceTrucks,
            'total_drivers' => $totalDrivers,
            'available_drivers' => $availableDrivers,
            'total_supplies' => $totalSupplies,
            'total_supply_amount' => $totalSupplyAmount,
            'recent_sales' => $recentSales,
            'sales_by_month' => $monthsData
        ];
        
        return view('cement-manager.dashboard', compact('dashboardStats'));
    }

    public function createAssignment(CementOrder $order)
    {
        // Vérifier si la commande est en attente
        if ($order->status !== 'pending') {
            return redirect()->back()->with('error', 'Cette commande a déjà été traitée.');
        }

        // Charger les relations nécessaires
        $order->load([
            'product',
            'details.destination',
            'details.customer',
            'details.product'
        ]);

        // Récupérer les camions disponibles
        $trucks = Truck::with('capacity')
            ->where('status', 'available')
            ->get();

        // Récupérer les chauffeurs disponibles
        $drivers = Driver::where('status', 'available')->get();

        // Récupérer les affectations existantes
        $existingAssignments = TripAssignment::with(['truck', 'driver'])
            ->whereIn('cement_order_detail_id', $order->details->pluck('id'))
            ->get();

        return view('cement-manager.assignments.create', compact('order', 'trucks', 'drivers', 'existingAssignments'));
    }

    public function storeAssignment(Request $request, CementOrder $order)
    {
        // Validation des données
        $request->validate([
            'cement_order_detail_id' => 'required|exists:cement_order_details,id',
            'truck_id' => 'required|exists:trucks,id',
            'driver_id' => 'required|exists:drivers,id',
            'trip_number' => 'required|integer|min:1',
            'start_date' => [
                'required',
                'date',
                'after_or_equal:' . now()->format('Y-m-d H:i:s')
            ],
            'end_date' => [
                'required',
                'date',
                'after:start_date'
            ]
        ]);

        try {
            DB::beginTransaction();

            // Récupérer le détail de la commande
            $detail = CementOrderDetail::findOrFail($request->cement_order_detail_id);

            // Vérifier que le détail appartient bien à la commande
            if ($detail->cement_order_id !== $order->id) {
                throw new \Exception('Le détail ne correspond pas à cette commande.');
            }

            // Vérifier si une affectation existe déjà pour ce voyage
            $existingAssignment = TripAssignment::where('cement_order_detail_id', $detail->id)
                ->where('trip_number', $request->trip_number)
                ->first();

            if ($existingAssignment) {
                throw new \Exception('Une affectation existe déjà pour ce voyage.');
            }

            // Vérifier si le camion est disponible pour la période
            $conflictingAssignment = TripAssignment::where('truck_id', $request->truck_id)
                ->where(function($query) use ($request) {
                    $query->whereBetween('start_date', [$request->start_date, $request->end_date])
                        ->orWhereBetween('end_date', [$request->start_date, $request->end_date]);
                })
                ->first();

            if ($conflictingAssignment) {
                throw new \Exception('Le camion est déjà affecté pendant cette période.');
            }

            // Vérifier si le chauffeur est disponible pour la période
            $conflictingDriver = TripAssignment::where('driver_id', $request->driver_id)
                ->where(function($query) use ($request) {
                    $query->whereBetween('start_date', [$request->start_date, $request->end_date])
                        ->orWhereBetween('end_date', [$request->start_date, $request->end_date]);
                })
                ->first();

            if ($conflictingDriver) {
                throw new \Exception('Le chauffeur est déjà affecté pendant cette période.');
            }

            // Créer l'affectation
            $assignment = TripAssignment::create([
                'cement_order_detail_id' => $detail->id,
                'truck_id' => $request->truck_id,
                'driver_id' => $request->driver_id,
                'trip_number' => $request->trip_number,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'status' => 'pending'
            ]);

            DB::commit();

            return redirect()->back()->with('success', 'Affectation créée avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Erreur lors de la création de l\'affectation : ' . $e->getMessage());
        }
    }

    public function getAvailableTrucks()
    {
        try {
            $trucks = Truck::with(['driver', 'capacity'])
                ->where('status', 'available')
                ->get()
                ->map(function ($truck) {
                    return [
                        'id' => $truck->id,
                        'registration_number' => $truck->registration_number,
                        'brand' => $truck->brand,
                        'model' => $truck->model,
                        'capacity' => $truck->capacity ? [
                            'id' => $truck->capacity->id,
                            'tonnage' => $truck->capacity->tonnage
                        ] : null,
                        'driver' => $truck->driver ? [
                            'id' => $truck->driver->id,
                            'first_name' => $truck->driver->first_name,
                            'last_name' => $truck->driver->last_name,
                            'phone' => $truck->driver->phone
                        ] : null
                    ];
                });

            return response()->json([
                'success' => true,
                'trucks' => $trucks
            ]);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des camions', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des camions'
            ], 500);
        }
    }

    public function listAssignments()
    {
        $assignments = TripAssignment::with([
            'order',
            'detail.product',
            'detail.destination',
            'detail.customer',
            'trip.truck',
            'trip.driver'
        ])
            ->latest()
            ->paginate(10);

        return view('cement-manager.assignments.index', compact('assignments'));
    }

    public function confirmDelivery(Request $request, CementOrderDetail $detail)
    {
        try {
            DB::beginTransaction();

            // Valider la requête
            $validated = $request->validate([
                'notes' => 'nullable|string'
            ]);

            // Vérifier que le détail n'a pas déjà été confirmé
            if ($detail->creditSales()->exists()) {
                throw new \Exception('Cette livraison a déjà été confirmée.');
            }

            // Créer la vente à crédit pour chaque affectation
            foreach ($detail->assignments as $assignment) {
                $creditSale = new CreditSale([
                    'cement_order_id' => $detail->cement_order_id,
                    'cement_order_detail_id' => $detail->id,
                    'trip_assignment_id' => $assignment->id,
                    'quantity' => $assignment->tonnage,
                    'unit_price' => $detail->unit_price,
                    'total_amount' => $assignment->tonnage * $detail->unit_price,
                    'status' => 'pending_payment',
                    'notes' => $validated['notes'] ?? null
                ]);

                $creditSale->save();

                // Mettre à jour le statut de l'affectation
                $assignment->update([
                    'status' => 'completed',
                    'completed_at' => now()
                ]);

                // Mettre à jour le stock
                $stock = Stock::firstOrCreate([
                    'product_id' => $detail->product_id,
                    'supplier_id' => $detail->supplier_id
                ]);

                $stock->quantity -= $assignment->tonnage;
                $stock->save();

                // Libérer le camion et le chauffeur
                $assignment->truck->update(['status' => 'available']);
                $assignment->driver->update(['status' => 'available']);
            }

            // Mettre à jour les quantités dans le détail de la commande
            $detail->delivered_quantity = $detail->assignments->sum('tonnage');
            $detail->remaining_quantity = max(0, $detail->total_tonnage - $detail->delivered_quantity);
            $detail->save();

            DB::commit();

            return redirect()->route('cement-manager.dashboard')
                ->with('success', 'Livraison confirmée avec succès et stock mis à jour.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Une erreur est survenue : ' . $e->getMessage()]);
        }
    }

    public function showSupply(Supply $supply)
    {
        // Vérifier que l'approvisionnement est validé
        if ($supply->status !== 'validated' || !$supply->validator_id) {
            return redirect()->route('cement-manager.dashboard')
                ->with('error', 'Cet approvisionnement n\'est pas validé.');
        }

        // Charger les relations nécessaires avec les véhicules et les chauffeurs
        $supply->load([
            'supplier:id,name',
            'details.product:id,name,category_id,unit',
            'details.product.category:id,name',
            'details.product.prices',
            'validator:id,name',
            'cities.city:id,name',
            'cities.vehicle:id,registration_number',
            'cities.driver:id,first_name,last_name'
        ]);

        // S'assurer que les relations sont chargées
        if (!$supply->relationLoaded('details') || !$supply->relationLoaded('cities')) {
            return redirect()->route('cement-manager.dashboard')
                ->with('error', 'Impossible de charger les détails de l\'approvisionnement.');
        }

        // Initialiser les tableaux pour stocker les prix
        $cityPrices = [];
        $totalAmount = 0;
        
        // Charger d'abord tous les prix de vente par produit et par ville
        foreach ($supply->details as $detail) {
            if (!$detail->product) continue;
            
            foreach ($supply->cities as $supplyCity) {
                if (!$supplyCity->city_id) continue;
                
                $price = $detail->product->prices
                    ->where('city_id', $supplyCity->city_id)
                    ->first();
                    
                $currentPrice = $price ? $price->price : $detail->product->price;
                $cityPrices[$supplyCity->city_id][$detail->product_id] = $currentPrice;
                
                // Calculer le montant total
                $quantity = $supplyCity->quantity ?? 0;
                $totalAmount += $quantity * $currentPrice;
            }
        }
        
        // Mettre à jour le total_amount dans la base de données
        $supply->update(['total_amount' => $totalAmount]);
        
        // Charger les véhicules avec leurs capacités et chauffeurs
        $vehicleIds = $supply->cities->pluck('vehicle_id')->filter();
        $vehicles = Truck::with(['capacity', 'driver'])
            ->whereIn('id', $vehicleIds)
            ->get()
            ->keyBy('id');

        foreach ($supply->cities as $city) {
            if ($city->vehicle_id && isset($vehicles[$city->vehicle_id])) {
                $city->vehicle = $vehicles[$city->vehicle_id];
            }

            // Utiliser les prix déjà chargés
            $city->prices = $cityPrices[$city->city_id] ?? [];
        }

        return view('cement-manager.supplies.show', compact('supply'));
    }
}
