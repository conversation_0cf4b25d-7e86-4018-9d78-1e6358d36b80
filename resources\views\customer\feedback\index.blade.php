@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">📝 Mes Avis & Suggestions</h2>
                    <p class="text-muted mb-0"><PERSON><PERSON>rez vos feedbacks et suivez les réponses</p>
                </div>
                <a href="{{ route('customer.feedback.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nouveau Feedback
                </a>
            </div>

            <!-- Filtres -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <select name="type" class="form-select">
                                <option value="">Tous les types</option>
                                <option value="delivery" {{ request('type') == 'delivery' ? 'selected' : '' }}>Livraison</option>
                                <option value="service" {{ request('type') == 'service' ? 'selected' : '' }}>Service</option>
                                <option value="product" {{ request('type') == 'product' ? 'selected' : '' }}>Produit</option>
                                <option value="general" {{ request('type') == 'general' ? 'selected' : '' }}>Général</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="category" class="form-select">
                                <option value="">Toutes les catégories</option>
                                <option value="suggestion" {{ request('category') == 'suggestion' ? 'selected' : '' }}>Suggestion</option>
                                <option value="complaint" {{ request('category') == 'complaint' ? 'selected' : '' }}>Réclamation</option>
                                <option value="compliment" {{ request('category') == 'compliment' ? 'selected' : '' }}>Compliment</option>
                                <option value="question" {{ request('category') == 'question' ? 'selected' : '' }}>Question</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">Tous les statuts</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>En attente</option>
                                <option value="reviewed" {{ request('status') == 'reviewed' ? 'selected' : '' }}>Examiné</option>
                                <option value="resolved" {{ request('status') == 'resolved' ? 'selected' : '' }}>Résolu</option>
                                <option value="closed" {{ request('status') == 'closed' ? 'selected' : '' }}>Fermé</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-filter me-1"></i>Filtrer
                            </button>
                            <a href="{{ route('customer.feedback.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Liste des feedbacks -->
            @if($feedbacks->count() > 0)
                <div class="row">
                    @foreach($feedbacks as $feedback)
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 feedback-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="badge bg-{{ $feedback->status_badge }}">
                                        {{ ucfirst($feedback->status) }}
                                    </span>
                                    <span class="badge bg-{{ $feedback->priority_badge }} ms-1">
                                        {{ ucfirst($feedback->priority) }}
                                    </span>
                                </div>
                                <small class="text-muted">{{ $feedback->created_at->format('d/m/Y') }}</small>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">{{ $feedback->subject }}</h6>
                                <p class="card-text text-muted small">
                                    {{ Str::limit($feedback->message, 100) }}
                                </p>
                                
                                <!-- Ratings si disponibles -->
                                @if($feedback->overall_rating)
                                <div class="mb-2">
                                    <small class="text-muted">Note globale:</small>
                                    <div class="rating-display">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star {{ $i <= $feedback->overall_rating ? 'text-warning' : 'text-muted' }}"></i>
                                        @endfor
                                    </div>
                                </div>
                                @endif

                                <!-- Informations sur la commande -->
                                @if($feedback->cementOrderDetail)
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-box me-1"></i>
                                        Commande #{{ $feedback->cementOrderDetail->id }}
                                    </small>
                                </div>
                                @endif

                                <!-- Type et catégorie -->
                                <div class="mb-2">
                                    <span class="badge bg-light text-dark">{{ ucfirst($feedback->type) }}</span>
                                    <span class="badge bg-light text-dark">{{ ucfirst($feedback->category) }}</span>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="{{ route('customer.feedback.show', $feedback) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>Voir détails
                                    </a>
                                    @if($feedback->admin_response)
                                        <small class="text-success">
                                            <i class="fas fa-reply me-1"></i>Répondu
                                        </small>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $feedbacks->links() }}
                </div>
            @else
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                        <h5>Aucun feedback trouvé</h5>
                        <p class="text-muted">Vous n'avez pas encore donné d'avis. Commencez par partager votre expérience !</p>
                        <a href="{{ route('customer.feedback.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Donner mon premier avis
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('styles')
<style>
    .feedback-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .feedback-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }
    .rating-display {
        display: inline-block;
    }
    .rating-display .fa-star {
        font-size: 12px;
    }
</style>
@endpush
@endsection
