<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "🏷️  Test des étiquettes de stock...\n\n";
    
    // Créer le service de stock
    $stockService = new App\Services\StockService();
    
    // Obtenir les données de stock
    $stockStatus = $stockService->getStockStatus();
    
    echo "📊 RÉSUMÉ DES STOCKS:\n";
    echo "==================\n";
    echo "Total produits: " . $stockStatus['summary']['total_products'] . "\n";
    echo "Stock normal: " . $stockStatus['summary']['normal_stock_count'] . "\n";
    echo "Stock faible: " . $stockStatus['summary']['low_stock_count'] . "\n";
    echo "Rupture de stock: " . $stockStatus['summary']['out_of_stock_count'] . "\n";
    echo "Valeur totale: " . number_format($stockStatus['summary']['total_value'], 0, ',', ' ') . " FCFA\n\n";
    
    echo "🏷️  APERÇU DES ÉTIQUETTES:\n";
    echo "=========================\n";
    
    foreach ($stockStatus['products'] as $product) {
        $statusIcon = '';
        $statusColor = '';
        
        switch ($product['status']) {
            case 'out_of_stock':
                $statusIcon = '🔴';
                $statusColor = 'ROUGE';
                break;
            case 'low_stock':
                $statusIcon = '🟡';
                $statusColor = 'ORANGE';
                break;
            default:
                $statusIcon = '🟢';
                $statusColor = 'VERT';
        }
        
        echo sprintf(
            "%s %s\n   Stock: %s %s (%s)\n   Valeur: %s FCFA\n   Statut: %s\n\n",
            $statusIcon,
            $product['name'],
            $product['current_stock'],
            $product['unit'],
            $statusColor,
            number_format($product['total_value'], 0, ',', ' '),
            ucfirst(str_replace('_', ' ', $product['status']))
        );
    }
    
    echo "✅ Les étiquettes devraient maintenant s'afficher avec:\n";
    echo "   • Couleurs selon le statut (Rouge/Orange/Vert)\n";
    echo "   • Icônes pour chaque type de statut\n";
    echo "   • Badges stylisés pour le stock et la valeur\n";
    echo "   • Effets de survol (hover)\n\n";
    
    echo "🌐 Testez maintenant sur: http://127.0.0.1:8000/admin/dashboard\n";
    echo "   Allez dans l'onglet 'État des Stocks' pour voir les nouvelles étiquettes!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . "\n";
    echo "Ligne: " . $e->getLine() . "\n";
}
