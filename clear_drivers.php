<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Driver;
use App\Models\Truck;
use Illuminate\Support\Facades\DB;

// Initialiser Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== SCRIPT DE VIDAGE DES TABLES DRIVERS ET TRUCKS ===\n\n";

try {
    // Compter les enregistrements
    $driversCount = Driver::withTrashed()->count();
    $trucksCount = Truck::withTrashed()->count();

    if ($driversCount === 0 && $trucksCount === 0) {
        echo "✅ Les tables drivers et trucks sont déjà vides.\n";
        exit(0);
    }

    echo "Nombre total de chauffeurs trouvés : {$driversCount}\n";
    echo "Nombre total de camions trouvés : {$trucksCount}\n";
    echo "⚠️  ATTENTION : Cette action va supprimer DÉFINITIVEMENT tous les chauffeurs ET camions !\n";
    echo "⚠️  Cela supprimera aussi automatiquement tous les trips et assignments liés !\n";
    echo "Tapez 'OUI' pour confirmer : ";

    $handle = fopen("php://stdin", "r");
    $confirmation = trim(fgets($handle));
    fclose($handle);

    if (strtoupper($confirmation) !== 'OUI') {
        echo "❌ Opération annulée.\n";
        exit(0);
    }

    echo "\n🔄 Début du processus de nettoyage simultané...\n";

    DB::beginTransaction();

    // Étape 1: Supprimer tous les chauffeurs et camions
    echo "1. Suppression des chauffeurs et camions...\n";
    Driver::withTrashed()->forceDelete();
    echo "   → Tous les chauffeurs supprimés\n";
    Truck::withTrashed()->forceDelete();
    echo "   → Tous les camions supprimés\n";

    // Étape 2: Reset des auto-increments
    echo "2. Reset des auto-increments...\n";
    DB::statement('ALTER TABLE drivers AUTO_INCREMENT = 1');
    DB::statement('ALTER TABLE trucks AUTO_INCREMENT = 1');
    echo "   → Auto-increments remis à 1\n";

    DB::commit();

    echo "\n✅ SUCCÈS ! Les tables drivers et trucks ont été vidées complètement.\n";
    echo "📊 Résumé :\n";
    echo "   - {$driversCount} chauffeurs supprimés définitivement\n";
    echo "   - {$trucksCount} camions supprimés définitivement\n";
    echo "   - Tous les trips et assignments liés supprimés automatiquement\n";
    echo "   - Auto-increments remis à 1\n";

} catch (Exception $e) {
    DB::rollBack();
    echo "\n❌ ERREUR : " . $e->getMessage() . "\n";
    exit(1);
}
