<?php

namespace App\Http\Controllers\CustomerService;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Ticket;
use App\Models\User;

class TicketController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:customer_service']);
    }

    public function index()
    {
        $tickets = Ticket::with(['customer', 'assignedTo'])
            ->latest()
            ->paginate(15);

        return view('customer-service.tickets.index', compact('tickets'));
    }

    public function show(Ticket $ticket)
    {
        $ticket->load(['customer', 'assignedTo', 'messages']);
        
        return view('customer-service.tickets.show', compact('ticket'));
    }

    public function create()
    {
        $customers = User::role('customer')->get();
        
        return view('customer-service.tickets.create', compact('customers'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:users,id',
            'subject' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:low,medium,high,urgent',
            'category' => 'required|string|max:100',
        ]);

        $validated['assigned_to'] = auth()->id();
        $validated['status'] = 'open';

        $ticket = Ticket::create($validated);

        return redirect()->route('customer-service.tickets.show', $ticket)
            ->with('success', 'Ticket créé avec succès.');
    }

    public function update(Request $request, Ticket $ticket)
    {
        $validated = $request->validate([
            'status' => 'required|in:open,in_progress,resolved,closed',
            'priority' => 'required|in:low,medium,high,urgent',
            'notes' => 'nullable|string',
        ]);

        $ticket->update($validated);

        return redirect()->route('customer-service.tickets.show', $ticket)
            ->with('success', 'Ticket mis à jour avec succès.');
    }
}
