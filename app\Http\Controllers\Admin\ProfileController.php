<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    public function show()
    {
        return view('admin.profile.show', [
            'user' => Auth::user()
        ]);
    }

    public function edit()
    {
        return view('admin.profile.edit', [
            'user' => Auth::user()
        ]);
    }

    public function update(Request $request)
    {
        // Récupérer l'utilisateur connecté
        $user = auth()->user();
        
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'avatar' => ['nullable', 'image', 'max:2048'], // Max 2MB
            'phone' => ['nullable', 'string', 'max:20'],
            'position' => ['nullable', 'string', 'max:100'],
        ]);
        
        if ($request->hasFile('avatar')) {
            // Créer le dossier s'il n'existe pas
            $uploadPath = 'uploads/avatars/admin';
            if (!file_exists(public_path($uploadPath))) {
                mkdir(public_path($uploadPath), 0777, true);
            }
            
            // Supprimer l'ancienne image si elle existe
            if ($user->avatar && file_exists(public_path($user->avatar))) {
                // Suppression de l'ancien avatar
                unlink(public_path($user->avatar));
            }

            // Générer un nom de fichier unique
            $avatarName = 'admin_' . $user->id . '_' . time() . '.' . $request->file('avatar')->getClientOriginalExtension();
            
            // Déplacer le fichier
            $request->file('avatar')->move(public_path($uploadPath), $avatarName);
            
            // Enregistrer le chemin relatif dans la base de données
            $user->avatar = $uploadPath . '/' . $avatarName;
            // Nouvel avatar enregistré
        }

        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->phone = $validated['phone'] ?? $user->phone;
        $user->position = $validated['position'] ?? $user->position;
        
        $result = $user->save();
        
        // Profil mis à jour

        return redirect()->route('admin.profile.show')
            ->with('success', 'Profil mis à jour avec succès.');
    }

    public function showPasswordForm()
    {
        return view('admin.profile.password');
    }

    public function updatePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        Auth::user()->update([
            'password' => Hash::make($validated['password']),
        ]);

        return redirect()->route('admin.profile.show')
            ->with('success', 'Mot de passe mis à jour avec succès.');
    }
}
