/**
 * GRADIS Admin - Diagnostic de Performance
 * Script pour identifier les sources de blocage de 40 secondes
 * Version: 1.0 - Diagnostic complet
 */

console.log('🔍 GRADIS Admin - Diagnostic de performance chargé');

/**
 * Classe principale de diagnostic
 */
class AdminPerformanceDiagnostic {
    constructor() {
        this.startTime = performance.now();
        this.intervals = new Set();
        this.timeouts = new Set();
        this.requests = new Set();
        this.diagnosticData = {
            intervals: [],
            timeouts: [],
            requests: [],
            scripts: [],
            errors: []
        };
        
        this.init();
    }
    
    init() {
        console.log('🚀 Initialisation du diagnostic de performance...');
        
        // Intercepter les timers
        this.interceptTimers();
        
        // Intercepter les requêtes
        this.interceptRequests();
        
        // Surveiller les scripts
        this.monitorScripts();
        
        // Surveiller les erreurs
        this.monitorErrors();
        
        // Créer l'interface de diagnostic
        this.createDiagnosticUI();
        
        // Démarrer le monitoring
        this.startMonitoring();
        
        console.log('✅ Diagnostic de performance initialisé');
    }
    
    interceptTimers() {
        const self = this;
        
        // Intercepter setInterval
        const originalSetInterval = window.setInterval;
        window.setInterval = function(callback, delay, ...args) {
            const stackTrace = new Error().stack;
            const intervalData = {
                delay: delay,
                created: Date.now(),
                stack: stackTrace,
                type: 'interval'
            };
            
            self.diagnosticData.intervals.push(intervalData);
            
            if (delay < 1000) {
                console.warn('⚠️ setInterval court détecté:', delay, 'ms');
                console.log('Stack trace:', stackTrace);
            }
            
            const id = originalSetInterval.call(this, callback, delay, ...args);
            self.intervals.add(id);
            return id;
        };
        
        // Intercepter setTimeout
        const originalSetTimeout = window.setTimeout;
        window.setTimeout = function(callback, delay, ...args) {
            const stackTrace = new Error().stack;
            const timeoutData = {
                delay: delay,
                created: Date.now(),
                stack: stackTrace,
                type: 'timeout'
            };
            
            self.diagnosticData.timeouts.push(timeoutData);
            
            if (delay > 30000) {
                console.warn('⚠️ setTimeout très long détecté:', delay, 'ms');
                console.log('Stack trace:', stackTrace);
            }
            
            const id = originalSetTimeout.call(this, callback, delay, ...args);
            self.timeouts.add(id);
            return id;
        };
    }
    
    interceptRequests() {
        const self = this;
        
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function(url, options = {}) {
                const requestStart = performance.now();
                const requestData = {
                    url: url,
                    started: requestStart,
                    options: options
                };
                
                self.diagnosticData.requests.push(requestData);
                self.requests.add(requestData);
                
                console.log('🌐 Requête démarrée:', url);
                
                return originalFetch(url, options)
                    .then(response => {
                        const duration = performance.now() - requestStart;
                        requestData.duration = duration;
                        requestData.status = response.status;
                        
                        if (duration > 5000) {
                            console.warn('⚠️ Requête lente:', url, duration, 'ms');
                        }
                        
                        self.requests.delete(requestData);
                        return response;
                    })
                    .catch(error => {
                        const duration = performance.now() - requestStart;
                        requestData.duration = duration;
                        requestData.error = error.message;
                        
                        console.error('❌ Erreur de requête:', url, error.message);
                        
                        self.requests.delete(requestData);
                        throw error;
                    });
            };
        }
    }
    
    monitorScripts() {
        const scripts = document.querySelectorAll('script[src]');
        scripts.forEach(script => {
            this.diagnosticData.scripts.push({
                src: script.src,
                loaded: script.readyState === 'complete',
                defer: script.defer,
                async: script.async
            });
        });
        
        console.log('📜 Scripts détectés:', this.diagnosticData.scripts.length);
    }
    
    monitorErrors() {
        const self = this;
        
        window.addEventListener('error', function(event) {
            const errorData = {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                timestamp: Date.now()
            };
            
            self.diagnosticData.errors.push(errorData);
            console.error('❌ Erreur JavaScript détectée:', errorData);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            const errorData = {
                message: event.reason?.message || 'Promise rejection',
                type: 'unhandledrejection',
                timestamp: Date.now()
            };
            
            self.diagnosticData.errors.push(errorData);
            console.error('❌ Promise rejetée:', errorData);
        });
    }
    
    createDiagnosticUI() {
        const diagnosticPanel = document.createElement('div');
        diagnosticPanel.id = 'admin-diagnostic-panel';
        diagnosticPanel.style.cssText = `
            position: fixed;
            top: 50px;
            right: 10px;
            width: 300px;
            max-height: 400px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            z-index: 10000;
            font-family: monospace;
            font-size: 12px;
            overflow-y: auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        `;
        
        diagnosticPanel.innerHTML = `
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                <h4 style="margin: 0; color: #007bff;">🔍 Diagnostic Admin</h4>
                <button onclick="this.parentNode.parentNode.style.display='none'" style="background: #dc3545; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer;">×</button>
            </div>
            <div id="diagnostic-content">
                <div>⏱️ Temps écoulé: <span id="elapsed-time">0</span>s</div>
                <div>🔄 Intervals actifs: <span id="active-intervals">0</span></div>
                <div>⏰ Timeouts actifs: <span id="active-timeouts">0</span></div>
                <div>🌐 Requêtes en cours: <span id="active-requests">0</span></div>
                <div>❌ Erreurs: <span id="error-count">0</span></div>
                <hr>
                <div id="diagnostic-details"></div>
            </div>
        `;
        
        document.body.appendChild(diagnosticPanel);
        
        // Bouton pour générer un rapport
        const reportButton = document.createElement('button');
        reportButton.innerHTML = '📊 Rapport Complet';
        reportButton.style.cssText = `
            width: 100%;
            background: #28a745;
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        `;
        reportButton.addEventListener('click', () => this.generateReport());
        diagnosticPanel.appendChild(reportButton);
    }
    
    startMonitoring() {
        setInterval(() => {
            this.updateDiagnosticUI();
        }, 1000);
    }
    
    updateDiagnosticUI() {
        const elapsedTime = Math.round((performance.now() - this.startTime) / 1000);
        
        document.getElementById('elapsed-time').textContent = elapsedTime;
        document.getElementById('active-intervals').textContent = this.intervals.size;
        document.getElementById('active-timeouts').textContent = this.timeouts.size;
        document.getElementById('active-requests').textContent = this.requests.size;
        document.getElementById('error-count').textContent = this.diagnosticData.errors.length;
        
        // Afficher les détails des problèmes
        const details = document.getElementById('diagnostic-details');
        let detailsHTML = '';
        
        // Intervals problématiques
        const problematicIntervals = this.diagnosticData.intervals.filter(i => i.delay < 1000);
        if (problematicIntervals.length > 0) {
            detailsHTML += `<div style="color: #dc3545;">⚠️ ${problematicIntervals.length} intervals courts détectés</div>`;
        }
        
        // Timeouts longs
        const longTimeouts = this.diagnosticData.timeouts.filter(t => t.delay > 30000);
        if (longTimeouts.length > 0) {
            detailsHTML += `<div style="color: #ffc107;">⏰ ${longTimeouts.length} timeouts longs détectés</div>`;
        }
        
        // Requêtes lentes
        const slowRequests = this.diagnosticData.requests.filter(r => r.duration && r.duration > 5000);
        if (slowRequests.length > 0) {
            detailsHTML += `<div style="color: #fd7e14;">🐌 ${slowRequests.length} requêtes lentes détectées</div>`;
        }
        
        details.innerHTML = detailsHTML;
    }
    
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            elapsedTime: Math.round((performance.now() - this.startTime) / 1000),
            summary: {
                totalIntervals: this.diagnosticData.intervals.length,
                totalTimeouts: this.diagnosticData.timeouts.length,
                totalRequests: this.diagnosticData.requests.length,
                totalErrors: this.diagnosticData.errors.length
            },
            details: this.diagnosticData
        };
        
        console.log('📊 RAPPORT DE DIAGNOSTIC COMPLET:', report);
        
        // Télécharger le rapport
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `admin-diagnostic-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        alert('📊 Rapport de diagnostic généré et téléchargé!');
    }
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', function() {
    window.adminDiagnostic = new AdminPerformanceDiagnostic();
});

// Initialisation immédiate si le DOM est déjà prêt
if (document.readyState !== 'loading') {
    window.adminDiagnostic = new AdminPerformanceDiagnostic();
}

console.log('🔍 Script de diagnostic de performance prêt');
