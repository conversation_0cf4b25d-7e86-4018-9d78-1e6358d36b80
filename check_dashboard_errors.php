<?php

echo "🔍 Vérification des erreurs du dashboard...\n\n";

// Vérifier les logs Laravel
$logPath = 'storage/logs/laravel.log';

if (file_exists($logPath)) {
    echo "📋 Vérification des logs Laravel...\n";
    
    // Lire les dernières lignes du log
    $logContent = file_get_contents($logPath);
    $logLines = explode("\n", $logContent);
    $recentLines = array_slice($logLines, -50); // 50 dernières lignes
    
    $errorFound = false;
    foreach ($recentLines as $line) {
        if (stripos($line, 'error') !== false || 
            stripos($line, 'exception') !== false ||
            stripos($line, 'fatal') !== false ||
            stripos($line, 'dashboard') !== false ||
            stripos($line, 'stock') !== false) {
            
            if (!$errorFound) {
                echo "⚠️  Erreurs trouvées dans les logs:\n";
                $errorFound = true;
            }
            echo "   " . trim($line) . "\n";
        }
    }
    
    if (!$errorFound) {
        echo "✅ Aucune erreur récente dans les logs\n";
    }
    echo "\n";
} else {
    echo "⚠️  Fichier de log non trouvé: $logPath\n\n";
}

// Vérifier la structure des fichiers
echo "📁 Vérification des fichiers...\n";

$files = [
    'app/Http/Controllers/Admin/DashboardController.php' => 'Contrôleur Admin',
    'app/Services/StockService.php' => 'Service Stock',
    'resources/views/admin/dashboard.blade.php' => 'Vue Dashboard',
    'routes/web.php' => 'Routes'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description: $file\n";
    } else {
        echo "❌ $description manquant: $file\n";
    }
}
echo "\n";

// Vérifier les permissions
echo "🔐 Vérification des permissions...\n";

$directories = [
    'storage/logs',
    'storage/framework/cache',
    'storage/framework/views',
    'bootstrap/cache'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ $dir: Écriture OK\n";
        } else {
            echo "⚠️  $dir: Pas d'écriture\n";
        }
    } else {
        echo "❌ $dir: Répertoire manquant\n";
    }
}
echo "\n";

// Vérifier la configuration
echo "⚙️  Vérification de la configuration...\n";

if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    
    $configs = [
        'APP_DEBUG' => 'Mode debug',
        'DB_CONNECTION' => 'Base de données',
        'CACHE_DRIVER' => 'Cache'
    ];
    
    foreach ($configs as $key => $description) {
        if (preg_match("/^$key=(.*)$/m", $envContent, $matches)) {
            echo "✅ $description ($key): " . trim($matches[1]) . "\n";
        } else {
            echo "⚠️  $description ($key): Non défini\n";
        }
    }
} else {
    echo "❌ Fichier .env manquant\n";
}
echo "\n";

// Créer un script de test rapide
echo "🧪 Création d'un test rapide...\n";

$quickTest = '<?php
// Test rapide du dashboard
require_once "vendor/autoload.php";

try {
    $app = require_once "bootstrap/app.php";
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✅ Laravel chargé\n";
    
    // Test du StockService
    $stockService = new App\Services\StockService();
    $stockStatus = $stockService->getStockStatus();
    echo "✅ StockService OK - " . $stockStatus["summary"]["total_products"] . " produits\n";
    
    // Test des produits
    $products = App\Models\Product::count();
    echo "✅ Produits en DB: $products\n";
    
    if ($products === 0) {
        echo "⚠️  Créez des produits avec: php create_test_products.php\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
';

file_put_contents('quick_test.php', $quickTest);
echo "✅ Fichier quick_test.php créé\n\n";

echo "🚀 COMMANDES À EXÉCUTER:\n";
echo "========================\n";
echo "1. php debug_stock_tab.php     # Diagnostic complet\n";
echo "2. php quick_test.php          # Test rapide\n";
echo "3. php create_test_products.php # Créer des produits\n";
echo "4. php artisan cache:clear     # Vider le cache\n";
echo "5. php artisan view:clear      # Vider les vues\n";
echo "6. php artisan serve           # Redémarrer le serveur\n\n";

echo "🌐 URLS À TESTER:\n";
echo "=================\n";
echo "• Dashboard: http://127.0.0.1:8000/admin/dashboard\n";
echo "• API Stock: http://127.0.0.1:8000/dashboard/stock-data\n";
echo "• Test HTML: ouvrir test_stock_tab.html\n\n";

echo "🔍 SI LE PROBLÈME PERSISTE:\n";
echo "============================\n";
echo "1. Vérifiez que vous êtes connecté en tant qu'admin\n";
echo "2. Ouvrez F12 → Console pour voir les erreurs JavaScript\n";
echo "3. Vérifiez F12 → Network pour voir les requêtes échouées\n";
echo "4. Regardez les logs: tail -f storage/logs/laravel.log\n";
