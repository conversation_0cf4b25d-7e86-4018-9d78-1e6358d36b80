# Script PowerShell pour supprimer le gros script entre les lignes 5569 et sa fermeture

$filePath = "resources\views\accountant\dashboard-professional.blade.php"

Write-Host "🔧 Suppression du gros script de widgets..." -ForegroundColor Yellow

# Lire le contenu du fichier
$content = Get-Content $filePath

# Trouver la ligne de début (5569) et la ligne de fin (</script>)
$startLine = 5569
$endLine = -1

for ($i = $startLine; $i -lt $content.Length; $i++) {
    if ($content[$i] -eq "</script>") {
        $endLine = $i + 1  # +1 car les indices sont 0-based mais les numéros de ligne sont 1-based
        break
    }
}

if ($endLine -gt 0) {
    Write-Host "📊 Script trouvé de la ligne $startLine à la ligne $endLine" -ForegroundColor Cyan
    
    # Créer un nouveau contenu sans les lignes du script
    $newContent = @()
    
    # Ajouter les lignes avant le script
    for ($i = 0; $i -lt ($startLine - 1); $i++) {
        $newContent += $content[$i]
    }
    
    # Ajouter un commentaire de remplacement
    $newContent += ""
    
    # Ajouter les lignes après le script
    for ($i = $endLine; $i -lt $content.Length; $i++) {
        $newContent += $content[$i]
    }
    
    # Écrire le nouveau contenu
    Set-Content $filePath $newContent -Encoding UTF8
    
    $linesRemoved = $endLine - $startLine + 1
    Write-Host "✅ Script supprimé!" -ForegroundColor Green
    Write-Host "📈 Lignes supprimées: $linesRemoved" -ForegroundColor Green
    
    # Afficher les nouvelles statistiques
    $newLines = (Get-Content $filePath | Measure-Object -Line).Lines
    $newSize = (Get-Item $filePath).Length
    Write-Host "📄 Nouvelles lignes: $newLines" -ForegroundColor Green
    Write-Host "💾 Nouvelle taille: $([math]::Round($newSize/1KB, 2)) KB" -ForegroundColor Green
} else {
    Write-Host "❌ Impossible de trouver la fermeture du script" -ForegroundColor Red
}
