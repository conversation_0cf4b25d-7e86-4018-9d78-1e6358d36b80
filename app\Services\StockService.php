<?php

namespace App\Services;

use App\Models\Product;
use App\Models\StockHistory;
use App\Models\Supply;
use App\Models\Sale;
use App\Events\StockUpdated;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class StockService
{
    /**
     * Met à jour le stock d'un produit et enregistre l'historique
     */
    public function updateStock(Product $product, float $quantity, string $type, $relatedModel = null, $userId = null): bool
    {
        try {
            DB::beginTransaction();

            $previousStock = $product->stock_quantity;
            $newStock = $previousStock + $quantity;

            // Mise à jour du stock du produit
            $product->update(['stock_quantity' => $newStock]);

            // Enregistrement dans l'historique
            $stockHistory = $this->recordStockHistory($product, $quantity, $previousStock, $newStock, $type, $relatedModel, $userId);

            DB::commit();

            // Déclencher l'événement de mise à jour de stock
            event(new StockUpdated($product->fresh(), $stockHistory));

            Log::info('Stock mis à jour avec succès', [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'previous_stock' => $previousStock,
                'quantity_change' => $quantity,
                'new_stock' => $newStock,
                'type' => $type
            ]);

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de la mise à jour du stock', [
                'product_id' => $product->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Enregistre un mouvement de stock dans l'historique
     */
    private function recordStockHistory(Product $product, float $quantity, float $previousStock, float $newStock, string $type, $relatedModel = null, $userId = null): StockHistory
    {
        $historyData = [
            'product_id' => $product->id,
            'type' => $type,
            'quantity' => $quantity,
            'previous_stock' => $previousStock,
            'new_stock' => $newStock,
            'user_id' => $userId ?? auth()->id(),
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Ajouter la relation selon le type
        if ($relatedModel instanceof Supply) {
            $historyData['supply_id'] = $relatedModel->id;
        }

        return StockHistory::create($historyData);
    }

    /**
     * Traite la validation d'un approvisionnement
     */
    public function processSupplyValidation(Supply $supply): bool
    {
        try {
            DB::beginTransaction();

            // Charger les relations nécessaires - le produit est associé à l'approvisionnement, pas aux villes
            $supply->load(['cities', 'product']);

            Log::info('Début du traitement de validation d\'approvisionnement', [
                'supply_id' => $supply->id,
                'product_id' => $supply->product_id,
                'cities_count' => $supply->cities->count()
            ]);

            // Le produit est associé à l'approvisionnement, pas aux villes individuelles
            $product = $supply->product;
            if ($product) {
                // Calculer la quantité totale de toutes les villes
                $totalQuantity = $supply->cities->sum('quantity');
                
                Log::info('Mise à jour du stock via StockService', [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'total_quantity' => $totalQuantity,
                    'cities_data' => $supply->cities->map(function($city) {
                        return [
                            'city_id' => $city->city_id,
                            'quantity' => $city->quantity
                        ];
                    })->toArray()
                ]);
                
                // Vérifier que la quantité totale est valide
                if ($totalQuantity <= 0) {
                    throw new \Exception('La quantité totale doit être supérieure à 0. Quantité calculée: ' . $totalQuantity);
                }
                
                // Mettre à jour le stock avec la quantité totale
                $updateSuccess = $this->updateStock(
                    $product,
                    floatval($totalQuantity),
                    'supply_validation',
                    $supply,
                    $supply->validator_id
                );
                
                if (!$updateSuccess) {
                    throw new \Exception('Échec de la mise à jour du stock pour le produit ID: ' . $product->id);
                }
            } else {
                Log::error('Produit non trouvé pour l\'approvisionnement', [
                    'supply_id' => $supply->id,
                    'product_id' => $supply->product_id
                ]);
                throw new \Exception('Produit associé à l\'approvisionnement non trouvé.');
            }

            DB::commit();
            Log::info('Traitement de validation d\'approvisionnement terminé avec succès');
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors du traitement de la validation d\'approvisionnement', [
                'supply_id' => $supply->id,
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Traite une vente et met à jour les stocks
     */
    public function processSale(Sale $sale): bool
    {
        try {
            DB::beginTransaction();

            // Récupérer le produit via la relation supply->cities
            $supplyCity = $sale->supply->cities()
                ->where('city_id', $sale->city_id)
                ->first();

            if ($supplyCity && $supplyCity->product) {
                $quantity = -floatval($sale->quantity); // Négatif car c'est une sortie de stock
                $this->updateStock(
                    $supplyCity->product,
                    $quantity,
                    'sale',
                    $sale,
                    $sale->created_by
                );

                // Mettre à jour la quantité restante dans supply_cities
                $supplyCity->remaining_quantity = max(0, $supplyCity->remaining_quantity - $sale->quantity);
                $supplyCity->save();
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors du traitement de la vente', [
                'sale_id' => $sale->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Convertit une quantité en tonnage pour l'affichage
     */
    private function convertToTonnage($quantity, $unit): array
    {
        // Conversion des unités vers les tonnes
        $conversions = [
            'kg' => 0.001,      // 1 kg = 0.001 tonne
            'kilogramme' => 0.001,
            'sac' => 0.05,      // 1 sac de ciment = 50kg = 0.05 tonne
            'sacs' => 0.05,
            'tonne' => 1,       // 1 tonne = 1 tonne
            'tonnes' => 1,
            't' => 1,
            'unité' => 0.05,    // Par défaut, 1 unité = 50kg = 0.05 tonne
            'unités' => 0.05,
            'm³' => 1.5,        // 1 m³ de sable/gravier ≈ 1.5 tonnes
            'm3' => 1.5,
            'litre' => 0.001,   // 1 litre ≈ 1 kg = 0.001 tonne
            'litres' => 0.001,
            'l' => 0.001
        ];

        $unitLower = strtolower(trim($unit ?? 'unité'));
        $conversionFactor = $conversions[$unitLower] ?? 0.05; // Par défaut 50kg

        $tonnage = $quantity * $conversionFactor;

        // Formatage intelligent
        if ($tonnage >= 1) {
            return [
                'display' => number_format($tonnage, 1, ',', ' ') . ' T',
                'raw_tonnage' => $tonnage,
                'original_quantity' => $quantity,
                'original_unit' => $unit
            ];
        } else {
            // Afficher en kg si moins d'une tonne
            $kg = $tonnage * 1000;
            return [
                'display' => number_format($kg, 0, ',', ' ') . ' kg',
                'raw_tonnage' => $tonnage,
                'original_quantity' => $quantity,
                'original_unit' => $unit
            ];
        }
    }

    /**
     * Récupère l'état actuel des stocks avec des informations détaillées (optimisé avec cache)
     */
    public function getStockStatus(): array
    {
        return Cache::remember('stock_service_status', 600, function () {
            // Requête optimisée avec eager loading sélectif
            $products = Product::with(['category:id,name'])
                ->select('id', 'name', 'category_id', 'stock_quantity', 'unit', 'price', 'updated_at')
                ->where('is_active', true)
                ->orderBy('stock_quantity', 'asc')
                ->get();

            // Calculs optimisés avec une seule boucle
            $stockData = [];
            $summary = [
                'total_products' => 0,
                'total_value' => 0,
                'total_tonnage' => 0,
                'low_stock_count' => 0,
                'out_of_stock_count' => 0,
                'normal_stock_count' => 0
            ];

            foreach ($products as $product) {
                $isLowStock = $product->stock_quantity <= 10;
                $isOutOfStock = $product->stock_quantity <= 0;
                $productValue = $product->stock_quantity * $product->price;

                // Mise à jour des compteurs
                $summary['total_products']++;
                $summary['total_value'] += $productValue;

                if ($isOutOfStock) {
                    $summary['out_of_stock_count']++;
                } elseif ($isLowStock) {
                    $summary['low_stock_count']++;
                } else {
                    $summary['normal_stock_count']++;
                }

                // Conversion en tonnage optimisée
                $tonnageData = $this->convertToTonnage($product->stock_quantity, $product->unit);
                $summary['total_tonnage'] += $tonnageData['raw_tonnage'];

                $stockData[] = [
                    'id' => $product->id,
                    'name' => $product->name,
                    'category' => $product->category->name ?? 'N/A',
                    'current_stock' => $product->stock_quantity,
                    'unit' => $product->unit,
                    'tonnage_display' => $tonnageData['display'],
                    'raw_tonnage' => $tonnageData['raw_tonnage'],
                    'unit_price' => $product->price,
                    'total_value' => $productValue,
                    'status' => $isOutOfStock ? 'out_of_stock' : ($isLowStock ? 'low_stock' : 'normal'),
                    'last_updated' => $product->updated_at
                ];
            }

            $summary['total_tonnage_display'] = number_format($summary['total_tonnage'], 1, ',', ' ') . ' T';

            return [
                'products' => $stockData,
                'summary' => $summary
            ];
        });
    }

    /**
     * Récupère les mouvements de stock récents (optimisé avec cache)
     */
    public function getRecentStockMovements(int $limit = 10): array
    {
        $cacheKey = "stock_movements_recent_{$limit}";

        return Cache::remember($cacheKey, 300, function () use ($limit) {
            // Eager loading optimisé avec sélection des champs nécessaires
            $movements = StockHistory::with([
                'product:id,name',
                'supply:id,reference',
                'user:id,name'
            ])
            ->select('id', 'product_id', 'supply_id', 'user_id', 'type', 'quantity', 'previous_stock', 'new_stock', 'created_at')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

            return $movements->map(function ($movement) {
                return [
                    'id' => $movement->id,
                    'product_name' => $movement->product->name ?? 'Produit supprimé',
                    'type' => $movement->type,
                    'quantity' => $movement->quantity,
                    'previous_stock' => $movement->previous_stock,
                    'new_stock' => $movement->new_stock,
                    'user_name' => $movement->user->name ?? 'Utilisateur supprimé',
                    'supply_reference' => $movement->supply->reference ?? null,
                    'created_at' => $movement->created_at,
                    'formatted_date' => $movement->created_at->format('d/m/Y H:i')
                ];
            })->toArray();
        });
    }

    /**
     * Récupère les alertes de stock
     */
    public function getStockAlerts(): array
    {
        $lowStockProducts = Product::with('category')
            ->where('is_active', true)
            ->where('stock_quantity', '<=', 10)
            ->orderBy('stock_quantity', 'asc')
            ->get();

        $outOfStockProducts = Product::with('category')
            ->where('is_active', true)
            ->where('stock_quantity', '<=', 0)
            ->get();

        return [
            'low_stock' => $lowStockProducts->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'category' => $product->category->name ?? 'N/A',
                    'current_stock' => $product->stock_quantity,
                    'unit' => $product->unit,
                    'severity' => $product->stock_quantity <= 0 ? 'critical' : 'warning'
                ];
            })->toArray(),
            'out_of_stock' => $outOfStockProducts->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'category' => $product->category->name ?? 'N/A',
                    'unit' => $product->unit
                ];
            })->toArray()
        ];
    }
}
