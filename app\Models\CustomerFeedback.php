<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerFeedback extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'cement_order_detail_id',
        'type',
        'category',
        'subject',
        'message',
        'delivery_rating',
        'service_rating',
        'product_rating',
        'overall_rating',
        'delivery_aspects',
        'driver_name',
        'truck_number',
        'delivery_date',
        'status',
        'admin_response',
        'responded_by',
        'responded_at',
        'is_anonymous',
        'priority'
    ];

    protected $casts = [
        'delivery_aspects' => 'array',
        'delivery_date' => 'datetime',
        'responded_at' => 'datetime',
        'is_anonymous' => 'boolean'
    ];

    // Relations
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function cementOrderDetail(): BelongsTo
    {
        return $this->belongsTo(CementOrderDetail::class);
    }

    public function respondedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'responded_by');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'warning',
            'reviewed' => 'info',
            'resolved' => 'success',
            'closed' => 'secondary'
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    public function getPriorityBadgeAttribute()
    {
        $badges = [
            'low' => 'success',
            'normal' => 'primary',
            'high' => 'warning',
            'urgent' => 'danger'
        ];

        return $badges[$this->priority] ?? 'primary';
    }
}
