<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="test-token">
    
    <!-- Permissions Policy pour éviter les erreurs en développement -->
    <meta http-equiv="Permissions-Policy" content="unload=*, payment=*, geolocation=*">
    
    <title>Test - Correction des Paiements</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .payment-card {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .payment-card:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }
        
        .payment-card.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .reference-field {
            margin-top: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        .alert-info {
            border-left: 4px solid #06b6d4;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-credit-card me-2"></i>Test - Sélection Mode de Paiement</h4>
                    </div>
                    <div class="card-body">
                        <!-- Cartes de méthodes de paiement -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="payment-card" data-method="cash">
                                    <input type="radio" name="payment_method" value="cash" style="display: none;">
                                    <div class="text-center">
                                        <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                                        <h6>Espèces</h6>
                                        <small class="text-muted">Paiement en liquide</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="payment-card" data-method="bank_transfer">
                                    <input type="radio" name="payment_method" value="bank_transfer" style="display: none;">
                                    <div class="text-center">
                                        <i class="fas fa-university fa-2x text-primary mb-2"></i>
                                        <h6>Virement</h6>
                                        <small class="text-muted">Virement bancaire</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="payment-card" data-method="check">
                                    <input type="radio" name="payment_method" value="check" style="display: none;">
                                    <div class="text-center">
                                        <i class="fas fa-money-check-alt fa-2x text-info mb-2"></i>
                                        <h6>Chèque</h6>
                                        <small class="text-muted">Paiement par chèque</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="payment-card" data-method="mobile_money">
                                    <input type="radio" name="payment_method" value="mobile_money" style="display: none;">
                                    <div class="text-center">
                                        <i class="fas fa-mobile-alt fa-2x text-warning mb-2"></i>
                                        <h6>Mobile Money</h6>
                                        <small class="text-muted">Paiement mobile</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Informations sur la méthode de paiement -->
                        <div id="paymentMethodInfo" class="alert alert-info mt-3" style="display: none;">
                            <h6 class="alert-heading d-flex align-items-center">
                                <i class="fas fa-info-circle me-2"></i>
                                <span>Information</span>
                            </h6>
                            <p class="mb-0">Sélectionnez une méthode de paiement.</p>
                        </div>
                        
                        <!-- Champ de référence -->
                        <div class="reference-field" style="display: none;">
                            <label for="reference_number" class="form-label">
                                <i class="fas fa-hashtag me-1"></i>
                                Numéro de référence *
                            </label>
                            <input type="text" class="form-control" id="reference_number" name="reference_number" 
                                   placeholder="Saisissez le numéro de référence">
                            <small class="form-text text-muted">
                                Ce champ est requis pour les paiements par virement, chèque ou mobile money.
                            </small>
                        </div>
                        
                        <!-- Boutons d'action -->
                        <div class="mt-4">
                            <button type="button" class="btn btn-primary" onclick="runPaymentDiagnostic()">
                                <i class="fas fa-stethoscope me-2"></i>Diagnostic
                            </button>
                            <button type="button" class="btn btn-warning" onclick="autoFixPaymentIssues()">
                                <i class="fas fa-wrench me-2"></i>Réparation Auto
                            </button>
                            <button type="button" class="btn btn-success" onclick="testPaymentSelection()">
                                <i class="fas fa-play me-2"></i>Test Sélection
                            </button>
                        </div>
                        
                        <!-- Zone de résultats -->
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="js/payment-diagnostic.js"></script>
    
    <script>
        // Gestionnaire d'erreurs global
        window.addEventListener('error', function(e) {
            console.warn('Erreur JavaScript interceptée:', e.error);
            return false;
        });
        
        document.addEventListener('DOMContentLoaded', function() {
            const paymentCards = document.querySelectorAll('.payment-card');
            const paymentMethodInfo = document.getElementById('paymentMethodInfo');
            
            // Fonction pour gérer l'affichage du champ de référence
            function toggleReferenceField(method) {
                const referenceField = document.querySelector('.reference-field');
                const referenceInput = document.getElementById('reference_number');
                
                if (method === 'cash') {
                    if (referenceField) {
                        referenceField.style.display = 'none';
                    }
                    if (referenceInput) {
                        referenceInput.removeAttribute('required');
                        referenceInput.value = '';
                    }
                } else {
                    if (referenceField) {
                        referenceField.style.display = 'block';
                    }
                    if (referenceInput) {
                        referenceInput.setAttribute('required', 'required');
                    }
                }
            }
            
            // Fonction pour mettre à jour les informations sur la méthode de paiement
            function updatePaymentMethodInfo(method) {
                let infoContent = '';
                let title = '';
                let iconClass = 'fas fa-info-circle';
                
                switch(method) {
                    case 'cash':
                        iconClass = 'fas fa-money-bill-wave';
                        title = 'Paiement en espèces';
                        infoContent = 'Assurez-vous de compter correctement les espèces et de délivrer un reçu au client.';
                        break;
                    case 'bank_transfer':
                        iconClass = 'fas fa-university';
                        title = 'Paiement par virement';
                        infoContent = 'Veuillez saisir le numéro de référence du virement dans le champ prévu à cet effet.';
                        break;
                    case 'check':
                        iconClass = 'fas fa-money-check-alt';
                        title = 'Paiement par chèque';
                        infoContent = 'N\'oubliez pas de noter le numéro du chèque dans le champ de référence.';
                        break;
                    case 'mobile_money':
                        iconClass = 'fas fa-mobile-alt';
                        title = 'Paiement par Mobile Money';
                        infoContent = 'Veuillez saisir le numéro de transaction Mobile Money dans le champ de référence.';
                        break;
                }
                
                paymentMethodInfo.innerHTML = `
                    <h6 class="alert-heading d-flex align-items-center">
                        <i class="${iconClass} me-2"></i>
                        <span>${title}</span>
                    </h6>
                    <p class="mb-0">${infoContent}</p>
                `;
                paymentMethodInfo.style.display = 'block';
            }
            
            // Attacher les gestionnaires d'événements
            paymentCards.forEach(card => {
                card.addEventListener('click', function() {
                    try {
                        paymentCards.forEach(c => c.classList.remove('selected'));
                        this.classList.add('selected');
                        
                        const radio = this.querySelector('input[type="radio"]');
                        if (radio) {
                            radio.checked = true;
                        }
                        
                        const method = this.dataset.method;
                        if (method) {
                            updatePaymentMethodInfo(method);
                            toggleReferenceField(method);
                        }
                    } catch (error) {
                        console.error('Erreur lors de la sélection du mode de paiement:', error);
                    }
                });
            });
        });
        
        // Fonction de test
        function testPaymentSelection() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="alert alert-info">Test en cours...</div>';
            
            setTimeout(() => {
                const selectedCard = document.querySelector('.payment-card.selected');
                const checkedRadio = document.querySelector('input[name="payment_method"]:checked');
                const referenceField = document.querySelector('.reference-field');
                
                let html = '<div class="alert alert-success"><h6>Résultats du test:</h6><ul>';
                html += `<li>Carte sélectionnée: ${selectedCard ? '✅' : '❌'}</li>`;
                html += `<li>Radio bouton coché: ${checkedRadio ? '✅' : '❌'}</li>`;
                html += `<li>Champ de référence géré: ${referenceField ? '✅' : '❌'}</li>`;
                html += '</ul></div>';
                
                results.innerHTML = html;
            }, 1000);
        }
    </script>
</body>
</html>
