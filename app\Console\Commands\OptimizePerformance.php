<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class OptimizePerformance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:optimize-performance {--force : Force l\'optimisation même en production}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = 'Optimise les performances de l\'application (cache, vues, routes, etc.)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Début de l\'optimisation des performances...');

        // Vérification de l'environnement
        if (app()->environment('production') && !$this->option('force')) {
            if (!$this->confirm('Vous êtes en production. Continuer l\'optimisation ?')) {
                $this->info('Optimisation annulée.');
                return 0;
            }
        }

        $this->optimizeCache();
        $this->optimizeViews();
        $this->optimizeRoutes();
        $this->optimizeConfig();
        $this->optimizeDatabase();
        $this->clearOldLogs();

        $this->info('✅ Optimisation des performances terminée avec succès !');
        $this->displayOptimizationTips();

        return 0;
    }

    /**
     * Optimise le cache de l'application
     */
    private function optimizeCache()
    {
        $this->info('📦 Optimisation du cache...');

        // Nettoyer le cache existant
        $this->call('cache:clear');
        $this->line('   - Cache application nettoyé');

        // Optimiser le cache des configurations
        $this->call('config:cache');
        $this->line('   - Cache des configurations créé');

        // Pré-charger les données critiques en cache
        $this->preloadCriticalData();
        $this->line('   - Données critiques pré-chargées');

        $this->info('✅ Cache optimisé');
    }

    /**
     * Optimise les vues Blade
     */
    private function optimizeViews()
    {
        $this->info('🎨 Optimisation des vues...');

        // Nettoyer le cache des vues
        $this->call('view:clear');
        $this->line('   - Cache des vues nettoyé');

        // Pré-compiler les vues
        $this->call('view:cache');
        $this->line('   - Vues pré-compilées');

        $this->info('✅ Vues optimisées');
    }

    /**
     * Optimise les routes
     */
    private function optimizeRoutes()
    {
        $this->info('🛣️ Optimisation des routes...');

        // Nettoyer le cache des routes
        $this->call('route:clear');
        $this->line('   - Cache des routes nettoyé');

        // Mettre en cache les routes
        $this->call('route:cache');
        $this->line('   - Routes mises en cache');

        $this->info('✅ Routes optimisées');
    }

    /**
     * Optimise la configuration
     */
    private function optimizeConfig()
    {
        $this->info('⚙️ Optimisation de la configuration...');

        // Optimiser l'autoloader de Composer
        $this->call('optimize');
        $this->line('   - Autoloader optimisé');

        $this->info('✅ Configuration optimisée');
    }

    /**
     * Optimise la base de données
     */
    private function optimizeDatabase()
    {
        $this->info('🗄️ Optimisation de la base de données...');

        try {
            // Analyser les tables pour optimiser les index
            $tables = ['orders', 'supplies', 'products', 'users', 'cement_orders'];
            
            foreach ($tables as $table) {
                if (DB::getSchemaBuilder()->hasTable($table)) {
                    DB::statement("ANALYZE TABLE {$table}");
                    $this->line("   - Table {$table} analysée");
                }
            }

            // Optimiser les tables MyISAM si présentes
            DB::statement("OPTIMIZE TABLE " . implode(',', $tables));
            $this->line('   - Tables optimisées');

        } catch (\Exception $e) {
            $this->warn('   - Optimisation des tables échouée : ' . $e->getMessage());
        }

        $this->info('✅ Base de données optimisée');
    }

    /**
     * Nettoie les anciens logs
     */
    private function clearOldLogs()
    {
        $this->info('🧹 Nettoyage des anciens logs...');

        $logPath = storage_path('logs');
        $files = glob($logPath . '/laravel-*.log');
        
        $deletedCount = 0;
        foreach ($files as $file) {
            $fileDate = filemtime($file);
            $daysDiff = (time() - $fileDate) / (60 * 60 * 24);
            
            // Supprimer les logs de plus de 30 jours
            if ($daysDiff > 30) {
                unlink($file);
                $deletedCount++;
            }
        }

        $this->line("   - {$deletedCount} anciens fichiers de logs supprimés");
        $this->info('✅ Logs nettoyés');
    }

    /**
     * Pré-charge les données critiques en cache
     */
    private function preloadCriticalData()
    {
        try {
            // Pré-charger les statistiques admin
            Cache::remember('admin_dashboard_stats', 600, function () {
                return [
                    'users_count' => DB::table('users')->count(),
                    'products_count' => DB::table('products')->where('deleted_at', null)->count(),
                    'orders_count' => DB::table('orders')->count(),
                ];
            });

            // Pré-charger les données de performance
            Cache::remember('performance_metrics', 300, function () {
                return [
                    'last_optimization' => now(),
                    'cache_status' => 'optimized'
                ];
            });

        } catch (\Exception $e) {
            $this->warn('Erreur lors du pré-chargement : ' . $e->getMessage());
        }
    }

    /**
     * Affiche des conseils d'optimisation
     */
    private function displayOptimizationTips()
    {
        $this->info('💡 Conseils pour maintenir les performances :');
        $this->line('   • Exécutez cette commande régulièrement (hebdomadaire)');
        $this->line('   • Surveillez les logs de performances');
        $this->line('   • Utilisez un serveur de cache Redis/Memcached en production');
        $this->line('   • Activez la compression GZIP au niveau du serveur web');
        $this->line('   • Surveillez l\'utilisation mémoire et CPU');
        
        $this->info('📊 Prochaines étapes recommandées :');
        $this->line('   • php artisan migrate (pour appliquer les index de performance)');
        $this->line('   • Redémarrer le serveur web pour appliquer toutes les optimisations');
    }
}
