<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "Test du StockService...\n";
    
    // Créer une instance du service
    $stockService = new App\Services\StockService();
    echo "✅ StockService créé avec succès\n";
    
    // Tester getStockStatus
    $stockStatus = $stockService->getStockStatus();
    echo "✅ getStockStatus() fonctionne\n";
    echo "Nombre de produits: " . $stockStatus['summary']['total_products'] . "\n";
    
    // Tester getRecentStockMovements
    $movements = $stockService->getRecentStockMovements(5);
    echo "✅ getRecentStockMovements() fonctionne\n";
    echo "Nombre de mouvements récents: " . count($movements) . "\n";
    
    // Tester getStockAlerts
    $alerts = $stockService->getStockAlerts();
    echo "✅ getStockAlerts() fonctionne\n";
    echo "Produits en rupture: " . count($alerts['out_of_stock']) . "\n";
    echo "Produits en stock faible: " . count($alerts['low_stock']) . "\n";
    
    echo "\n🎉 Tous les tests du StockService sont passés avec succès!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . "\n";
    echo "Ligne: " . $e->getLine() . "\n";
}
