# 🚀 Configuration Gradis pour gradis-togo.com

## 📋 Informations de Configuration

### 🌐 Domaine et URL
- **Domaine**: gradis-togo.com
- **URL complète**: https://gradis-togo.com
- **SSL**: Activé (obligatoire)

### 🗄️ Base de Données MySQL
- **Nom de la base**: `gradfkxi_gradis_db`
- **Utilisateur**: `gradfkxi_kamal_gradis`
- **Mot de passe**: `78KLjuytre_54`
- **Hôte**: localhost
- **Port**: 3306

### 📧 Configuration Email
- **Serveur SMTP**: mail.gradis-togo.com
- **Port**: 587
- **Chiffrement**: TLS
- **Adresse expéditeur**: <EMAIL>
- **Nom expéditeur**: Gradis

### ⚙️ Configuration Technique
- **Environnement**: Production
- **Debug**: Désactivé
- **Cache**: <PERSON><PERSON> (recommandé)
- **Sessions**: <PERSON><PERSON> (recommandé)
- **Timezone**: Africa/Lome

## 📁 Fichier .env.production Configuré

Le fichier `.env.production` a été configuré avec vos informations :

```env
APP_NAME=Gradis
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://gradis-togo.com
APP_TIMEZONE=Africa/Lome

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=gradfkxi_gradis_db
DB_USERNAME=gradfkxi_kamal_gradis
DB_PASSWORD=78KLjuytre_54

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

MAIL_MAILER=smtp
MAIL_HOST=mail.gradis-togo.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_mail_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

## 🔧 Étapes de Déploiement sur Namecheap

### 1. Préparation du Serveur
1. **Activer PHP 8.1+** dans cPanel > PHP Selector
2. **Créer la base de données** avec les informations ci-dessus
3. **Activer SSL** (Let's Encrypt gratuit)
4. **Demander l'activation de Redis** au support Namecheap

### 2. Upload et Configuration
```bash
# 1. Uploader les fichiers du projet
# 2. Copier la configuration de production
cp .env.production .env

# 3. Générer la clé d'application
php artisan key:generate

# 4. Installer les dépendances
composer install --no-dev --optimize-autoloader
npm ci --production

# 5. Compiler les assets
npm run build
```

### 3. Configuration de la Base de Données
```bash
# Exécuter les migrations
php artisan migrate --force

# Seeder de production (optionnel)
php artisan db:seed --class=ProductionSeeder
```

### 4. Optimisation Laravel
```bash
# Cache de configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Permissions
chmod -R 775 storage bootstrap/cache
```

### 5. Configuration du Domaine
1. **Pointer le domaine** vers le dossier `public` de Laravel
2. **Activer SSL** et forcer HTTPS
3. **Configurer les sous-domaines** si nécessaire

### 6. Tâches Cron
Ajouter dans cPanel > Cron Jobs :
```bash
* * * * * cd /home/<USER>/public_html/gradis && php artisan schedule:run >/dev/null 2>&1
```

## ✅ Vérification de la Configuration

Exécutez le script de vérification pour vous assurer que tout est correct :

```bash
php scripts/verify-config.php
```

Ce script vérifiera :
- ✅ Configuration du domaine
- ✅ Connexion à la base de données
- ✅ Configuration Redis
- ✅ Paramètres de sécurité
- ✅ Permissions des fichiers

## 🔒 Sécurité

### Headers de Sécurité Configurés
- **HTTPS forcé** avec HSTS
- **Content Security Policy** strict
- **Protection XSS** activée
- **Protection contre le clickjacking**
- **Cookies sécurisés** avec SameSite strict

### Rate Limiting
- **Login**: 5 tentatives / 15 minutes
- **API**: 100 requêtes / minute
- **Upload**: 10 fichiers / minute
- **Recherche**: 30 requêtes / minute

## 📊 Performance

### Optimisations Activées
- **Compression GZIP** pour tous les contenus
- **Cache des assets** avec expiration 1 an
- **Minification HTML** automatique
- **Optimisation des images** avec WebP
- **Cache Redis** pour les requêtes et sessions

### Monitoring Automatique
- **Vérification de l'état** toutes les 5 minutes
- **Nettoyage des logs** quotidien
- **Sauvegarde de la base** quotidienne
- **Optimisation des images** hebdomadaire

## 📞 Support et Maintenance

### Scripts Disponibles
- `deploy.sh` - Déploiement automatisé
- `scripts/post-deploy.php` - Tâches post-déploiement
- `scripts/backup.php` - Sauvegarde automatique
- `scripts/verify-config.php` - Vérification de la configuration

### Logs et Monitoring
- **Logs Laravel**: `storage/logs/laravel.log`
- **Logs serveur**: Accessible via cPanel
- **Monitoring**: Surveillance automatique de l'état

### Contact Support
- **Namecheap**: Support 24/7 via chat ou ticket
- **Documentation**: Guides complets dans le projet

## 🎯 Checklist Final

Avant de mettre en ligne :

- [ ] Serveur Namecheap configuré (PHP 8.1+, MySQL 8.0)
- [ ] Base de données `gradfkxi_gradis_db` créée
- [ ] Utilisateur `gradfkxi_kamal_gradis` configuré
- [ ] Domaine gradis-togo.com pointé vers /public
- [ ] SSL activé et HTTPS forcé
- [ ] Fichier .env copié et configuré
- [ ] APP_KEY générée
- [ ] Dépendances installées
- [ ] Assets compilés
- [ ] Migrations exécutées
- [ ] Cache optimisé
- [ ] Permissions configurées
- [ ] Tâches cron configurées
- [ ] Tests de fonctionnement OK
- [ ] Script de vérification exécuté

## 🎉 Prêt pour le Déploiement !

Votre application Gradis est maintenant **entièrement configurée** pour gradis-togo.com avec :

✅ **Configuration personnalisée** pour votre domaine et base de données  
✅ **Optimisations de performance** pour un chargement rapide  
✅ **Sécurité renforcée** contre toutes les attaques courantes  
✅ **Maintenance automatisée** pour un fonctionnement optimal  
✅ **Monitoring continu** pour détecter les problèmes rapidement  

L'application est **production-ready** pour gradis-togo.com ! 🚀

---

*Configuration créée le $(date) pour le déploiement de Gradis sur gradis-togo.com*
