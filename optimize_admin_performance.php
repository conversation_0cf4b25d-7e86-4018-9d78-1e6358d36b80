<?php
/**
 * Script d'optimisation des performances admin
 * Supprime les logs excessifs et optimise les contrôleurs admin
 */

echo "🚀 OPTIMISATION DES PERFORMANCES ADMIN\n";
echo "=====================================\n\n";

$controllersPath = __DIR__ . '/app/Http/Controllers/Admin/';
$optimizedCount = 0;
$logsRemoved = 0;

// Liste des contrôleurs à optimiser
$controllers = [
    'SettingController.php',
    'SaleController.php', 
    'TruckController.php',
    'TogoRegionController.php',
    'ReportController.php',
    'OrderController.php'
];

foreach ($controllers as $controller) {
    $filePath = $controllersPath . $controller;
    
    if (!file_exists($filePath)) {
        echo "⚠️  Fichier non trouvé: $controller\n";
        continue;
    }
    
    echo "🔧 Optimisation de $controller...\n";
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Compter les logs avant optimisation
    $logsBefore = substr_count($content, 'Log::');
    
    // Supprimer les logs excessifs mais garder les erreurs critiques
    $patterns = [
        // Supprimer les logs info détaillés
        '/\s*Log::info\([^;]+\);\s*\n/i' => '',
        '/\s*\\\\Log::info\([^;]+\);\s*\n/i' => '',
        
        // Simplifier les logs d'erreur (garder seulement le message)
        '/Log::error\([^,]+,\s*\[[^\]]+\]\);/i' => 'Log::error($1);',
        '/\\\\Log::error\([^,]+,\s*\[[^\]]+\]\);/i' => '\\Log::error($1);',
        
        // Supprimer les stack traces
        '/\s*Log::error\([^;]*getTraceAsString[^;]*\);\s*\n/i' => '',
        '/\s*\\\\Log::error\([^;]*getTraceAsString[^;]*\);\s*\n/i' => '',
        
        // Supprimer les commentaires de logs
        '/\s*\/\/ Log [^\n]*\n/i' => '',
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $content = preg_replace($pattern, $replacement, $content);
    }
    
    // Compter les logs après optimisation
    $logsAfter = substr_count($content, 'Log::');
    $logsRemovedInFile = $logsBefore - $logsAfter;
    
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "✅ $controller optimisé - $logsRemovedInFile logs supprimés\n";
        $optimizedCount++;
        $logsRemoved += $logsRemovedInFile;
    } else {
        echo "ℹ️  $controller déjà optimisé\n";
    }
}

echo "\n📊 RÉSULTATS DE L'OPTIMISATION\n";
echo "==============================\n";
echo "Contrôleurs optimisés: $optimizedCount\n";
echo "Logs supprimés: $logsRemoved\n";
echo "✅ Optimisation terminée avec succès!\n\n";

echo "🎯 PROCHAINES ÉTAPES:\n";
echo "- Testez les fonctionnalités admin\n";
echo "- Vérifiez la fluidité des interfaces\n";
echo "- Les performances devraient être considérablement améliorées\n";
