<?php $__env->startSection('title', 'Détails de l\'approvisionnement'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <!-- Header moderne avec gradient -->
    <div class="detail-header">
        <div class="detail-header-content">
            <div class="d-flex justify-content-between align-items-center">
                <div class="header-left">
                    <div class="d-flex align-items-center">
                        <div class="detail-icon">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <div class="ms-3">
                            <h1 class="detail-title">Approvisionnement <?php echo e($supply->reference); ?></h1>
                            <p class="detail-subtitle mb-0">Détails complets de la demande d'approvisionnement</p>
                        </div>
                    </div>
                </div>
                <div class="header-right">
                    <div class="d-flex align-items-center gap-3">
                        <div class="status-display">
                            <?php if($supply->status === 'pending'): ?>
                                <span class="status-badge-large status-pending">
                                    <i class="fas fa-clock me-2"></i>
                                    En attente
                                </span>
                            <?php elseif($supply->status === 'validated'): ?>
                                <span class="status-badge-large status-validated">
                                    <i class="fas fa-check-circle me-2"></i>
                                    Validé
                                </span>
                            <?php elseif($supply->status === 'rejected'): ?>
                                <span class="status-badge-large status-rejected">
                                    <i class="fas fa-times-circle me-2"></i>
                                    Rejeté
                                </span>
                            <?php endif; ?>
                        </div>
                        <a href="<?php echo e(route('admin.supplies.index')); ?>" class="btn-back-modern">
                            <i class="fas fa-arrow-left me-2"></i>
                            Retour
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <?php if($supply->status === 'pending'): ?>
        <div class="quick-actions-bar">
            <div class="d-flex justify-content-center gap-3">
                <button type="button"
                        class="action-btn-large validate-btn-large"
                        onclick="confirmValidation(<?php echo e($supply->id); ?>)">
                    <i class="fas fa-check me-2"></i>
                    Valider l'approvisionnement
                </button>
                <button type="button"
                        class="action-btn-large reject-btn-large"
                        onclick="showRejectModal(<?php echo e($supply->id); ?>)">
                    <i class="fas fa-ban me-2"></i>
                    Rejeter l'approvisionnement
                </button>
            </div>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Colonne principale -->
        <div class="col-lg-8">
            <!-- Informations générales -->
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">
                        <i class="fas fa-info-circle me-2"></i>
                        Informations générales
                    </h3>
                </div>
                <div class="modern-card-body">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Référence</span>
                                <span class="info-value"><?php echo e($supply->reference); ?></span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Date de création</span>
                                <span class="info-value"><?php echo e($supply->created_at->format('d/m/Y à H:i')); ?></span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Fournisseur</span>
                                <span class="info-value"><?php echo e($supply->supplier->name); ?></span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Créé par</span>
                                <span class="info-value"><?php echo e($supply->createdBy->name ?? 'N/A'); ?></span>
                            </div>
                        </div>
                        <?php if($supply->validator): ?>
                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-label"><?php echo e($supply->status === 'validated' ? 'Validé' : 'Rejeté'); ?> par</span>
                                    <span class="info-value"><?php echo e($supply->validator->name); ?></span>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-label">Date de <?php echo e($supply->status === 'validated' ? 'validation' : 'rejet'); ?></span>
                                    <span class="info-value">
                                        <?php if($supply->validated_at): ?>
                                            <?php echo e(\Carbon\Carbon::parse($supply->validated_at)->format('d/m/Y à H:i')); ?>

                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">Montant total</span>
                                <span class="info-value amount-highlight"><?php echo e(number_format($supply->real_total_amount, 0, ',', ' ')); ?> FCFA</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Articles de l'approvisionnement -->
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">
                        <i class="fas fa-boxes me-2"></i>
                        Articles de l'approvisionnement
                    </h3>
                    <div class="card-actions">
                        <span class="items-count"><?php echo e($supply->details->count()); ?> article(s)</span>
                    </div>
                </div>
                <div class="modern-card-body p-0">
                    <div class="table-responsive">
                        <table class="modern-table-detailed">
                            <thead>
                                <tr>
                                    <th>
                                        <i class="fas fa-cube me-2"></i>
                                        Produit
                                    </th>
                                    <th>
                                        <i class="fas fa-weight-hanging me-2"></i>
                                        Quantité
                                    </th>
                                    <th>
                                        <i class="fas fa-ruler me-2"></i>
                                        Unité
                                    </th>
                                    <th>
                                        <i class="fas fa-tag me-2"></i>
                                        Prix unitaire
                                    </th>
                                    <th>
                                        <i class="fas fa-calculator me-2"></i>
                                        Total
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $supply->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="item-row">
                                        <td>
                                            <div class="product-cell">
                                                <div class="product-icon">
                                                    <i class="fas fa-box"></i>
                                                </div>
                                                <div class="product-info">
                                                    <span class="product-name"><?php echo e($item->product->name); ?></span>
                                                    <span class="product-category"><?php echo e($item->product->category->name); ?></span>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="quantity-cell">
                                                <?php if($item->product->category->name === 'Ciment'): ?>
                                                    <span class="quantity-value"><?php echo e(number_format($item->tonnage, 2, ',', ' ')); ?></span>
                                                    <span class="quantity-unit">T</span>
                                                <?php else: ?>
                                                    <span class="quantity-value"><?php echo e($item->quantity); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="unit-badge"><?php echo e($item->product->unit); ?></span>
                                        </td>
                                        <td>
                                            <div class="price-cell">
                                                <span class="price-value"><?php echo e(number_format($item->unit_price, 0, ',', ' ')); ?></span>
                                                <span class="price-currency">FCFA</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="total-cell">
                                                <span class="total-value"><?php echo e(number_format($item->total_price, 0, ',', ' ')); ?></span>
                                                <span class="total-currency">FCFA</span>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr class="total-row">
                                    <td colspan="4" class="total-label">
                                        <strong>
                                            <i class="fas fa-calculator me-2"></i>
                                            Total général
                                        </strong>
                                    </td>
                                    <td class="total-amount">
                                        <strong><?php echo e(number_format($supply->real_total_amount, 0, ',', ' ')); ?> FCFA</strong>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Destinations et Livraisons -->
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">
                        <i class="fas fa-truck me-2"></i>
                        Destinations et Livraisons
                    </h3>
                    <div class="card-actions">
                        <span class="destinations-count"><?php echo e($supply->cities->count()); ?> destination(s)</span>
                    </div>
                </div>
                <div class="modern-card-body p-0">
                    <div class="table-responsive">
                        <table class="modern-table-detailed">
                            <thead>
                                <tr>
                                    <th>
                                        <i class="fas fa-map-marker-alt me-2"></i>
                                        Ville
                                    </th>
                                    <th>
                                        <i class="fas fa-truck me-2"></i>
                                        Véhicule
                                    </th>
                                    <th>
                                        <i class="fas fa-user-tie me-2"></i>
                                        Chauffeur
                                    </th>
                                    <th>
                                        <i class="fas fa-route me-2"></i>
                                        Voyages
                                    </th>
                                    <th>
                                        <i class="fas fa-weight me-2"></i>
                                        Quantité
                                    </th>
                                    <th>
                                        <i class="fas fa-money-bill me-2"></i>
                                        Prix unitaire
                                    </th>
                                    <th>
                                        <i class="fas fa-calculator me-2"></i>
                                        Total
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $supply->cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $supplyCity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="destination-row">
                                        <td>
                                            <div class="city-cell">
                                                <div class="city-icon">
                                                    <i class="fas fa-city"></i>
                                                </div>
                                                <span class="city-name"><?php echo e($supplyCity->city->name); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="vehicle-cell">
                                                <?php if($supplyCity->vehicle): ?>
                                                    <div class="vehicle-info">
                                                        <span class="vehicle-number"><?php echo e($supplyCity->vehicle->registration_number); ?></span>
                                                        <?php if($supplyCity->vehicle->capacity): ?>
                                                            <span class="vehicle-capacity">
                                                                (<?php echo e(number_format($supplyCity->vehicle->capacity->capacity, 2, ',', ' ')); ?> <?php echo e(Str::lower($supplyCity->vehicle->capacity->unit)); ?>)
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="not-assigned">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                                        Non assigné
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="driver-cell">
                                                <?php if($supplyCity->driver): ?>
                                                    <div class="driver-info">
                                                        <div class="driver-avatar">
                                                            <?php echo e(substr($supplyCity->driver->full_name, 0, 2)); ?>

                                                        </div>
                                                        <span class="driver-name"><?php echo e($supplyCity->driver->full_name); ?></span>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="not-assigned">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                                        Non assigné
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="trips-cell">
                                                <span class="trips-badge"><?php echo e($supplyCity->trips); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="quantity-cell">
                                                <span class="quantity-value"><?php echo e(number_format($supplyCity->quantity, 2, ',', ' ')); ?></span>
                                                <span class="quantity-unit">T</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="price-cell">
                                                <span class="price-value"><?php echo e(number_format($supplyCity->price, 0, ',', ' ')); ?></span>
                                                <span class="price-currency">FCFA</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="total-cell">
                                                <span class="total-value"><?php echo e(number_format($supplyCity->quantity * $supplyCity->price, 0, ',', ' ')); ?></span>
                                                <span class="total-currency">FCFA</span>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr class="summary-row">
                                    <td colspan="4" class="summary-label">
                                        <strong>
                                            <i class="fas fa-chart-line me-2"></i>
                                            Totaux
                                        </strong>
                                    </td>
                                    <td class="summary-quantity">
                                        <strong><?php echo e(number_format($supply->cities->sum('quantity'), 2, ',', ' ')); ?> T</strong>
                                    </td>
                                    <td class="summary-trips">
                                        <strong><?php echo e($supply->cities->sum('trips')); ?> voyages</strong>
                                    </td>
                                    <td class="summary-total">
                                        <strong><?php echo e(number_format($supply->real_total_amount, 0, ',', ' ')); ?> FCFA</strong>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Colonne latérale -->
        <div class="col-lg-4">
            <!-- Notes et informations supplémentaires -->
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">
                        <i class="fas fa-sticky-note me-2"></i>
                        Notes et observations
                    </h3>
                </div>
                <div class="modern-card-body">
                    <?php if($supply->notes): ?>
                        <div class="notes-content">
                            <p class="notes-text"><?php echo e($supply->notes); ?></p>
                        </div>
                    <?php else: ?>
                        <div class="no-notes">
                            <i class="fas fa-info-circle me-2"></i>
                            Aucune note ajoutée
                        </div>
                    <?php endif; ?>

                    <?php if($supply->status === 'rejected' && $supply->rejection_reason): ?>
                        <div class="rejection-reason">
                            <h5 class="rejection-title">
                                <i class="fas fa-ban me-2"></i>
                                Raison du rejet
                            </h5>
                            <div class="rejection-content">
                                <p class="rejection-text"><?php echo e($supply->rejection_reason); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Section Facture -->
            <?php if($supply->invoice_file): ?>
                <div class="modern-card mb-4">
                    <div class="modern-card-header">
                        <h3 class="modern-card-title">
                            <i class="fas fa-file-invoice me-2"></i>
                            Facture attachée
                        </h3>
                        <div class="card-actions">
                            <a href="<?php echo e(asset($supply->invoice_file)); ?>"
                               target="_blank"
                               class="btn-download-small">
                                <i class="fas fa-download"></i>
                            </a>
                        </div>
                    </div>
                    <div class="modern-card-body p-0">
                        <div class="invoice-preview-container">
                            <?php
                                $fileExtension = strtolower(pathinfo($supply->invoice_file, PATHINFO_EXTENSION));
                                $filePath = asset($supply->invoice_file);
                            ?>

                            <?php if(in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])): ?>
                                <!-- Prévisualisation d'image -->
                                <div class="invoice-image-preview">
                                    <img src="<?php echo e($filePath); ?>"
                                         alt="Facture <?php echo e($supply->reference); ?>"
                                         class="invoice-thumbnail"
                                         onclick="openImageModal('<?php echo e($filePath); ?>', '<?php echo e($supply->reference); ?>')">
                                    <div class="image-overlay">
                                        <i class="fas fa-search-plus"></i>
                                        <span>Cliquer pour agrandir</span>
                                    </div>
                                </div>
                            <?php elseif($fileExtension === 'pdf'): ?>
                                <!-- Prévisualisation PDF -->
                                <div class="pdf-preview-small">
                                    <div class="pdf-icon">
                                        <i class="fas fa-file-pdf"></i>
                                    </div>
                                    <div class="pdf-info">
                                        <span class="pdf-name">Facture <?php echo e($supply->reference); ?>.pdf</span>
                                        <a href="<?php echo e($filePath); ?>" target="_blank" class="pdf-link">
                                            <i class="fas fa-external-link-alt me-1"></i>
                                            Ouvrir le PDF
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- Fichier non prévisualisable -->
                                <div class="file-not-previewable-small">
                                    <div class="file-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <div class="file-info">
                                        <span class="file-name">Facture.<?php echo e(strtoupper($fileExtension)); ?></span>
                                        <a href="<?php echo e($filePath); ?>" target="_blank" class="file-link">
                                            <i class="fas fa-download me-1"></i>
                                            Télécharger
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Résumé financier -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">
                        <i class="fas fa-chart-pie me-2"></i>
                        Résumé financier
                    </h3>
                </div>
                <div class="modern-card-body">
                    <div class="financial-summary">
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="summary-content">
                                <span class="summary-label">Articles</span>
                                <span class="summary-value"><?php echo e($supply->details->count()); ?></span>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="summary-content">
                                <span class="summary-label">Destinations</span>
                                <span class="summary-value"><?php echo e($supply->cities->count()); ?></span>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-weight"></i>
                            </div>
                            <div class="summary-content">
                                <span class="summary-label">Quantité totale</span>
                                <span class="summary-value"><?php echo e(number_format($supply->cities->sum('quantity'), 2, ',', ' ')); ?> T</span>
                            </div>
                        </div>
                        <div class="summary-item total-summary">
                            <div class="summary-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="summary-content">
                                <span class="summary-label">Montant total</span>
                                <span class="summary-value-large"><?php echo e(number_format($supply->real_total_amount, 0, ',', ' ')); ?> FCFA</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de rejet moderne -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <form id="rejectForm" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-header modern-modal-header reject-header">
                    <div class="modal-icon reject-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="modal-title-wrapper">
                        <h5 class="modal-title" id="rejectModalLabel">Rejeter l'approvisionnement</h5>
                        <p class="modal-subtitle">Cette action nécessite une justification</p>
                    </div>
                    <button type="button" class="btn-close modern-btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body modern-modal-body">
                    <div class="form-group-modern">
                        <label for="rejection_reason" class="form-label-modern">
                            <i class="fas fa-comment-alt me-2"></i>
                            Raison du rejet
                        </label>
                        <textarea class="form-control-modern"
                                  id="rejection_reason"
                                  name="rejection_reason"
                                  rows="4"
                                  placeholder="Veuillez expliquer les raisons du rejet de cet approvisionnement..."
                                  required></textarea>
                        <div class="form-help-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Cette raison sera communiquée au demandeur
                        </div>
                    </div>
                </div>
                <div class="modal-footer modern-modal-footer">
                    <button type="button" class="btn btn-secondary-modern" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-danger-modern">
                        <i class="fas fa-ban me-2"></i>
                        Rejeter l'approvisionnement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal d'image -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content image-modal-content">
            <div class="modal-header image-modal-header">
                <h5 class="modal-title" id="imageModalLabel">Facture</h5>
                <div class="image-controls">
                    <button type="button" class="control-btn" onclick="zoomIn()">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button type="button" class="control-btn" onclick="zoomOut()">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button type="button" class="control-btn" onclick="resetZoom()">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button type="button" class="control-btn" onclick="rotateImage()">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body image-modal-body">
                <div class="image-container">
                    <img id="modalImage" src="" alt="" class="modal-image">
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Variables CSS pour la cohérence */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --info-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Header de détail */
.detail-header {
    background: var(--primary-gradient);
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.detail-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.detail-header-content {
    padding: 2rem;
    color: white;
    position: relative;
    z-index: 2;
}

.detail-icon {
    width: 70px;
    height: 70px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    backdrop-filter: blur(10px);
}

.detail-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-subtitle {
    opacity: 0.9;
    font-size: 1.1rem;
    margin-top: 0.5rem;
}

.status-badge-large {
    display: inline-flex;
    align-items: center;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
}

.status-badge-large.status-pending {
    background: rgba(251, 191, 36, 0.9);
    color: #92400e;
}

.status-badge-large.status-validated {
    background: rgba(16, 185, 129, 0.9);
    color: #065f46;
}

.status-badge-large.status-rejected {
    background: rgba(239, 68, 68, 0.9);
    color: #991b1b;
}

.btn-back-modern {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.btn-back-modern:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Actions rapides */
.quick-actions-bar {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
}

.action-btn-large {
    padding: 1rem 2rem;
    border-radius: 25px;
    border: none;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.validate-btn-large {
    background: var(--success-gradient);
    color: white;
}

.validate-btn-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(79, 172, 254, 0.3);
}

.reject-btn-large {
    background: var(--danger-gradient);
    color: white;
}

.reject-btn-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
}

/* Cartes modernes */
.modern-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    border: none;
    transition: var(--transition);
}

.modern-card:hover {
    box-shadow: var(--hover-shadow);
}

.modern-card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.modern-card-title {
    color: #2d3748;
    font-weight: 600;
    margin: 0;
    font-size: 1.2rem;
}

.modern-card-body {
    padding: 2rem;
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.items-count, .destinations-count {
    background: var(--primary-gradient);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

/* Grille d'informations */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 10px;
    border-left: 4px solid var(--primary-gradient);
    transition: var(--transition);
}

.info-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.info-icon {
    width: 45px;
    height: 45px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.info-content {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 0.85rem;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1.1rem;
    color: #2d3748;
    font-weight: 600;
    margin-top: 0.2rem;
}

.amount-highlight {
    color: #059669 !important;
    font-size: 1.3rem !important;
}

/* Tables modernes */
.modern-table-detailed {
    width: 100%;
    margin: 0;
}

.modern-table-detailed thead th {
    background: #f7fafc;
    border: none;
    padding: 1.2rem 1rem;
    font-weight: 600;
    color: #4a5568;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #e2e8f0;
}

.modern-table-detailed tbody td {
    padding: 1.5rem 1rem;
    border: none;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.item-row, .destination-row {
    transition: var(--transition);
}

.item-row:hover, .destination-row:hover {
    background: #f8fafc;
    transform: scale(1.01);
}

/* Cellules de produit */
.product-cell {
    display: flex;
    align-items: center;
}

.product-icon {
    width: 40px;
    height: 40px;
    background: var(--info-gradient);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
}

.product-info {
    display: flex;
    flex-direction: column;
}

.product-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 1rem;
}

.product-category {
    font-size: 0.8rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Cellules de quantité */
.quantity-cell {
    display: flex;
    align-items: baseline;
    gap: 0.3rem;
}

.quantity-value {
    font-weight: 700;
    color: #2d3748;
    font-size: 1.1rem;
}

.quantity-unit {
    font-size: 0.8rem;
    color: #718096;
    font-weight: 600;
}

.unit-badge {
    background: #e2e8f0;
    color: #4a5568;
    padding: 0.3rem 0.6rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Cellules de prix */
.price-cell, .total-cell {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.price-value, .total-value {
    font-weight: 700;
    color: #2d3748;
    font-size: 1rem;
}

.price-currency, .total-currency {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Cellules de ville */
.city-cell {
    display: flex;
    align-items: center;
}

.city-icon {
    width: 35px;
    height: 35px;
    background: var(--warning-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 0.8rem;
    font-size: 0.9rem;
}

.city-name {
    font-weight: 600;
    color: #2d3748;
}

/* Cellules de véhicule */
.vehicle-cell {
    display: flex;
    flex-direction: column;
}

.vehicle-info {
    display: flex;
    flex-direction: column;
}

.vehicle-number {
    font-weight: 600;
    color: #2d3748;
    font-family: 'Monaco', 'Menlo', monospace;
}

.vehicle-capacity {
    font-size: 0.8rem;
    color: #718096;
}

.not-assigned {
    color: #e53e3e;
    font-style: italic;
    font-size: 0.9rem;
}

/* Cellules de chauffeur */
.driver-cell {
    display: flex;
    align-items: center;
}

.driver-info {
    display: flex;
    align-items: center;
}

.driver-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: var(--success-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
    margin-right: 0.8rem;
    text-transform: uppercase;
}

.driver-name {
    font-weight: 500;
    color: #2d3748;
}

/* Badge de voyages */
.trips-badge {
    background: var(--info-gradient);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Lignes de total */
.total-row, .summary-row {
    background: linear-gradient(135deg, #f7fafc, #edf2f7) !important;
    font-weight: 700;
}

.total-row td, .summary-row td {
    border-top: 2px solid #e2e8f0 !important;
    padding: 1.5rem 1rem !important;
}

.total-label, .summary-label {
    color: #2d3748;
    font-size: 1.1rem;
}

.total-amount, .summary-total {
    color: #059669;
    font-size: 1.2rem;
}

/* Section Notes */
.notes-content {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid var(--info-gradient);
}

.notes-text {
    color: #4a5568;
    line-height: 1.6;
    margin: 0;
}

.no-notes {
    text-align: center;
    color: #718096;
    font-style: italic;
    padding: 2rem;
}

.rejection-reason {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.rejection-title {
    color: #e53e3e;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.rejection-content {
    background: linear-gradient(135deg, #fed7d7, #feb2b2);
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #e53e3e;
}

.rejection-text {
    color: #742a2a;
    line-height: 1.6;
    margin: 0;
    font-weight: 500;
}

/* Section Facture */
.btn-download-small {
    background: var(--success-gradient);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 8px;
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
}

.btn-download-small:hover {
    transform: scale(1.1);
    color: white;
}

.invoice-preview-container {
    position: relative;
}

.invoice-image-preview {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    border-radius: 8px;
}

.invoice-thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.invoice-image-preview:hover .image-overlay {
    opacity: 1;
}

.invoice-image-preview:hover .invoice-thumbnail {
    transform: scale(1.05);
}

.pdf-preview-small, .file-not-previewable-small {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 8px;
}

.pdf-icon, .file-icon {
    width: 50px;
    height: 50px;
    background: var(--danger-gradient);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.pdf-info, .file-info {
    display: flex;
    flex-direction: column;
}

.pdf-name, .file-name {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.pdf-link, .file-link {
    color: #3182ce;
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.pdf-link:hover, .file-link:hover {
    color: #2c5282;
}

/* Résumé financier */
.financial-summary {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.summary-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 10px;
    transition: var(--transition);
}

.summary-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.summary-item.total-summary {
    background: linear-gradient(135deg, #e6fffa, #b2f5ea);
    border: 2px solid #38b2ac;
}

.summary-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    background: var(--primary-gradient);
}

.summary-item.total-summary .summary-icon {
    background: var(--success-gradient);
}

.summary-content {
    display: flex;
    flex-direction: column;
}

.summary-label {
    font-size: 0.85rem;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    font-size: 1.1rem;
    color: #2d3748;
    font-weight: 700;
    margin-top: 0.2rem;
}

.summary-value-large {
    font-size: 1.3rem;
    color: #059669;
    font-weight: 700;
    margin-top: 0.2rem;
}

/* Modales modernes */
.modern-modal {
    border: none;
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    overflow: hidden;
}

.modern-modal-header {
    border: none;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.reject-header {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
}

.modal-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.reject-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.modal-title-wrapper {
    flex: 1;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #7f1d1d;
    margin: 0;
}

.modal-subtitle {
    font-size: 0.9rem;
    color: #991b1b;
    margin: 0.25rem 0 0 0;
    opacity: 0.8;
}

.modern-btn-close {
    background: rgba(127, 29, 29, 0.1);
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.modern-modal-body {
    padding: 2rem;
    background: white;
}

.form-group-modern {
    margin-bottom: 1.5rem;
}

.form-label-modern {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.form-control-modern {
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    resize: vertical;
    min-height: 100px;
}

.form-control-modern:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-help-text {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
}

.modern-modal-footer {
    background: #f9fafb;
    border: none;
    padding: 1.5rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-secondary-modern {
    background: #6b7280;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.btn-secondary-modern:hover {
    background: #4b5563;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(107, 114, 128, 0.3);
}

.btn-danger-modern {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.btn-danger-modern:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

/* Modal d'image */
.image-modal-content {
    background: #1a202c;
    border: none;
}

.image-modal-header {
    background: #2d3748;
    color: white;
    border: none;
    padding: 1rem 2rem;
    display: flex;
    justify-content: between;
    align-items: center;
}

.image-controls {
    display: flex;
    gap: 0.5rem;
}

.control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.image-modal-body {
    padding: 0;
    background: #1a202c;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
}

.image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;
}

.modal-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    transition: var(--transition);
    cursor: grab;
}

.modal-image:active {
    cursor: grabbing;
}

/* Responsive Design */
@media (max-width: 768px) {
    .detail-header-content {
        padding: 1.5rem;
    }

    .detail-title {
        font-size: 1.5rem;
    }

    .header-right {
        margin-top: 1rem;
    }

    .quick-actions-bar {
        padding: 1rem;
    }

    .action-btn-large {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .modern-card-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .modern-card-body {
        padding: 1rem;
    }

    .modern-table-detailed thead th {
        padding: 0.8rem 0.5rem;
        font-size: 0.8rem;
    }

    .modern-table-detailed tbody td {
        padding: 1rem 0.5rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-card {
    animation: fadeInUp 0.6s ease-out;
}

.modern-card:nth-child(1) { animation-delay: 0.1s; }
.modern-card:nth-child(2) { animation-delay: 0.2s; }
.modern-card:nth-child(3) { animation-delay: 0.3s; }
.modern-card:nth-child(4) { animation-delay: 0.4s; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Animation d'apparition des cartes
    const cards = document.querySelectorAll('.modern-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
});

// Variables pour le zoom d'image
let currentZoom = 1;
let currentRotation = 0;

function showRejectModal(id) {
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    const form = document.getElementById('rejectForm');

    // Remove any existing event listeners
    const newForm = form.cloneNode(true);
    form.parentNode.replaceChild(newForm, form);

    newForm.action = `<?php echo e(route('admin.supplies.rejectSupply', ['supply' => ':id'])); ?>`.replace(':id', id);

    newForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Afficher un loader sur le bouton
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Traitement...';
        submitBtn.disabled = true;

        fetch(this.action, {
            method: 'POST',
            body: new FormData(this),
            headers: {
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => Promise.reject(err));
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                modal.hide();

                Swal.fire({
                    title: '✅ Rejet effectué',
                    html: `
                        <div style="text-align: center; margin: 1rem 0;">
                            <div style="background: linear-gradient(135deg, #fee2e2, #fecaca); padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                                <i class="fas fa-ban" style="font-size: 2rem; color: #dc2626; margin-bottom: 0.5rem;"></i>
                                <p style="color: #7f1d1d; margin: 0; font-weight: 600;">${data.message}</p>
                            </div>
                        </div>
                    `,
                    icon: 'success',
                    iconColor: '#dc2626',
                    confirmButtonColor: '#dc2626',
                    confirmButtonText: '<i class="fas fa-check me-2"></i>Compris',
                    timer: 3000,
                    timerProgressBar: true
                }).then(() => {
                    window.location.href = '<?php echo e(route('admin.supplies.index')); ?>';
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            const message = error.message || 'Une erreur est survenue lors du rejet de l\'approvisionnement.';

            Swal.fire({
                title: '❌ Erreur de rejet',
                html: `
                    <div style="text-align: center; margin: 1rem 0;">
                        <div style="background: linear-gradient(135deg, #fee2e2, #fecaca); padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #dc2626; margin-bottom: 0.5rem;"></i>
                            <p style="color: #7f1d1d; margin: 0; font-weight: 600;">${message}</p>
                        </div>
                    </div>
                `,
                icon: 'error',
                iconColor: '#dc2626',
                confirmButtonColor: '#dc2626',
                confirmButtonText: '<i class="fas fa-times me-2"></i>Fermer'
            });
        })
        .finally(() => {
            // Restaurer le bouton
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });

    modal.show();
}

function confirmValidation(id) {
    Swal.fire({
        title: '✅ Validation d\'approvisionnement',
        html: `
            <div style="text-align: left; margin: 1rem 0;">
                <p style="color: #6b7280; margin-bottom: 1rem;">Pour confirmer la validation de cet approvisionnement, veuillez saisir <strong style="color: #059669;">VALIDER</strong> dans le champ ci-dessous :</p>
            </div>
        `,
        input: 'text',
        inputPlaceholder: 'Tapez VALIDER pour confirmer',
        icon: 'question',
        iconColor: '#059669',
        showCancelButton: true,
        confirmButtonColor: '#059669',
        cancelButtonColor: '#6b7280',
        confirmButtonText: '<i class="fas fa-check me-2"></i>Valider',
        cancelButtonText: '<i class="fas fa-times me-2"></i>Annuler',
        buttonsStyling: true,
        inputValidator: (value) => {
            if (!value) {
                return '<i class="fas fa-exclamation-triangle"></i> Vous devez saisir quelque chose!';
            }
            if (value.toUpperCase() !== 'VALIDER') {
                return '<i class="fas fa-times-circle"></i> Veuillez saisir exactement "VALIDER"';
            }
        },
        preConfirm: () => {
            return new Promise((resolve) => {
                Swal.showLoading();
                setTimeout(() => {
                    resolve();
                }, 1000);
            });
        }
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`<?php echo e(route('admin.supplies.validateSupply', ':id')); ?>`.replace(':id', id), {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: '🎉 Validation réussie !',
                        html: `
                            <div style="text-align: center; margin: 1rem 0;">
                                <div style="background: linear-gradient(135deg, #d1fae5, #a7f3d0); padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                                    <i class="fas fa-check-circle" style="font-size: 2rem; color: #059669; margin-bottom: 0.5rem;"></i>
                                    <p style="color: #065f46; margin: 0; font-weight: 600;">${data.message || 'L\'approvisionnement a été validé avec succès.'}</p>
                                </div>
                            </div>
                        `,
                        icon: 'success',
                        iconColor: '#059669',
                        confirmButtonColor: '#059669',
                        confirmButtonText: '<i class="fas fa-check me-2"></i>Parfait !',
                        timer: 3000,
                        timerProgressBar: true
                    }).then(() => {
                        window.location.href = data.redirect || '<?php echo e(route('admin.supplies.index')); ?>';
                    });
                } else {
                    throw new Error(data.message || 'Une erreur est survenue');
                }
            })
            .catch(error => {
                Swal.fire({
                    title: '❌ Erreur de validation',
                    html: `
                        <div style="text-align: center; margin: 1rem 0;">
                            <div style="background: linear-gradient(135deg, #fee2e2, #fecaca); padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #dc2626; margin-bottom: 0.5rem;"></i>
                                <p style="color: #7f1d1d; margin: 0; font-weight: 600;">${error.message || "Une erreur s'est produite lors de la validation."}</p>
                            </div>
                        </div>
                    `,
                    icon: 'error',
                    iconColor: '#dc2626',
                    confirmButtonColor: '#dc2626',
                    confirmButtonText: '<i class="fas fa-times me-2"></i>Compris'
                });
            });
        }
    });
}

// Fonctions pour la gestion des images
function openImageModal(imageSrc, title) {
    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('imageModalLabel');

    modalImage.src = imageSrc;
    modalImage.alt = title;
    modalTitle.textContent = `Facture ${title}`;

    // Réinitialiser le zoom et la rotation
    currentZoom = 1;
    currentRotation = 0;
    updateImageTransform();

    modal.show();
}

function zoomIn() {
    currentZoom = Math.min(currentZoom * 1.2, 5);
    updateImageTransform();
}

function zoomOut() {
    currentZoom = Math.max(currentZoom / 1.2, 0.5);
    updateImageTransform();
}

function resetZoom() {
    currentZoom = 1;
    currentRotation = 0;
    updateImageTransform();
}

function rotateImage() {
    currentRotation = (currentRotation + 90) % 360;
    updateImageTransform();
}

function updateImageTransform() {
    const modalImage = document.getElementById('modalImage');
    modalImage.style.transform = `scale(${currentZoom}) rotate(${currentRotation}deg)`;
}

// Gestion du drag pour l'image
let isDragging = false;
let startX, startY, scrollLeft, scrollTop;

document.addEventListener('DOMContentLoaded', function() {
    const modalImage = document.getElementById('modalImage');

    if (modalImage) {
        modalImage.addEventListener('mousedown', function(e) {
            isDragging = true;
            startX = e.pageX - modalImage.offsetLeft;
            startY = e.pageY - modalImage.offsetTop;
            scrollLeft = modalImage.scrollLeft;
            scrollTop = modalImage.scrollTop;
        });

        modalImage.addEventListener('mouseleave', function() {
            isDragging = false;
        });

        modalImage.addEventListener('mouseup', function() {
            isDragging = false;
        });

        modalImage.addEventListener('mousemove', function(e) {
            if (!isDragging) return;
            e.preventDefault();
            const x = e.pageX - modalImage.offsetLeft;
            const y = e.pageY - modalImage.offsetTop;
            const walkX = (x - startX) * 2;
            const walkY = (y - startY) * 2;
            modalImage.scrollLeft = scrollLeft - walkX;
            modalImage.scrollTop = scrollTop - walkY;
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/supplies/show.blade.php ENDPATH**/ ?>