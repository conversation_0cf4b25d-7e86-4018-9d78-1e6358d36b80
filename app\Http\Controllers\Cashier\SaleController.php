<?php

namespace App\Http\Controllers\Cashier;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Product;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SaleController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:cashier']);
    }

    public function index()
    {
        // Récupérer toutes les ventes avec le produit associé
        $allSales = Sale::with(['product'])->get();
        
        // Trier les ventes selon l'état de règlement
        $sortedSales = $allSales->sort(function ($a, $b) {
            // Calculer la progression du paiement pour chaque vente
            $progressA = $a->total_amount > 0 ? ($a->amount_paid ?? 0) / $a->total_amount : 0;
            $progressB = $b->total_amount > 0 ? ($b->amount_paid ?? 0) / $b->total_amount : 0;
            
            // Cas spécial pour les ventes annulées par l'admin (priorité la plus basse)
            if ($a->isCancelledByAdmin() && !$b->isCancelledByAdmin()) {
                return 1; // a vient après b
            }
            if (!$a->isCancelledByAdmin() && $b->isCancelledByAdmin()) {
                return -1; // a vient avant b
            }
            
            // Ordre de priorité : 1. Payé (bleu), 2. Partiellement payé (orange), 3. Non payé (rouge)
            if ($progressA >= 0.999 && $progressB < 0.999) {
                return -1; // a (payé) vient avant b
            }
            if ($progressA < 0.999 && $progressB >= 0.999) {
                return 1; // a vient après b (payé)
            }
            
            if ($progressA > 0 && $progressB == 0) {
                return -1; // a (partiellement payé) vient avant b (non payé)
            }
            if ($progressA == 0 && $progressB > 0) {
                return 1; // a (non payé) vient après b (partiellement payé)
            }
            
            // Pour les ventes de même catégorie, trier par date (plus récent d'abord)
            return $b->created_at <=> $a->created_at;
        });
        
        // Paginer les résultats manuellement
        $page = request()->get('page', 1);
        $perPage = 10;
        $total = $sortedSales->count();
        
        $sales = new \Illuminate\Pagination\LengthAwarePaginator(
            $sortedSales->forPage($page, $perPage),
            $total,
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );
        
        return view('cashier.sales.index', compact('sales'));
    }
    
    /**
     * Affiche la vue moderne des ventes
     *
     * @return \Illuminate\View\View
     */
    public function modern()
    {
        $sales = Sale::with(['product'])
            ->latest()
            ->paginate(10);
            
        return view('cashier.sales.modern-sales', compact('sales'));
    }

    public function create()
    {
        $products = Product::where('stock_quantity', '>', 0)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('cashier.sales.create', compact('products'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'products' => 'required|array',
            'products.*.id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
        ]);

        $sale = new Sale();
        $sale->cashier_id = auth()->id();
        $sale->sale_date = Carbon::now();
        $sale->total_amount = 0;
        $sale->save();

        $totalAmount = 0;
        foreach ($request->products as $product) {
            $productModel = Product::find($product['id']);
            if ($productModel->stock_quantity >= $product['quantity']) {
                $amount = $productModel->price * $product['quantity'];
                // Set product_id directly since it's a belongsTo relationship
                $sale->product_id = $product['id'];
                $sale->quantity = $product['quantity'];
                $sale->unit_price = $productModel->price;
                $sale->total_amount = $amount;
                $productModel->decrement('stock_quantity', $product['quantity']);
                $totalAmount += $amount;
            }
        }

        $sale->total_amount = $totalAmount;
        $sale->save();

        return redirect()->route('cashier.sales.show', $sale)
            ->with('success', 'Vente créée avec succès.');
    }

    public function show(Sale $sale)
    {
        // Charger toutes les relations nécessaires pour afficher les détails complets de la vente
        $sale->load([
            'supply.details.product', 
            'city', 
            'createdBy', 
            'driver'
            // Ne pas charger 'payments' et 'paymentSchedules' car ces relations causent des erreurs
            // La table payments n'a probablement pas de colonne sale_id
        ]);
        
        // Récupérer les informations spécifiques de l'approvisionnement pour cette vente
        $supply = $sale->supply;
        $supplyCity = null;
        
        if ($supply) {
            // Essayer de trouver la correspondance exacte par véhicule et ville
            $supplyCity = $supply->cities->where('vehicle_id', $sale->vehicle_id)
                                       ->where('city_id', $sale->city_id)
                                       ->first();
            
            // Si on ne trouve pas, essayer par véhicule uniquement
            if (!$supplyCity) {
                $supplyCity = $supply->cities->where('vehicle_id', $sale->vehicle_id)->first();
            }
            
            // Si on ne trouve toujours pas, essayer par ville uniquement
            if (!$supplyCity) {
                $supplyCity = $supply->cities->where('city_id', $sale->city_id)->first();
            }
        }
        
        return view('cashier.sales.show', compact('sale', 'supplyCity'));
    }
}
