<?php

echo "🔧 Test de la correction du sélecteur CSS...\n\n";

// Vérifier que le fichier dashboard.blade.php ne contient plus de :contains
$dashboardFile = 'resources/views/admin/dashboard.blade.php';

if (!file_exists($dashboardFile)) {
    echo "❌ Fichier dashboard.blade.php non trouvé\n";
    exit(1);
}

$content = file_get_contents($dashboardFile);

echo "🔍 Vérification des sélecteurs CSS...\n";

// Vérifier qu'il n'y a plus de :contains
if (strpos($content, ':contains') !== false) {
    echo "❌ Le sélecteur :contains est encore présent dans le fichier\n";
    
    // Trouver les lignes contenant :contains
    $lines = explode("\n", $content);
    foreach ($lines as $lineNumber => $line) {
        if (strpos($line, ':contains') !== false) {
            echo "   Ligne " . ($lineNumber + 1) . ": " . trim($line) . "\n";
        }
    }
} else {
    echo "✅ Aucun sélecteur :contains trouvé\n";
}

// Vérifier que les IDs sont présents
$requiredIds = [
    'total-products-count',
    'normal-stock-count', 
    'low-stock-count',
    'out-of-stock-count'
];

echo "\n🏷️  Vérification des IDs des cartes...\n";

foreach ($requiredIds as $id) {
    if (strpos($content, 'id="' . $id . '"') !== false) {
        echo "✅ ID '$id' trouvé\n";
    } else {
        echo "❌ ID '$id' manquant\n";
    }
}

// Vérifier que la fonction updateStockSummaryCards utilise getElementById
echo "\n📝 Vérification de la fonction JavaScript...\n";

if (strpos($content, 'getElementById') !== false) {
    echo "✅ La fonction utilise getElementById\n";
} else {
    echo "❌ La fonction n'utilise pas getElementById\n";
}

if (strpos($content, 'querySelector') !== false && strpos($content, ':contains') === false) {
    echo "✅ querySelector utilisé sans :contains\n";
} else if (strpos($content, 'querySelector') === false) {
    echo "✅ Pas de querySelector problématique\n";
} else {
    echo "⚠️  querySelector présent - vérification manuelle nécessaire\n";
}

echo "\n🧪 TESTS RECOMMANDÉS:\n";
echo "====================\n";
echo "1. Videz le cache: php artisan view:clear\n";
echo "2. Actualisez le navigateur: Ctrl+F5\n";
echo "3. Testez l'actualisation des stocks\n";
echo "4. Vérifiez la console (F12) - plus d'erreur de sélecteur\n\n";

echo "🌐 URL de test: http://127.0.0.1:8000/admin/dashboard\n";
echo "   → Onglet 'État des Stocks' → Bouton 'Actualiser'\n\n";

echo "✅ Correction terminée!\n";
echo "L'erreur 'querySelector' avec ':contains' devrait être résolue.\n";
