<?php

namespace App\Listeners;

use App\Events\StockUpdated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class HandleStockUpdate implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(StockUpdated $event): void
    {
        // Log du mouvement de stock
        Log::info('Mouvement de stock détecté', [
            'product_id' => $event->product->id,
            'product_name' => $event->product->name,
            'type' => $event->type,
            'quantity_change' => $event->quantityChange,
            'previous_stock' => $event->previousStock,
            'new_stock' => $event->newStock
        ]);

        // Invalider le cache des statistiques du dashboard
        Cache::forget('dashboard_stock_stats');
        Cache::forget('dashboard_low_stock_products');
        Cache::forget('dashboard_stock_movements');

        // Mettre à jour les statistiques en temps réel
        $this->updateRealTimeStats($event);

        // Gérer les alertes de stock
        $this->handleStockAlerts($event);
    }

    /**
     * Met à jour les statistiques en temps réel
     */
    private function updateRealTimeStats(StockUpdated $event): void
    {
        // Calculer et mettre en cache les nouvelles statistiques
        $stockStats = [
            'total_products' => \App\Models\Product::where('is_active', true)->count(),
            'low_stock_count' => \App\Models\Product::where('is_active', true)->where('stock_quantity', '<=', 10)->count(),
            'out_of_stock_count' => \App\Models\Product::where('is_active', true)->where('stock_quantity', '<=', 0)->count(),
            'last_updated' => now()->toISOString()
        ];

        Cache::put('dashboard_stock_stats', $stockStats, now()->addMinutes(5));
    }

    /**
     * Gère les alertes de stock
     */
    private function handleStockAlerts(StockUpdated $event): void
    {
        $product = $event->product;
        $newStock = $event->newStock;

        // Alerte de rupture de stock
        if ($newStock <= 0) {
            Log::warning('Produit en rupture de stock', [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'stock' => $newStock
            ]);

            // Ici, on pourrait envoyer des notifications par email, SMS, etc.
            // $this->sendOutOfStockNotification($product);
        }
        // Alerte de stock faible
        elseif ($newStock <= 10) {
            Log::warning('Produit avec stock faible', [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'stock' => $newStock
            ]);

            // Ici, on pourrait envoyer des notifications par email, SMS, etc.
            // $this->sendLowStockNotification($product);
        }
    }

    /**
     * Failed job handling
     */
    public function failed(StockUpdated $event, \Throwable $exception): void
    {
        Log::error('Échec du traitement de l\'événement StockUpdated', [
            'product_id' => $event->product->id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
