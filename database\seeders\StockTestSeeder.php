<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;
use App\Models\Supply;
use App\Models\Supplier;
use App\Models\User;
use App\Models\StockHistory;
use App\Services\StockService;

class StockTestSeeder extends Seeder
{
    /**
     * Seeder pour créer des données de test pour le système de suivi des stocks
     */
    public function run()
    {
        $this->command->info('🚀 Création des données de test pour le système de stocks...');

        // Créer des catégories si elles n'existent pas
        $categories = [
            'Ciment' => 'Produits cimentiers',
            'Sable' => 'Différents types de sable',
            'Gravier' => 'Graviers et pierres',
            'Fer' => 'Fers à béton et armatures',
            'Outils' => 'Outils de construction'
        ];

        foreach ($categories as $name => $description) {
            Category::firstOrCreate(['name' => $name], ['description' => $description]);
        }

        // Créer des produits avec différents niveaux de stock
        $products = [
            [
                'name' => 'Ciment Portland 50kg',
                'category' => 'Ciment',
                'stock_quantity' => 0, // Rupture de stock
                'price' => 4500,
                'unit' => 'sac'
            ],
            [
                'name' => 'Sable fin',
                'category' => 'Sable',
                'stock_quantity' => 8, // Stock faible
                'price' => 15000,
                'unit' => 'm³'
            ],
            [
                'name' => 'Gravier 15/25',
                'category' => 'Gravier',
                'stock_quantity' => 5, // Stock faible
                'price' => 18000,
                'unit' => 'm³'
            ],
            [
                'name' => 'Fer 12mm',
                'category' => 'Fer',
                'stock_quantity' => 150, // Stock normal
                'price' => 650,
                'unit' => 'barre'
            ],
            [
                'name' => 'Fer 8mm',
                'category' => 'Fer',
                'stock_quantity' => 200, // Stock normal
                'price' => 450,
                'unit' => 'barre'
            ],
            [
                'name' => 'Pelle',
                'category' => 'Outils',
                'stock_quantity' => 25, // Stock normal
                'price' => 8500,
                'unit' => 'pièce'
            ],
            [
                'name' => 'Sable gros',
                'category' => 'Sable',
                'stock_quantity' => 12, // Stock normal
                'price' => 16000,
                'unit' => 'm³'
            ],
            [
                'name' => 'Ciment blanc 25kg',
                'category' => 'Ciment',
                'stock_quantity' => 3, // Stock faible
                'price' => 6500,
                'unit' => 'sac'
            ]
        ];

        foreach ($products as $productData) {
            $category = Category::where('name', $productData['category'])->first();
            
            $product = Product::firstOrCreate(
                ['name' => $productData['name']],
                [
                    'category_id' => $category->id,
                    'stock_quantity' => $productData['stock_quantity'],
                    'price' => $productData['price'],
                    'unit' => $productData['unit'],
                    'is_active' => true,
                    'description' => 'Produit de test pour le système de stocks'
                ]
            );

            $this->command->info("✅ Produit créé: {$product->name} (Stock: {$product->stock_quantity})");
        }

        // Créer un fournisseur de test
        $supplier = Supplier::firstOrCreate(
            ['name' => 'Fournisseur Test'],
            [
                'email' => '<EMAIL>',
                'phone' => '123456789',
                'address' => 'Adresse test'
            ]
        );

        // Créer quelques mouvements de stock récents pour l'historique
        $stockService = new StockService();
        $products = Product::all();

        foreach ($products->take(5) as $product) {
            // Simuler quelques mouvements récents
            for ($i = 0; $i < rand(2, 5); $i++) {
                $quantity = rand(5, 50);
                $type = rand(0, 1) ? 'supply' : 'sale';
                
                StockHistory::create([
                    'product_id' => $product->id,
                    'type' => $type,
                    'quantity' => $quantity,
                    'previous_stock' => $product->stock_quantity,
                    'new_stock' => $product->stock_quantity + ($type === 'supply' ? $quantity : -$quantity),
                    'user_id' => 1, // Supposer qu'il y a un utilisateur avec ID 1
                    'created_at' => now()->subDays(rand(1, 7)),
                    'updated_at' => now()->subDays(rand(1, 7))
                ]);
            }
        }

        $this->command->info('📊 Historique des mouvements de stock créé');

        // Afficher un résumé
        $totalProducts = Product::count();
        $outOfStock = Product::where('stock_quantity', 0)->count();
        $lowStock = Product::where('stock_quantity', '>', 0)->where('stock_quantity', '<=', 10)->count();
        $normalStock = Product::where('stock_quantity', '>', 10)->count();

        $this->command->info('');
        $this->command->info('📈 RÉSUMÉ DES DONNÉES DE TEST:');
        $this->command->info("   Total produits: {$totalProducts}");
        $this->command->info("   Rupture de stock: {$outOfStock}");
        $this->command->info("   Stock faible: {$lowStock}");
        $this->command->info("   Stock normal: {$normalStock}");
        $this->command->info('');
        $this->command->info('🎯 Vous pouvez maintenant tester le système de stocks sur le dashboard admin!');
        $this->command->info('   URL: http://127.0.0.1:8000/admin/dashboard');
        $this->command->info('');
        $this->command->info('💡 Pour tester les mises à jour automatiques:');
        $this->command->info('   1. Validez un approvisionnement');
        $this->command->info('   2. Créez une vente');
        $this->command->info('   3. Observez les changements dans l\'onglet "État des Stocks"');
    }
}
