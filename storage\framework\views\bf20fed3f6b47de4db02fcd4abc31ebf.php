<?php $__env->startSection('title', 'Gestion des catégories'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-0">
    <!-- En-tête moderne et attrayant -->
    <div class="modern-header-wrapper">
        <div class="modern-header-background">
            <div class="header-gradient-overlay"></div>
            <div class="header-pattern"></div>
        </div>

        <div class="modern-header-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <!-- Navigation breadcrumb moderne -->
                    <nav aria-label="breadcrumb" class="modern-breadcrumb">
                        <ol class="breadcrumb-modern">
                            <li class="breadcrumb-item-modern">
                                <a href="<?php echo e(route('admin.dashboard')); ?>" class="breadcrumb-link">
                                    <i class="fas fa-home"></i>
                                    <span>Dashboard</span>
                                </a>
                            </li>
                            <li class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </li>
                            <li class="breadcrumb-item-modern active">
                                <span>Catégories</span>
                            </li>
                        </ol>
                    </nav>

                    <!-- Titre principal avec animation -->
                    <div class="header-title-section">
                        <div class="title-icon-wrapper">
                            <div class="title-icon-bg">
                                <i class="fas fa-layer-group"></i>
                            </div>
                        </div>
                        <div class="title-content">
                            <h1 class="header-title">
                                Gestion des catégories
                                <span class="title-highlight">Produits</span>
                            </h1>
                            <p class="header-subtitle">
                                <i class="fas fa-info-circle me-2"></i>
                                Organisez vos produits en catégories pour une meilleure gestion
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <!-- Boutons d'action -->
                    <div class="header-actions">
                        <div class="action-buttons-group">
                            <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                                <a href="<?php echo e(route('admin.categories.create')); ?>" class="btn btn-success btn-modern">
                                    <i class="fas fa-plus me-2"></i>
                                    Nouvelle catégorie
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="main-content-wrapper">
        <!-- Statistiques rapides -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card stats-primary">
                    <div class="stats-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number"><?php echo e($categories->total()); ?></h3>
                        <p class="stats-label">Total catégories</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card stats-success">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number"><?php echo e($categories->where('is_active', true)->count()); ?></h3>
                        <p class="stats-label">Catégories actives</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card stats-info">
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number"><?php echo e($categories->sum('products_count')); ?></h3>
                        <p class="stats-label">Total produits</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card stats-warning">
                    <div class="stats-icon">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-number"><?php echo e($categories->where('is_active', false)->count()); ?></h3>
                        <p class="stats-label">Catégories inactives</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenu des catégories -->
        <div class="row">
            <div class="col-12">
                <?php if($categories->isEmpty()): ?>
                    <!-- État vide moderne -->
                    <div class="empty-state-modern">
                        <div class="empty-state-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h3 class="empty-state-title">Aucune catégorie pour le moment</h3>
                        <p class="empty-state-description">
                            Commencez par créer une catégorie pour organiser vos produits de manière efficace
                        </p>
                        <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                            <a href="<?php echo e(route('admin.categories.create')); ?>" class="btn btn-primary btn-lg empty-state-action">
                                <i class="fas fa-plus me-2"></i>
                                Créer votre première catégorie
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <!-- Vue en cartes modernes -->
                    <div class="categories-grid">
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $categoryName = strtolower($category->name);
                                $iconClass = match($categoryName) {
                                    'ciment' => 'fas fa-industry',
                                    'fer' => 'fas fa-hammer',
                                    'brique' => 'fas fa-th-large',
                                    'sable' => 'fas fa-mountain',
                                    'gravier' => 'fas fa-gem',
                                    'bois' => 'fas fa-tree',
                                    'acier' => 'fas fa-cog',
                                    'peinture' => 'fas fa-paint-brush',
                                    'carrelage' => 'fas fa-th',
                                    'plomberie' => 'fas fa-wrench',
                                    'électricité' => 'fas fa-bolt',
                                    default => 'fas fa-cube'
                                };
                                $gradientClass = match($categoryName) {
                                    'ciment' => 'gradient-blue',
                                    'fer' => 'gradient-orange',
                                    'brique' => 'gradient-red',
                                    'sable' => 'gradient-yellow',
                                    'gravier' => 'gradient-purple',
                                    'bois' => 'gradient-green',
                                    'acier' => 'gradient-gray',
                                    'peinture' => 'gradient-pink',
                                    'carrelage' => 'gradient-teal',
                                    'plomberie' => 'gradient-cyan',
                                    'électricité' => 'gradient-indigo',
                                    default => 'gradient-primary'
                                };
                            ?>

                            <div class="category-card <?php echo e($gradientClass); ?>" data-category-id="<?php echo e($category->id); ?>">
                                <!-- Badge de statut -->
                                <div class="category-status-badge">
                                    <?php if($category->is_active): ?>
                                        <span class="status-active">
                                            <i class="fas fa-check-circle"></i>
                                            Active
                                        </span>
                                    <?php else: ?>
                                        <span class="status-inactive">
                                            <i class="fas fa-pause-circle"></i>
                                            Inactive
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- Icône de la catégorie -->
                                <div class="category-icon">
                                    <i class="<?php echo e($iconClass); ?>"></i>
                                </div>

                                <!-- Contenu de la carte -->
                                <div class="category-content">
                                    <h3 class="category-name"><?php echo e($category->name); ?></h3>
                                    <p class="category-slug"><?php echo e($category->slug); ?></p>

                                    <?php if($category->description): ?>
                                        <p class="category-description">
                                            <?php echo e(Str::limit($category->description, 80)); ?>

                                        </p>
                                    <?php endif; ?>

                                    <!-- Statistiques -->
                                    <div class="category-stats">
                                        <div class="stat-item">
                                            <i class="fas fa-boxes"></i>
                                            <span class="stat-number"><?php echo e($category->products_count); ?></span>
                                            <span class="stat-label"><?php echo e($category->products_count > 1 ? 'Produits' : 'Produit'); ?></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="category-actions">
                                    <div class="action-buttons">
                                        <a href="<?php echo e(route('admin.categories.edit', $category)); ?>"
                                           class="action-btn edit-btn"
                                           data-bs-toggle="tooltip"
                                           title="Modifier la catégorie">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <button type="button"
                                                class="action-btn delete-btn"
                                                data-bs-toggle="tooltip"
                                                title="Supprimer la catégorie"
                                                onclick="confirmDelete(<?php echo e($category->id); ?>, '<?php echo e($category->name); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>

                                        <form id="delete-form-<?php echo e($category->id); ?>"
                                              action="<?php echo e(route('admin.categories.destroy', $category)); ?>"
                                              method="POST"
                                              style="display: none;">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                        </form>
                                    </div>
                                </div>

                                <!-- Effet de hover -->
                                <div class="category-hover-effect"></div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- Pagination moderne -->
                    <?php if($categories->hasPages()): ?>
                        <div class="modern-pagination">
                            <?php echo e($categories->links()); ?>

                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* ===== STYLES POUR LA PAGE DES CATÉGORIES MODERNE ===== */

/* Variables CSS */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

/* En-tête moderne */
.modern-header-wrapper {
    position: relative;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    overflow: hidden;
}

.modern-header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
}

.header-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
}

.header-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 50%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 50px 50px;
}

.modern-header-content {
    position: relative;
    z-index: 2;
    padding: 2rem;
    color: white;
}

/* Breadcrumb moderne */
.modern-breadcrumb {
    margin-bottom: 1.5rem;
}

.breadcrumb-modern {
    display: flex;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-wrap: wrap;
}

.breadcrumb-item-modern {
    display: flex;
    align-items: center;
}

.breadcrumb-link {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
}

.breadcrumb-link:hover {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.breadcrumb-link i {
    margin-right: 0.5rem;
}

.breadcrumb-separator {
    margin: 0 0.5rem;
    color: rgba(255,255,255,0.6);
}

.breadcrumb-item-modern.active span {
    color: white;
    font-weight: 600;
}

/* Titre principal */
.header-title-section {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.title-icon-wrapper {
    margin-right: 1.5rem;
}

.title-icon-bg {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.header-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.title-highlight {
    color: rgba(255,255,255,0.8);
    font-weight: 400;
}

.header-subtitle {
    margin: 0.5rem 0 0 0;
    color: rgba(255,255,255,0.8);
    font-size: 1.1rem;
}

/* Boutons d'action dans l'en-tête */
.header-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
}

.action-buttons-group {
    display: flex;
    gap: 1rem;
}

.btn-modern {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-success.btn-modern {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.btn-success.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

/* Contenu principal */
.main-content-wrapper {
    padding: 0 1.5rem;
}

/* Cartes de statistiques */
.stats-card {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stats-primary::before { background: linear-gradient(90deg, #667eea, #764ba2); }
.stats-success::before { background: linear-gradient(90deg, #28a745, #20c997); }
.stats-info::before { background: linear-gradient(90deg, #17a2b8, #6f42c1); }
.stats-warning::before { background: linear-gradient(90deg, #ffc107, #fd7e14); }

.stats-card {
    display: flex;
    align-items: center;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
    color: white;
}

.stats-primary .stats-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
.stats-success .stats-icon { background: linear-gradient(135deg, #28a745, #20c997); }
.stats-info .stats-icon { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
.stats-warning .stats-icon { background: linear-gradient(135deg, #ffc107, #fd7e14); }

.stats-content {
    flex: 1;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #2d3748;
}

.stats-label {
    color: #718096;
    margin: 0;
    font-weight: 500;
}

/* État vide moderne */
.empty-state-modern {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
}

.empty-state-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #667eea;
}

.empty-state-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.empty-state-description {
    font-size: 1.1rem;
    color: #718096;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-state-action {
    border-radius: 25px;
    padding: 1rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

/* Grille des catégories */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Cartes de catégories */
.category-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    border: none;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
}

/* Gradients pour les catégories */
.gradient-primary::before { background: linear-gradient(90deg, #667eea, #764ba2); }
.gradient-blue::before { background: linear-gradient(90deg, #4facfe, #00f2fe); }
.gradient-orange::before { background: linear-gradient(90deg, #fa709a, #fee140); }
.gradient-red::before { background: linear-gradient(90deg, #ff9a9e, #fecfef); }
.gradient-yellow::before { background: linear-gradient(90deg, #ffecd2, #fcb69f); }
.gradient-purple::before { background: linear-gradient(90deg, #a8edea, #fed6e3); }
.gradient-green::before { background: linear-gradient(90deg, #d299c2, #fef9d7); }
.gradient-gray::before { background: linear-gradient(90deg, #89f7fe, #66a6ff); }
.gradient-pink::before { background: linear-gradient(90deg, #fdbb2d, #22c1c3); }
.gradient-teal::before { background: linear-gradient(90deg, #ee9ca7, #ffdde1); }
.gradient-cyan::before { background: linear-gradient(90deg, #2196f3, #21cbf3); }
.gradient-indigo::before { background: linear-gradient(90deg, #673ab7, #512da8); }

/* Badge de statut */
.category-status-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 2;
}

.status-active, .status-inactive {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.status-active {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-inactive {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.status-active i, .status-inactive i {
    margin-right: 0.5rem;
}

/* Icône de la catégorie */
.category-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #667eea;
    transition: all 0.3s ease;
}

.category-card:hover .category-icon {
    transform: scale(1.1);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
}

/* Contenu de la carte */
.category-content {
    text-align: center;
    margin-bottom: 1.5rem;
}

.category-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.category-slug {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-family: 'Courier New', monospace;
}

.category-description {
    color: #4a5568;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

/* Statistiques */
.category-stats {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    min-width: 80px;
}

.stat-item i {
    color: #667eea;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: #718096;
    font-weight: 500;
}

/* Actions */
.category-actions {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    opacity: 0;
    transition: all 0.3s ease;
}

.category-card:hover .category-actions {
    opacity: 1;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.edit-btn {
    background: rgba(0, 123, 255, 0.2);
    color: #007bff;
    border: 1px solid rgba(0, 123, 255, 0.3);
}

.edit-btn:hover {
    background: #007bff;
    color: white;
    transform: scale(1.1);
}

.delete-btn {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.delete-btn:hover {
    background: #dc3545;
    color: white;
    transform: scale(1.1);
}

/* Effet de hover */
.category-hover-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.category-card:hover .category-hover-effect {
    opacity: 1;
}

/* Pagination moderne */
.modern-pagination {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

.modern-pagination .pagination {
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.modern-pagination .page-link {
    border: none;
    padding: 0.75rem 1rem;
    color: #667eea;
    background: white;
    transition: all 0.3s ease;
}

.modern-pagination .page-link:hover {
    background: #667eea;
    color: white;
}

.modern-pagination .page-item.active .page-link {
    background: #667eea;
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .modern-header-content {
        padding: 1rem;
    }

    .header-title {
        font-size: 1.8rem;
    }

    .title-icon-bg {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .action-buttons-group {
        flex-direction: column;
        width: 100%;
    }

    .main-content-wrapper {
        padding: 0 0.5rem;
    }

    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .category-card {
        padding: 1.5rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.category-card {
    animation: slideInUp 0.5s ease-out;
}

.stats-card {
    animation: slideInUp 0.3s ease-out;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // ===== GESTION DES TOOLTIPS =====

    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // ===== FONCTION DE CONFIRMATION DE SUPPRESSION =====

    window.confirmDelete = function(categoryId, categoryName) {
        // Créer une modal de confirmation moderne
        const modalHtml = `
            <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content modern-modal">
                        <div class="modal-header border-0 pb-0">
                            <div class="w-100 text-center">
                                <div class="delete-icon-wrapper mb-3">
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                </div>
                                <h4 class="modal-title">Confirmer la suppression</h4>
                            </div>
                        </div>
                        <div class="modal-body text-center">
                            <p class="mb-3">Êtes-vous sûr de vouloir supprimer la catégorie :</p>
                            <p class="fw-bold text-primary mb-3">"${categoryName}"</p>
                            <p class="text-muted small">Cette action est irréversible et supprimera également tous les produits associés.</p>
                        </div>
                        <div class="modal-footer border-0 justify-content-center">
                            <button type="button" class="btn btn-secondary btn-modern me-2" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>Annuler
                            </button>
                            <button type="button" class="btn btn-danger btn-modern" onclick="executeDelete(${categoryId})">
                                <i class="fas fa-trash me-2"></i>Supprimer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprimer l'ancienne modal si elle existe
        const existingModal = document.getElementById('deleteConfirmModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Ajouter la nouvelle modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Afficher la modal
        const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        modal.show();

        // Nettoyer après fermeture
        document.getElementById('deleteConfirmModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    };

    // ===== FONCTION D'EXÉCUTION DE LA SUPPRESSION =====

    window.executeDelete = function(categoryId) {
        const form = document.getElementById(`delete-form-${categoryId}`);
        if (form) {
            form.submit();
        }

        // Fermer la modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        if (modal) {
            modal.hide();
        }
    };

    // ===== ANIMATIONS AU CHARGEMENT =====

    // Animation des cartes de statistiques
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Animation des cartes de catégories
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach((card, index) => {
        card.style.animationDelay = `${(index * 0.1) + 0.3}s`;
    });

    // ===== EFFETS VISUELS =====

    // Effet de parallaxe sur l'en-tête
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const headerBackground = document.querySelector('.modern-header-background');
        if (headerBackground) {
            headerBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    // Effet de hover sur les cartes de catégories
    categoryCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});

// Styles CSS pour la modal de confirmation
const style = document.createElement('style');
style.textContent = `
    .modern-modal {
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    }

    .delete-icon-wrapper {
        width: 80px;
        height: 80px;
        margin: 0 auto;
        background: rgba(255, 193, 7, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
    }

    .btn-modern {
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
    }
`;
document.head.appendChild(style);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/categories/index.blade.php ENDPATH**/ ?>