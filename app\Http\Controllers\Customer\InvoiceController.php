<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Invoice;
use App\Models\Payment;

class InvoiceController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:customer']);
    }

    public function index()
    {
        $user = Auth::user();
        
        $invoices = Invoice::where('customer_id', $user->id)
            ->with(['payments'])
            ->latest()
            ->paginate(10);

        return view('customer.invoices.index', compact('invoices'));
    }

    public function show(Invoice $invoice)
    {
        // Vérifier que la facture appartient au client connecté
        if ($invoice->customer_id !== Auth::id()) {
            abort(403, 'Accès non autorisé à cette facture.');
        }

        $invoice->load(['payments', 'order', 'order.product']);
        
        return view('customer.invoices.show', compact('invoice'));
    }

    public function download(Invoice $invoice)
    {
        // Vérifier que la facture appartient au client connecté
        if ($invoice->customer_id !== Auth::id()) {
            abort(403, 'Accès non autorisé à cette facture.');
        }

        // Logique pour générer et télécharger le PDF de la facture
        // À implémenter selon vos besoins
        
        return response()->download(storage_path('app/invoices/' . $invoice->id . '.pdf'));
    }
}
