<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\CustomerFeedback;
use App\Models\CementOrderDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FeedbackController extends Controller
{
    public function index()
    {
        $feedbacks = CustomerFeedback::where('customer_id', Auth::id())
            ->with(['cementOrderDetail.cementOrder.product', 'respondedBy'])
            ->latest()
            ->paginate(10);

        return view('customer.feedback.index', compact('feedbacks'));
    }

    public function create(Request $request)
    {
        $orderId = $request->get('order_id');
        $order = null;

        if ($orderId) {
            $order = CementOrderDetail::where('id', $orderId)
                ->where('customer_id', Auth::id())
                ->with(['cementOrder.product'])
                ->first();
        }

        // Récupérer les commandes récentes du client pour le dropdown
        $recentOrders = CementOrderDetail::where('customer_id', Auth::id())
            ->with(['cementOrder.product'])
            ->latest()
            ->take(10)
            ->get();

        return view('customer.feedback.create', compact('order', 'recentOrders'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:delivery,service,product,general',
            'category' => 'required|in:suggestion,complaint,compliment,question',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|min:10',
            'cement_order_detail_id' => 'nullable|exists:cement_order_details,id',
            'delivery_rating' => 'nullable|integer|min:1|max:5',
            'service_rating' => 'nullable|integer|min:1|max:5',
            'product_rating' => 'nullable|integer|min:1|max:5',
            'overall_rating' => 'nullable|integer|min:1|max:5',
            'driver_name' => 'nullable|string|max:255',
            'truck_number' => 'nullable|string|max:50',
            'delivery_date' => 'nullable|date',
            'delivery_aspects' => 'nullable|array',
            'is_anonymous' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Vérifier que la commande appartient au client connecté
        if ($request->cement_order_detail_id) {
            $orderExists = CementOrderDetail::where('id', $request->cement_order_detail_id)
                ->where('customer_id', Auth::id())
                ->exists();

            if (!$orderExists) {
                return redirect()->back()
                    ->withErrors(['cement_order_detail_id' => 'Commande invalide'])
                    ->withInput();
            }
        }

        $feedback = CustomerFeedback::create([
            'customer_id' => Auth::id(),
            'cement_order_detail_id' => $request->cement_order_detail_id,
            'type' => $request->type,
            'category' => $request->category,
            'subject' => $request->subject,
            'message' => $request->message,
            'delivery_rating' => $request->delivery_rating,
            'service_rating' => $request->service_rating,
            'product_rating' => $request->product_rating,
            'overall_rating' => $request->overall_rating,
            'delivery_aspects' => $request->delivery_aspects,
            'driver_name' => $request->driver_name,
            'truck_number' => $request->truck_number,
            'delivery_date' => $request->delivery_date,
            'is_anonymous' => $request->boolean('is_anonymous'),
            'priority' => $this->determinePriority($request->category, $request->type)
        ]);

        return redirect()->route('customer.feedback.index')
            ->with('success', 'Votre feedback a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.');
    }

    public function show(CustomerFeedback $feedback)
    {
        // Vérifier que le feedback appartient au client connecté
        if ($feedback->customer_id !== Auth::id()) {
            abort(403);
        }

        $feedback->load(['cementOrderDetail.cementOrder.product', 'respondedBy']);

        return view('customer.feedback.show', compact('feedback'));
    }

    public function quickRating(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|exists:cement_order_details,id',
            'rating' => 'required|integer|min:1|max:5'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        // Vérifier que la commande appartient au client connecté
        $orderExists = CementOrderDetail::where('id', $request->order_id)
            ->where('customer_id', Auth::id())
            ->exists();

        if (!$orderExists) {
            return response()->json(['success' => false, 'message' => 'Commande invalide'], 403);
        }

        // Vérifier s'il existe déjà un feedback pour cette commande
        $existingFeedback = CustomerFeedback::where('customer_id', Auth::id())
            ->where('cement_order_detail_id', $request->order_id)
            ->first();

        if ($existingFeedback) {
            // Mettre à jour le rating existant
            $existingFeedback->update([
                'overall_rating' => $request->rating,
                'delivery_rating' => $request->rating
            ]);
        } else {
            // Créer un nouveau feedback rapide
            CustomerFeedback::create([
                'customer_id' => Auth::id(),
                'cement_order_detail_id' => $request->order_id,
                'type' => 'delivery',
                'category' => 'compliment',
                'subject' => 'Évaluation rapide de la livraison',
                'message' => "Évaluation rapide donnée via le dashboard : {$request->rating} étoile(s)",
                'overall_rating' => $request->rating,
                'delivery_rating' => $request->rating,
                'priority' => 'low'
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => "Merci pour votre évaluation de {$request->rating} étoile(s) !"
        ]);
    }

    private function determinePriority($category, $type)
    {
        if ($category === 'complaint' && $type === 'delivery') {
            return 'high';
        }

        if ($category === 'complaint') {
            return 'normal';
        }

        if ($category === 'suggestion') {
            return 'low';
        }

        return 'normal';
    }
}
