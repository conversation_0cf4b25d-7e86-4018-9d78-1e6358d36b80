<div class="receipt-header">
    <div class="receipt-logo-container">
        <img src="{{ asset('assets/images/logo_gradis.png') }}" alt="Logo" class="receipt-logo">
    </div>
    <h1 class="receipt-title">REÇU DE PAIEMENT</h1>
    <p class="receipt-number"># {{ $payment->receipt_number ?? 'REC-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}</p>
    <p class="receipt-date">{{ $payment->payment_date->format('d/m/Y H:i') }}</p>
</div>

<div class="receipt-body">
    <div class="receipt-info-container">
        <div class="receipt-info-column">
            <div class="receipt-section">
                <h5 class="receipt-section-title"><i class="fas fa-user"></i> Informations client</h5>
                <div class="info-row">
                    <div class="info-label">Nom:</div>
                    <div class="info-value">{{ $payment->sale->customer_name }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Téléphone:</div>
                    <div class="info-value">{{ $payment->sale->customer_phone }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Adresse:</div>
                    <div class="info-value">{{ $payment->sale->customer_address }}</div>
                </div>
            </div>

            <div class="receipt-section">
                <h5 class="receipt-section-title"><i class="fas fa-shopping-cart"></i> Informations vente</h5>
                <div class="info-row">
                    <div class="info-label">Référence:</div>
                    <div class="info-value">{{ $payment->sale->invoice_number ?? 'VNT-' . str_pad($payment->sale->id, 6, '0', STR_PAD_LEFT) }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Date de vente:</div>
                    <div class="info-value">{{ $payment->sale->created_at->format('d/m/Y') }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Statut du paiement:</div>
                    <div class="info-value">
                        @if($payment->status === 'completed')
                            <span class="status-badge completed"><i class="fas fa-check-circle"></i> Payé</span>
                        @elseif($payment->status === 'pending')
                            <span class="status-badge pending"><i class="fas fa-clock"></i> En attente</span>
                        @elseif($payment->status === 'cancelled')
                            <span class="status-badge pending"><i class="fas fa-times-circle"></i> Annulé</span>
                        @else
                            <span class="status-badge completed"><i class="fas fa-check-circle"></i> Payé</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="receipt-section">
        <h5 class="receipt-section-title"><i class="fas fa-box"></i> Détails du produit</h5>
        <table class="receipt-table">
            <thead>
                <tr>
                    <th>Produit</th>
                    <th>Quantité</th>
                    <th>Prix unitaire</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        @if($payment->sale->product)
                            {{ $payment->sale->product->name }}
                        @elseif($payment->sale->supply && $payment->sale->supply->details->isNotEmpty())
                            {{ $payment->sale->supply->details->first()->product->name }}
                        @else
                            N/A
                        @endif
                    </td>
                    <td>{{ number_format($payment->sale->quantity, 2, ',', ' ') }} T</td>
                    <td>{{ number_format($payment->sale->unit_price, 0, ',', ' ') }} FCFA</td>
                    <td>{{ number_format($payment->sale->total_amount, 0, ',', ' ') }} FCFA</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="receipt-info-container">
        <div class="receipt-info-column">
            <div class="receipt-section">
                <h5 class="receipt-section-title"><i class="fas fa-credit-card"></i> Détails du paiement</h5>
                <div class="info-row">
                    <div class="info-label">Mode de paiement:</div>
                    <div class="info-value">
                        <span class="payment-method-badge">
                            @switch($payment->payment_method)
                                @case('cash')
                                    <i class="fas fa-money-bill-wave"></i> Espèces
                                    @break
                                @case('bank_transfer')
                                    <i class="fas fa-university"></i> Virement bancaire
                                    @break
                                @case('check')
                                    <i class="fas fa-money-check"></i> Chèque
                                    @break
                                @case('mobile_money')
                                    <i class="fas fa-mobile-alt"></i> Mobile Money
                                    @break
                                @default
                                    <i class="fas fa-money-bill-wave"></i> Espèces
                            @endswitch
                        </span>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-label">Montant payé:</div>
                    <div class="info-value">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Date de paiement:</div>
                    <div class="info-value">{{ $payment->payment_date->format('d/m/Y H:i') }}</div>
                </div>
                @if($payment->notes)
                <div class="info-row">
                    <div class="info-label">Notes:</div>
                    <div class="info-value">{{ $payment->notes }}</div>
                </div>
                @endif
            </div>
        </div>
        <div class="receipt-info-column">
            <div class="payment-summary">
                <div class="payment-summary-title">
                    <i class="fas fa-calculator"></i> Résumé financier
                </div>
                <div class="payment-summary-content">
                    <div class="payment-summary-row">
                        <div>Montant total de la vente:</div>
                        <div class="payment-summary-value">{{ number_format($payment->sale->total_amount, 0, ',', ' ') }} FCFA</div>
                    </div>
                    <div class="payment-summary-row">
                        <div>Montant déjà payé:</div>
                        <div class="payment-summary-value">{{ number_format($payment->sale->amount_paid - $payment->amount, 0, ',', ' ') }} FCFA</div>
                    </div>
                    <div class="payment-summary-row">
                        <div>Ce paiement:</div>
                        <div class="payment-summary-value">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</div>
                    </div>
                    <div class="payment-summary-row">
                        <div>Solde restant:</div>
                        <div class="payment-summary-value">{{ number_format($payment->sale->total_amount - $payment->sale->amount_paid, 0, ',', ' ') }} FCFA</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="receipt-signatures">
        <div class="signature-box">
            <div class="signature-line"></div>
            <p class="signature-title">Signature du caissier</p>
        </div>
        <div class="signature-box stamp">
            <div class="company-stamp">
                <div class="stamp-circle">
                    <div class="stamp-text">GRADIS</div>
                    <div class="stamp-subtext">OFFICIEL</div>
                </div>
            </div>
            <p class="signature-title">Cachet de l'entreprise</p>
        </div>
    </div>

    <div class="receipt-qr">
        <img src="https://api.qrserver.com/v1/create-qr-code/?size=100x100&data={{ urlencode(route('accountant.cashier-receipt', $payment->id)) }}" alt="QR Code">
        <p class="receipt-qr-text">Scannez pour vérifier l'authenticité</p>
    </div>

    <div class="terms-conditions">
        <p class="terms-conditions-title">Termes et Conditions</p>
        <p>Valable avec cachet. Remboursement sous 7 jours. Réclamation avec reçu original uniquement.</p>
    </div>
</div>
