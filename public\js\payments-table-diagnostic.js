/**
 * Script de diagnostic pour le tableau des paiements
 * Vérifie que toutes les colonnes sont visibles et accessibles
 */

function diagnosticPaymentsTable() {
    console.log('=== DIAGNOSTIC DU TABLEAU DES PAIEMENTS ===');
    
    const table = document.querySelector('.payments-table table');
    const container = document.querySelector('.payments-table .table-responsive');
    
    if (!table) {
        console.error('❌ Tableau des paiements non trouvé');
        return;
    }
    
    if (!container) {
        console.error('❌ Conteneur responsive non trouvé');
        return;
    }
    
    // Vérifier les en-têtes de colonnes
    const headers = table.querySelectorAll('thead th');
    const expectedHeaders = [
        'Date', 'Réf. Paiement', 'Montant', 'Méthode', 
        'Réf. Transaction', 'Agent', 'Poste', 'Statut', 
        'Commentaire', 'Actions'
    ];
    
    console.log(`📊 Nombre de colonnes trouvées: ${headers.length}`);
    console.log(`📊 Nombre de colonnes attendues: ${expectedHeaders.length}`);
    
    if (headers.length !== expectedHeaders.length) {
        console.warn('⚠️ Nombre de colonnes incorrect');
    }
    
    // Vérifier chaque en-tête
    headers.forEach((header, index) => {
        const headerText = header.textContent.trim();
        const expected = expectedHeaders[index];
        const isVisible = header.offsetWidth > 0 && header.offsetHeight > 0;
        
        console.log(`${isVisible ? '✅' : '❌'} Colonne ${index + 1}: "${headerText}" ${isVisible ? '(visible)' : '(cachée)'}`);
        
        if (expected && headerText !== expected) {
            console.warn(`⚠️ En-tête inattendu. Attendu: "${expected}", Trouvé: "${headerText}"`);
        }
    });
    
    // Vérifier les dimensions du tableau
    const tableWidth = table.offsetWidth;
    const containerWidth = container.offsetWidth;
    const isScrollable = tableWidth > containerWidth;
    
    console.log(`📏 Largeur du tableau: ${tableWidth}px`);
    console.log(`📏 Largeur du conteneur: ${containerWidth}px`);
    console.log(`📜 Défilement horizontal: ${isScrollable ? 'Activé' : 'Désactivé'}`);
    
    if (!isScrollable && headers.length === expectedHeaders.length) {
        console.warn('⚠️ Le tableau pourrait être trop étroit pour afficher toutes les colonnes correctement');
    }
    
    // Vérifier les styles CSS
    const computedStyle = window.getComputedStyle(table);
    const minWidth = computedStyle.minWidth;
    
    console.log(`🎨 Largeur minimale CSS: ${minWidth}`);
    
    // Vérifier les données
    const rows = table.querySelectorAll('tbody tr');
    console.log(`📋 Nombre de lignes de données: ${rows.length}`);
    
    if (rows.length > 0) {
        const firstRow = rows[0];
        const cells = firstRow.querySelectorAll('td');
        console.log(`📋 Nombre de cellules dans la première ligne: ${cells.length}`);
        
        if (cells.length !== headers.length) {
            console.error('❌ Incohérence entre le nombre d\'en-têtes et de cellules');
        }
    }
    
    // Recommandations
    console.log('\n=== RECOMMANDATIONS ===');
    
    if (isScrollable) {
        console.log('✅ Le défilement horizontal est correctement configuré');
    } else if (headers.length === expectedHeaders.length) {
        console.log('💡 Considérez augmenter la largeur minimale du tableau');
    }
    
    if (window.innerWidth < 768) {
        console.log('📱 Affichage mobile détecté - vérifiez l\'indicateur de défilement');
    }
    
    console.log('=== FIN DU DIAGNOSTIC ===');
}

// Fonction pour tester le défilement
function testScrolling() {
    const container = document.querySelector('.payments-table .table-responsive');
    if (!container) {
        console.error('Conteneur de défilement non trouvé');
        return;
    }
    
    console.log('🧪 Test du défilement horizontal...');
    
    // Défilement vers la droite
    container.scrollTo({ left: 200, behavior: 'smooth' });
    
    setTimeout(() => {
        console.log(`Position de défilement: ${container.scrollLeft}px`);
        
        // Retour au début
        container.scrollTo({ left: 0, behavior: 'smooth' });
        
        setTimeout(() => {
            console.log('✅ Test de défilement terminé');
        }, 1000);
    }, 1000);
}

// Fonction pour afficher les informations de débogage
function showTableInfo() {
    const info = {
        viewport: {
            width: window.innerWidth,
            height: window.innerHeight
        },
        table: {},
        container: {}
    };
    
    const table = document.querySelector('.payments-table table');
    const container = document.querySelector('.payments-table .table-responsive');
    
    if (table) {
        info.table = {
            width: table.offsetWidth,
            height: table.offsetHeight,
            minWidth: window.getComputedStyle(table).minWidth,
            columns: table.querySelectorAll('thead th').length,
            rows: table.querySelectorAll('tbody tr').length
        };
    }
    
    if (container) {
        info.container = {
            width: container.offsetWidth,
            height: container.offsetHeight,
            scrollWidth: container.scrollWidth,
            scrollLeft: container.scrollLeft,
            canScroll: container.scrollWidth > container.offsetWidth
        };
    }
    
    console.table(info);
    return info;
}

// Exporter les fonctions pour utilisation dans la console
window.paymentsTableDiagnostic = {
    run: diagnosticPaymentsTable,
    testScroll: testScrolling,
    showInfo: showTableInfo
};

// Exécuter le diagnostic automatiquement au chargement
document.addEventListener('DOMContentLoaded', function() {
    // Attendre que tout soit chargé
    setTimeout(diagnosticPaymentsTable, 500);
});

// Ajouter des raccourcis clavier pour le débogage (Ctrl+Shift+D)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        e.preventDefault();
        diagnosticPaymentsTable();
    }
});

console.log('🔧 Script de diagnostic du tableau des paiements chargé');
console.log('💡 Utilisez paymentsTableDiagnostic.run() pour lancer le diagnostic');
console.log('💡 Utilisez paymentsTableDiagnostic.testScroll() pour tester le défilement');
console.log('💡 Utilisez paymentsTableDiagnostic.showInfo() pour voir les informations');
console.log('💡 Ou appuyez sur Ctrl+Shift+D pour lancer le diagnostic');
