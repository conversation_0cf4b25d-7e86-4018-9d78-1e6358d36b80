/**
 * Script de diagnostic pour les problèmes de paiement
 * Aide à identifier et résoudre les problèmes JavaScript liés aux paiements
 */

// Fonction de diagnostic principal
function runPaymentDiagnostic() {
    console.log('🔍 Diagnostic des paiements - Début');
    
    const results = {
        paymentCards: checkPaymentCards(),
        referenceField: checkReferenceField(),
        javaScriptErrors: checkJavaScriptErrors(),
        permissionsPolicy: checkPermissionsPolicy(),
        eventListeners: checkEventListeners()
    };
    
    console.log('📊 Résultats du diagnostic:', results);
    
    // Afficher un résumé
    displayDiagnosticSummary(results);
    
    return results;
}

// Vérifier les cartes de paiement
function checkPaymentCards() {
    const cards = document.querySelectorAll('.payment-card');
    const radios = document.querySelectorAll('.payment-card input[type="radio"]');
    
    return {
        cardsFound: cards.length,
        radiosFound: radios.length,
        selectedCard: document.querySelector('.payment-card.selected') ? true : false,
        checkedRadio: document.querySelector('.payment-card input[type="radio"]:checked') ? true : false
    };
}

// Vérifier le champ de référence
function checkReferenceField() {
    const referenceField = document.querySelector('.reference-field');
    const referenceInput = document.getElementById('reference_number');
    
    return {
        fieldExists: referenceField ? true : false,
        inputExists: referenceInput ? true : false,
        isVisible: referenceField ? referenceField.style.display !== 'none' : false,
        isRequired: referenceInput ? referenceInput.hasAttribute('required') : false
    };
}

// Vérifier les erreurs JavaScript
function checkJavaScriptErrors() {
    const errors = [];
    
    // Intercepter les erreurs
    const originalError = console.error;
    console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
    };
    
    // Vérifier les fonctions critiques
    const criticalFunctions = [
        'updatePaymentMethodInfo',
        'toggleReferenceField'
    ];
    
    const missingFunctions = criticalFunctions.filter(func => typeof window[func] === 'undefined');
    
    return {
        recentErrors: errors,
        missingFunctions: missingFunctions
    };
}

// Vérifier la politique de permissions
function checkPermissionsPolicy() {
    const permissionsPolicy = document.querySelector('meta[http-equiv="Permissions-Policy"]');
    
    return {
        policyExists: permissionsPolicy ? true : false,
        policyContent: permissionsPolicy ? permissionsPolicy.getAttribute('content') : null,
        unloadAllowed: permissionsPolicy ? 
            permissionsPolicy.getAttribute('content').includes('unload=*') : false
    };
}

// Vérifier les gestionnaires d'événements
function checkEventListeners() {
    const paymentCards = document.querySelectorAll('.payment-card');
    let listenersAttached = 0;
    
    paymentCards.forEach(card => {
        // Vérifier si des événements sont attachés (méthode approximative)
        if (card.onclick || card.addEventListener) {
            listenersAttached++;
        }
    });
    
    return {
        cardsWithListeners: listenersAttached,
        totalCards: paymentCards.length
    };
}

// Afficher un résumé du diagnostic
function displayDiagnosticSummary(results) {
    console.log('\n📋 RÉSUMÉ DU DIAGNOSTIC');
    console.log('========================');
    
    // Cartes de paiement
    if (results.paymentCards.cardsFound === 0) {
        console.log('❌ Aucune carte de paiement trouvée');
    } else {
        console.log(`✅ ${results.paymentCards.cardsFound} cartes de paiement trouvées`);
    }
    
    // Champ de référence
    if (!results.referenceField.fieldExists) {
        console.log('❌ Champ de référence manquant');
    } else {
        console.log('✅ Champ de référence présent');
    }
    
    // Erreurs JavaScript
    if (results.javaScriptErrors.missingFunctions.length > 0) {
        console.log(`❌ Fonctions manquantes: ${results.javaScriptErrors.missingFunctions.join(', ')}`);
    } else {
        console.log('✅ Toutes les fonctions critiques sont présentes');
    }
    
    // Politique de permissions
    if (!results.permissionsPolicy.unloadAllowed) {
        console.log('⚠️ L\'événement unload n\'est pas autorisé (peut causer des erreurs d\'extensions)');
    } else {
        console.log('✅ Politique de permissions configurée correctement');
    }
    
    console.log('========================\n');
}

// Fonction de réparation automatique
function autoFixPaymentIssues() {
    console.log('🔧 Tentative de réparation automatique...');
    
    // Réattacher les gestionnaires d'événements si nécessaire
    const paymentCards = document.querySelectorAll('.payment-card');
    
    paymentCards.forEach(card => {
        // Supprimer les anciens gestionnaires
        card.replaceWith(card.cloneNode(true));
    });
    
    // Réattacher les nouveaux gestionnaires
    document.querySelectorAll('.payment-card').forEach(card => {
        card.addEventListener('click', function() {
            try {
                // Logique de sélection de carte
                document.querySelectorAll('.payment-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }
                
                const method = this.dataset.method;
                if (method) {
                    // Gérer le champ de référence
                    const referenceField = document.querySelector('.reference-field');
                    const referenceInput = document.getElementById('reference_number');
                    
                    if (method === 'cash') {
                        if (referenceField) referenceField.style.display = 'none';
                        if (referenceInput) {
                            referenceInput.removeAttribute('required');
                            referenceInput.value = '';
                        }
                    } else {
                        if (referenceField) referenceField.style.display = 'block';
                        if (referenceInput) referenceInput.setAttribute('required', 'required');
                    }
                }
                
                console.log(`✅ Mode de paiement sélectionné: ${method}`);
            } catch (error) {
                console.error('❌ Erreur lors de la sélection:', error);
            }
        });
    });
    
    console.log('✅ Réparation terminée');
}

// Exporter les fonctions pour utilisation dans la console
window.runPaymentDiagnostic = runPaymentDiagnostic;
window.autoFixPaymentIssues = autoFixPaymentIssues;

// Exécuter le diagnostic automatiquement si on est sur une page de paiement
if (document.querySelector('.payment-card')) {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(runPaymentDiagnostic, 1000);
    });
}

console.log('🛠️ Script de diagnostic des paiements chargé. Utilisez runPaymentDiagnostic() ou autoFixPaymentIssues() dans la console.');
