<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Supply;
use App\Models\City;
use App\Models\User;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Payment;
use App\Models\PaymentSchedule;
use App\Models\Truck;
use App\Models\Driver;

class Sale extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'supply_id',
        'city_id',
        'customer_id',
        'customer_name',
        'customer_phone',
        'customer_email',
        'customer_address',
        'quantity',
        'unit_price',
        'total_before_discount',
        'discount_per_ton',
        'discount_total',
        'total_amount',
        'amount_paid',
        'payment_method',
        'payment_status',
        'trips',
        'delivery_status',
        'delivery_date',
        'status',
        'created_by',
        'invoice_number',
        'qr_code',
        'notes',
        'admin_note',            // raison du rejet par l'administrateur
        'admin_validation_status', // nouveau champ pour le workflow admin
        'price_modified',         // indique si le prix a été modifié
        'original_price',         // prix original avant modification
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'trips' => 'integer',
        'delivery_date' => 'datetime'
    ];

    public function supply()
    {
        return $this->belongsTo(Supply::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function product()
    {
        // Relation Eloquent classique pour compatibilité avec Blade
        return $this->belongsTo(Product::class, 'product_id')->withTrashed();
    }
    
    /**
     * Accesseur pour récupérer le produit même si product_id est null
     * en utilisant l'approvisionnement associé
     */
    public function getProductAttribute()
    {
        // Si la relation product est déjà chargée et existe, on la retourne
        if ($this->relationLoaded('product') && $this->getRelation('product')) {
            return $this->getRelation('product');
        }
        
        // Sinon, on essaie de récupérer le produit via l'approvisionnement
        if ($this->supply && $this->supply->details && $this->supply->details->isNotEmpty()) {
            $supplyDetail = $this->supply->details->first();
            if ($supplyDetail && $supplyDetail->product) {
                return $supplyDetail->product;
            }
        }
        
        // Si on ne trouve pas de produit, on retourne null
        return null;
    }

    public function getRemainingAmountAttribute()
    {
        return $this->total_amount - $this->amount_paid;
    }

    public function getIsFullyPaidAttribute()
    {
        return $this->payment_status === 'completed';
    }

    public function getDeliveryProgressAttribute()
    {
        switch ($this->delivery_status) {
            case 'pending':
                return 0;
            case 'in_progress':
                return 50;
            case 'completed':
                return 100;
            default:
                return 0;
        }
    }

    /**
     * Relation avec le véhicule (camion) utilisé pour cette vente
     * Note: Cette relation nécessite un champ vehicle_id dans la table sales
     */
    // public function vehicle()
    // {
    //     return $this->belongsTo(Truck::class, 'vehicle_id');
    // }

    /**
     * Relation avec le chauffeur assigné à cette vente
     */
    public function driver()
    {
        return $this->belongsTo(Driver::class, 'driver_id');
    }
    
    public function getPaymentProgressAttribute()
    {
        if ($this->total_amount <= 0) return 0;
        return ($this->amount_paid / $this->total_amount) * 100;
    }
    
    /**
     * Relation avec les paiements effectués pour cette vente
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }
    
    /**
     * Relation avec le client de cette vente
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }
    
    /**
     * Relation avec les éléments/lignes de cette vente
     */
    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }
    
    /**
     * Relation avec les échéanciers de paiement pour cette vente
     * Note: Cette relation est configurée pour retourner une relation vide car la table n'existe pas encore
     */
    public function paymentSchedules()
    {
        // Utiliser une relation morphMany avec une condition impossible pour s'assurer qu'aucun résultat n'est retourné
        // tout en maintenant une instance de relation valide
        return $this->hasMany(PaymentSchedule::class)->whereRaw('1 = 0');
    }
    
    /**
     * Vérifie si la vente est éligible pour le règlement par le caissier
     */
    public function getIsEligibleForCashierAttribute()
    {
        // Ventes sans remise ni augmentation de prix sont automatiquement éligibles
        if ($this->discount_per_ton == 0 && !$this->price_modified) {
            return true;
        }
        
        // Ventes avec remise ou augmentation de prix doivent être validées par l'admin
        return $this->admin_validation_status === 'approved';
    }
    
    /**
     * Vérifie si la vente a été annulée par l'administrateur
     *
     * @return bool
     */
    public function isCancelledByAdmin(): bool
    {
        return $this->status === 'cancelled'
            && $this->payment_status === 'cancelled'
            && !empty($this->admin_note)
            && str_contains($this->admin_note, 'Vente annulée par l\'administrateur');
    }

    /**
     * Scope pour récupérer uniquement les ventes annulées par l'admin
     */
    public function scopeCancelledByAdmin($query)
    {
        return $query->where('status', 'cancelled')
                    ->where('payment_status', 'cancelled')
                    ->whereNotNull('admin_note')
                    ->where('admin_note', 'like', '%Vente annulée par l\'administrateur%');
    }

    /**
     * Scope pour récupérer les ventes actives (non annulées par l'admin)
     */
    public function scopeActiveOrNormalCancelled($query)
    {
        return $query->where(function($q) {
            $q->where('status', '!=', 'cancelled')
              ->orWhere(function($subQ) {
                  // Inclure les ventes avec status cancelled mais qui ne sont PAS annulées par l'admin
                  $subQ->where('status', 'cancelled')
                       ->where(function($adminQ) {
                           $adminQ->whereNull('admin_note')
                                  ->orWhere('admin_note', 'not like', '%Vente annulée par l\'administrateur%');
                       });
              });
        });
    }

    /**
     * Calcule le montant total payé à partir des paiements enregistrés
     */
    public function updateAmountPaid()
    {
        // Calculer le montant total payé sans filtrer par status (car la colonne n'existe pas)
        $this->amount_paid = $this->payments()->sum('amount');

        // Mise à jour du statut de paiement
        if ($this->amount_paid >= $this->total_amount) {
            $this->payment_status = 'completed';
            $this->status = 'completed'; // Utiliser 'completed' au lieu de 'paid'
        } elseif ($this->amount_paid > 0) {
            $this->payment_status = 'partial';
            $this->status = 'pending'; // Utiliser 'pending' au lieu de 'partially_paid'
        } else {
            $this->payment_status = 'pending';
            $this->status = 'pending'; // Utiliser 'pending' au lieu de 'pending_payment'
        }

        $this->save();

        return $this->amount_paid;
    }
}
