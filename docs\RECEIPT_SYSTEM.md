# 📄 Système de Reçus de Vente - CIMTOGO

## 🎯 Vue d'ensemble

Le système de reçus de vente permet de générer et d'imprimer des reçus professionnels au format A5 pour toutes les ventes de ciment. Ce système offre une interface moderne et des options d'impression optimisées.

## ✨ Fonctionnalités

### 🖥️ **Interface Web**
- **Reçu interactif** : Visualisation complète du reçu avec tous les détails
- **Design responsive** : Optimisé pour tous les écrans
- **Impression directe** : Bouton d'impression intégré
- **Format A5** : Taille optimale pour les reçus de vente

### 📱 **Génération PDF**
- **Format A5 portrait** : Dimensions parfaites pour l'impression
- **Qualité professionnelle** : Mise en page soignée et lisible
- **Téléchargement direct** : PDF généré instantanément
- **Optimisation d'impression** : Marges et polices adaptées

## 🚀 Accès aux Reçus

### **Depuis la Liste des Ventes**
1. Aller sur `/cement-manager/sales`
2. Cliquer sur l'icône **📄 Reçu** (bouton vert) pour voir le reçu
3. Cliquer sur l'icône **📄 PDF** (bouton rouge) pour télécharger

### **Depuis les Détails d'une Vente**
1. Aller sur `/cement-manager/sales/{id}`
2. Utiliser les boutons "Voir le Reçu" ou "PDF" en haut à droite

### **URLs Directes**
- **Reçu Web** : `/cement-manager/sales/{id}/receipt`
- **Reçu PDF** : `/cement-manager/sales/{id}/receipt-pdf`

## 📋 Contenu du Reçu

### **En-tête**
- Logo CIMTOGO
- Numéro de reçu (format : #000001)
- Date et heure de création

### **Détails du Produit**
- Nom du produit (ex: CIMTOGO-CPJ 45-INTERIEUR)
- Référence du bon de commande
- Quantité en tonnes
- Prix unitaire
- Remises appliquées (si applicable)

### **Informations Client**
- Nom complet
- Numéro de téléphone
- Adresse complète
- Ville de livraison

### **Détails du Paiement**
- Mode de paiement (Comptant/Crédit)
- Statut de la vente
- Nombre de voyages (si applicable)
- Montant total avec calculs détaillés

### **Éléments Additionnels**
- Code QR (si disponible)
- Espaces pour signatures (Client/Vendeur)
- Informations de contact CIMTOGO
- Horodatage de génération

## 🎨 Caractéristiques du Design

### **Format A5**
- **Dimensions** : 148mm × 210mm
- **Orientation** : Portrait
- **Marges** : 10mm sur tous les côtés

### **Couleurs et Style**
- **Palette** : Bleu (#007bff), Vert (#4caf50), Orange (#ff9800)
- **Typographie** : Arial/DejaVu Sans pour compatibilité PDF
- **Sections colorées** : Chaque section a sa propre couleur d'identification

### **Éléments Visuels**
- **Badges de statut** : Colorés selon l'état (Terminé, En attente, etc.)
- **Icônes** : FontAwesome pour une meilleure lisibilité
- **Bordures** : Arrondies pour un aspect moderne
- **Ombres** : Légères pour la profondeur

## 🖨️ Instructions d'Impression

### **Paramètres Recommandés**
- **Format** : A5 (148 × 210 mm)
- **Orientation** : Portrait
- **Marges** : Minimales (5-10mm)
- **Qualité** : Normale à élevée
- **Couleur** : Recommandée pour les badges de statut

### **Papier Suggéré**
- **Grammage** : 80-120g/m²
- **Type** : Papier blanc standard ou légèrement coloré
- **Finition** : Mat ou légèrement brillant

## 🔧 Configuration Technique

### **Dépendances**
- **Laravel Framework** : ^10.0
- **DomPDF** : ^3.1 (barryvdh/laravel-dompdf)
- **Bootstrap** : 5.x
- **FontAwesome** : 6.x

### **Fichiers Impliqués**
```
app/Http/Controllers/CementManager/SaleController.php
├── receipt()         # Affichage web du reçu
└── receiptPdf()      # Génération PDF

resources/views/cement-manager/sales/
├── receipt.blade.php     # Template web
└── receipt-pdf.blade.php # Template PDF

routes/web.php
├── sales/{sale}/receipt     # Route web
└── sales/{sale}/receipt-pdf # Route PDF
```

## 🎯 Cas d'Usage

### **Pour les Vendeurs**
- Fournir un reçu immédiat au client
- Garder une trace papier des ventes
- Faciliter les réclamations et le SAV

### **Pour les Clients**
- Preuve d'achat officielle
- Référence pour les livraisons
- Document pour la comptabilité

### **Pour l'Administration**
- Audit des ventes
- Contrôle qualité des transactions
- Archivage des documents

## 🔄 Workflow Typique

1. **Création de la vente** dans le système
2. **Validation** par l'administrateur (si nécessaire)
3. **Génération du reçu** automatique
4. **Impression** pour remise au client
5. **Archivage** numérique du PDF

## 📞 Support

Pour toute question ou problème avec le système de reçus :
- **Email** : <EMAIL>
- **Téléphone** : +228 XX XX XX XX
- **Documentation** : Consultez ce guide

---

**Version** : 1.0  
**Dernière mise à jour** : {{ now()->format('d/m/Y') }}  
**Développé par** : Équipe CIMTOGO
