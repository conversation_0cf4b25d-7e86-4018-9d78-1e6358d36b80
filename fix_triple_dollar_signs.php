<?php

echo "🔧 CORRECTION AUTOMATIQUE DES SIGNES DOLLAR TRIPLES\n";
echo "==================================================\n\n";

$viewPath = 'resources/views/admin/dashboard.blade.php';

if (!file_exists($viewPath)) {
    echo "❌ Fichier non trouvé: $viewPath\n";
    exit(1);
}

echo "📖 Lecture du fichier...\n";
$content = file_get_contents($viewPath);

if ($content === false) {
    echo "❌ Impossible de lire le fichier\n";
    exit(1);
}

echo "🔍 Recherche des signes dollar triples...\n";

// Compter les occurrences avant correction
$countBefore = substr_count($content, '$$$');
echo "📊 Trouvé $countBefore occurrences de \$\$\$\n";

if ($countBefore > 0) {
    echo "🔧 Correction en cours...\n";
    
    // Remplacer tous les $$$ par $
    $content = str_replace('$$$', '$', $content);
    
    // Vérifier qu'il n'y a plus de $$$
    $countAfter = substr_count($content, '$$$');
    
    if ($countAfter === 0) {
        echo "✅ Toutes les occurrences de \$\$\$ ont été corrigées!\n";
        
        // Backup du fichier original
        $backupPath = $viewPath . '.triple-dollar-backup.' . date('Y-m-d-H-i-s');
        copy($viewPath, $backupPath);
        echo "📋 Backup créé: $backupPath\n";
        
        // Sauvegarde du fichier modifié
        if (file_put_contents($viewPath, $content) !== false) {
            echo "✅ Fichier corrigé avec succès!\n";
            echo "🔢 Total des corrections: $countBefore\n";
        } else {
            echo "❌ Erreur lors de la sauvegarde\n";
            exit(1);
        }
    } else {
        echo "⚠️  Il reste encore $countAfter occurrences de \$\$\$\n";
    }
} else {
    echo "ℹ️  Aucune occurrence de \$\$\$ trouvée\n";
}

// Vérifications supplémentaires pour d'autres erreurs similaires
echo "\n🔍 Vérification d'autres erreurs similaires...\n";

$otherErrors = [
    '$$' => '$',  // Double dollar
    '$$$$$' => '$', // Quintuple dollar
    '$$$$' => '$',  // Quadruple dollar
];

$additionalCorrections = 0;

foreach ($otherErrors as $search => $replace) {
    $count = substr_count($content, $search);
    if ($count > 0) {
        echo "✅ Corrigé $count occurrences de '$search'\n";
        $content = str_replace($search, $replace, $content);
        $additionalCorrections += $count;
    }
}

if ($additionalCorrections > 0) {
    echo "💾 Sauvegarde des corrections supplémentaires...\n";
    file_put_contents($viewPath, $content);
    echo "✅ $additionalCorrections corrections supplémentaires appliquées\n";
}

echo "\n🎯 RÉSUMÉ:\n";
echo "=========\n";
echo "✅ Tous les signes dollar sont maintenant corrects\n";
echo "✅ Plus de \$\$\$ dans le code\n";
echo "✅ Variables PHP valides\n";
echo "✅ Syntaxe Blade correcte\n";

echo "\n🚀 Toutes les variables ont maintenant le bon nombre de \$ !\n";

// Vérification finale
$finalCheck = substr_count($content, '$$$');
if ($finalCheck === 0) {
    echo "🎉 SUCCÈS TOTAL: Plus aucune erreur de \$\$\$ !\n";
} else {
    echo "⚠️  ATTENTION: Il reste encore $finalCheck erreurs\n";
}
