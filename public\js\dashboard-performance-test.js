/**
 * Script de test de performance pour le tableau de bord comptable
 * Permet de diagnostiquer et résoudre les problèmes de lenteur
 */

class DashboardPerformanceTester {
    constructor() {
        this.testResults = {};
        this.recommendations = [];
        this.init();
    }

    init() {
        console.log('🧪 Dashboard Performance Tester initialisé');
        this.addTestInterface();
    }

    addTestInterface() {
        // Créer l'interface de test
        const testInterface = document.createElement('div');
        testInterface.id = 'performance-test-interface';
        testInterface.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            z-index: 10000;
            min-width: 300px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            display: none;
        `;

        testInterface.innerHTML = `
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 15px;">
                <h4 style="margin: 0; font-size: 16px;">🧪 Test de Performance</h4>
                <button onclick="this.parentElement.parentElement.style.display='none'" 
                        style="background: none; border: none; color: white; font-size: 18px; cursor: pointer;">×</button>
            </div>
            
            <div id="test-controls" style="margin-bottom: 15px;">
                <button onclick="window.performanceTester.runFullTest()" 
                        style="background: #4CAF50; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    🚀 Test Complet
                </button>
                <button onclick="window.performanceTester.runQuickTest()" 
                        style="background: #2196F3; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">
                    ⚡ Test Rapide
                </button>
            </div>
            
            <div id="test-results" style="background: rgba(0,0,0,0.2); padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;">
                <div style="color: #ccc;">Cliquez sur un bouton pour commencer les tests...</div>
            </div>
            
            <div id="test-recommendations" style="margin-top: 10px; font-size: 12px; color: #e0e0e0;">
                <!-- Les recommandations apparaîtront ici -->
            </div>
        `;

        document.body.appendChild(testInterface);

        // Bouton pour afficher/masquer l'interface
        const toggleButton = document.createElement('button');
        toggleButton.innerHTML = '🧪';
        toggleButton.title = 'Ouvrir les tests de performance';
        toggleButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            z-index: 10001;
            font-size: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        `;

        toggleButton.addEventListener('click', () => {
            const isVisible = testInterface.style.display !== 'none';
            testInterface.style.display = isVisible ? 'none' : 'block';
            toggleButton.style.left = isVisible ? '20px' : '340px';
        });

        toggleButton.addEventListener('mouseenter', () => {
            toggleButton.style.transform = 'scale(1.1)';
        });

        toggleButton.addEventListener('mouseleave', () => {
            toggleButton.style.transform = 'scale(1)';
        });

        document.body.appendChild(toggleButton);
    }

    async runQuickTest() {
        this.updateResults('🚀 Démarrage du test rapide...');
        
        const tests = [
            this.testPageLoadTime.bind(this),
            this.testDOMElements.bind(this),
            this.testNetworkRequests.bind(this),
            this.testCacheStatus.bind(this)
        ];

        for (const test of tests) {
            await test();
            await this.delay(500);
        }

        this.generateRecommendations();
        this.updateResults('✅ Test rapide terminé !');
    }

    async runFullTest() {
        this.updateResults('🔬 Démarrage du test complet...');
        
        const tests = [
            this.testPageLoadTime.bind(this),
            this.testDOMElements.bind(this),
            this.testNetworkRequests.bind(this),
            this.testCacheStatus.bind(this),
            this.testDatabaseQueries.bind(this),
            this.testMemoryUsage.bind(this),
            this.testRenderingPerformance.bind(this),
            this.testJavaScriptExecution.bind(this)
        ];

        for (const test of tests) {
            await test();
            await this.delay(800);
        }

        this.generateRecommendations();
        this.updateResults('🎉 Test complet terminé !');
    }

    async testPageLoadTime() {
        this.updateResults('⏱️ Test du temps de chargement...');
        
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            const loadTime = navigation.loadEventEnd - navigation.navigationStart;
            const domTime = navigation.domContentLoadedEventEnd - navigation.navigationStart;
            
            this.testResults.pageLoad = {
                totalTime: loadTime,
                domTime: domTime,
                status: loadTime < 3000 ? 'good' : loadTime < 5000 ? 'warning' : 'critical'
            };
            
            this.updateResults(`📊 Temps de chargement: ${loadTime.toFixed(0)}ms (DOM: ${domTime.toFixed(0)}ms)`);
        }
    }

    async testDOMElements() {
        this.updateResults('🏗️ Test des éléments DOM...');
        
        const elementCount = document.querySelectorAll('*').length;
        const imageCount = document.querySelectorAll('img').length;
        const scriptCount = document.querySelectorAll('script').length;
        
        this.testResults.dom = {
            elementCount,
            imageCount,
            scriptCount,
            status: elementCount < 1000 ? 'good' : elementCount < 2000 ? 'warning' : 'critical'
        };
        
        this.updateResults(`🏗️ Éléments DOM: ${elementCount} (Images: ${imageCount}, Scripts: ${scriptCount})`);
    }

    async testNetworkRequests() {
        this.updateResults('🌐 Test des requêtes réseau...');
        
        const resources = performance.getEntriesByType('resource');
        const slowRequests = resources.filter(r => r.duration > 1000);
        const totalSize = resources.reduce((sum, r) => sum + (r.transferSize || 0), 0);
        
        this.testResults.network = {
            totalRequests: resources.length,
            slowRequests: slowRequests.length,
            totalSize: totalSize,
            status: slowRequests.length === 0 ? 'good' : slowRequests.length < 3 ? 'warning' : 'critical'
        };
        
        this.updateResults(`🌐 Requêtes: ${resources.length} (Lentes: ${slowRequests.length}, Taille: ${(totalSize/1024).toFixed(0)}KB)`);
    }

    async testCacheStatus() {
        this.updateResults('💾 Test du statut du cache...');
        
        try {
            const startTime = performance.now();
            const response = await fetch(window.location.href, { method: 'HEAD' });
            const endTime = performance.now();
            
            const responseTime = endTime - startTime;
            const cacheHeaders = response.headers.get('cache-control');
            
            this.testResults.cache = {
                responseTime,
                hasCacheHeaders: !!cacheHeaders,
                status: responseTime < 200 ? 'good' : responseTime < 500 ? 'warning' : 'critical'
            };
            
            this.updateResults(`💾 Cache: ${responseTime.toFixed(0)}ms (Headers: ${cacheHeaders ? 'Oui' : 'Non'})`);
        } catch (error) {
            this.updateResults('💾 Cache: Erreur lors du test');
        }
    }

    async testDatabaseQueries() {
        this.updateResults('🗄️ Test des requêtes base de données...');
        
        // Simuler un test de requête
        try {
            const startTime = performance.now();
            await fetch('/accountant/dashboard-professional', { method: 'HEAD' });
            const endTime = performance.now();
            
            const queryTime = endTime - startTime;
            
            this.testResults.database = {
                queryTime,
                status: queryTime < 500 ? 'good' : queryTime < 1000 ? 'warning' : 'critical'
            };
            
            this.updateResults(`🗄️ Base de données: ${queryTime.toFixed(0)}ms`);
        } catch (error) {
            this.updateResults('🗄️ Base de données: Erreur lors du test');
        }
    }

    async testMemoryUsage() {
        this.updateResults('🧠 Test de l\'utilisation mémoire...');
        
        if (performance.memory) {
            const memory = performance.memory;
            const usedMB = memory.usedJSHeapSize / 1024 / 1024;
            const totalMB = memory.totalJSHeapSize / 1024 / 1024;
            
            this.testResults.memory = {
                used: usedMB,
                total: totalMB,
                status: usedMB < 50 ? 'good' : usedMB < 100 ? 'warning' : 'critical'
            };
            
            this.updateResults(`🧠 Mémoire: ${usedMB.toFixed(1)}MB / ${totalMB.toFixed(1)}MB`);
        } else {
            this.updateResults('🧠 Mémoire: Non disponible dans ce navigateur');
        }
    }

    async testRenderingPerformance() {
        this.updateResults('🎨 Test des performances de rendu...');
        
        const startTime = performance.now();
        
        // Forcer un reflow
        document.body.offsetHeight;
        
        const endTime = performance.now();
        const renderTime = endTime - startTime;
        
        this.testResults.rendering = {
            renderTime,
            status: renderTime < 10 ? 'good' : renderTime < 50 ? 'warning' : 'critical'
        };
        
        this.updateResults(`🎨 Rendu: ${renderTime.toFixed(2)}ms`);
    }

    async testJavaScriptExecution() {
        this.updateResults('⚡ Test d\'exécution JavaScript...');
        
        const startTime = performance.now();
        
        // Test de calcul intensif
        let result = 0;
        for (let i = 0; i < 100000; i++) {
            result += Math.random();
        }
        
        const endTime = performance.now();
        const executionTime = endTime - startTime;
        
        this.testResults.javascript = {
            executionTime,
            status: executionTime < 10 ? 'good' : executionTime < 50 ? 'warning' : 'critical'
        };
        
        this.updateResults(`⚡ JavaScript: ${executionTime.toFixed(2)}ms`);
    }

    generateRecommendations() {
        this.recommendations = [];
        
        // Analyser les résultats et générer des recommandations
        if (this.testResults.pageLoad && this.testResults.pageLoad.status !== 'good') {
            this.recommendations.push('🐌 Optimiser le temps de chargement (cache, compression)');
        }
        
        if (this.testResults.dom && this.testResults.dom.status !== 'good') {
            this.recommendations.push('🏗️ Réduire le nombre d\'éléments DOM');
        }
        
        if (this.testResults.network && this.testResults.network.status !== 'good') {
            this.recommendations.push('🌐 Optimiser les requêtes réseau (lazy loading, CDN)');
        }
        
        if (this.testResults.cache && this.testResults.cache.status !== 'good') {
            this.recommendations.push('💾 Améliorer la stratégie de cache');
        }
        
        if (this.testResults.database && this.testResults.database.status !== 'good') {
            this.recommendations.push('🗄️ Optimiser les requêtes base de données');
        }
        
        if (this.testResults.memory && this.testResults.memory.status !== 'good') {
            this.recommendations.push('🧠 Optimiser l\'utilisation mémoire');
        }
        
        // Afficher les recommandations
        const recommendationsDiv = document.getElementById('test-recommendations');
        if (recommendationsDiv) {
            if (this.recommendations.length > 0) {
                recommendationsDiv.innerHTML = `
                    <strong>📋 Recommandations:</strong><br>
                    ${this.recommendations.map(rec => `• ${rec}`).join('<br>')}
                `;
            } else {
                recommendationsDiv.innerHTML = '✅ Aucune optimisation nécessaire !';
            }
        }
    }

    updateResults(message) {
        const resultsDiv = document.getElementById('test-results');
        if (resultsDiv) {
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div style="margin: 2px 0;">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        console.log(`🧪 ${message}`);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Méthodes utilitaires publiques
    exportResults() {
        const results = {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            testResults: this.testResults,
            recommendations: this.recommendations
        };
        
        const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `dashboard-performance-test-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        this.updateResults('📁 Résultats exportés');
    }

    clearResults() {
        this.testResults = {};
        this.recommendations = [];
        const resultsDiv = document.getElementById('test-results');
        if (resultsDiv) {
            resultsDiv.innerHTML = '<div style="color: #ccc;">Résultats effacés. Cliquez sur un bouton pour commencer les tests...</div>';
        }
        const recommendationsDiv = document.getElementById('test-recommendations');
        if (recommendationsDiv) {
            recommendationsDiv.innerHTML = '';
        }
    }
}

// Initialiser le testeur de performance
window.performanceTester = new DashboardPerformanceTester();

// Fonctions utilitaires globales
window.runPerformanceTest = () => window.performanceTester.runQuickTest();
window.runFullPerformanceTest = () => window.performanceTester.runFullTest();
window.exportPerformanceResults = () => window.performanceTester.exportResults();

console.log('🧪 Dashboard Performance Tester chargé');
console.log('💡 Utilisez runPerformanceTest() pour un test rapide');
console.log('💡 Utilisez runFullPerformanceTest() pour un test complet');
console.log('💡 Cliquez sur 🧪 en bas à gauche pour l\'interface de test');
