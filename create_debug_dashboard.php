<?php

echo "🔍 CRÉATION D'UN DASHBOARD DE DEBUG\n";
echo "===================================\n\n";

// Créer une version debug du dashboard
$debugDashboardContent = '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Dashboard GRADIS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>
    <style>
        .debug-info { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .chart-container { border: 2px dashed #dee2e6; margin: 20px 0; padding: 20px; }
        .status { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="mt-3">🔍 Debug Dashboard GRADIS</h1>
        
        <div class="debug-info">
            <h3>📊 État du Système</h3>
            <div id="system-status">⏳ Vérification en cours...</div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="chart-container">
                    <h4>📈 Test Graphique 1 - Ligne</h4>
                    <div id="debug-status-1" class="status warning">⏳ En attente...</div>
                    <div id="debugChart1" style="height: 300px; background: #f8f9fa;"></div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="chart-container">
                    <h4>🍩 Test Graphique 2 - Donut</h4>
                    <div id="debug-status-2" class="status warning">⏳ En attente...</div>
                    <div id="debugChart2" style="height: 300px; background: #f8f9fa;"></div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="chart-container">
                    <h4>📊 Test Graphique 3 - Barres</h4>
                    <div id="debug-status-3" class="status warning">⏳ En attente...</div>
                    <div id="debugChart3" style="height: 300px; background: #f8f9fa;"></div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="chart-container">
                    <h4>📈 Test Graphique 4 - Aires</h4>
                    <div id="debug-status-4" class="status warning">⏳ En attente...</div>
                    <div id="debugChart4" style="height: 300px; background: #f8f9fa;"></div>
                </div>
            </div>
        </div>
        
        <div class="debug-info">
            <h3>🔧 Logs de Debug</h3>
            <div id="debug-logs" style="background: #000; color: #0f0; padding: 15px; font-family: monospace; height: 200px; overflow-y: auto;">
                Initialisation des logs...<br>
            </div>
        </div>
        
        <div class="debug-info">
            <h3>📋 Actions de Debug</h3>
            <button class="btn btn-primary" onclick="testApexCharts()">🧪 Tester ApexCharts</button>
            <button class="btn btn-success" onclick="createAllCharts()">📊 Créer Tous les Graphiques</button>
            <button class="btn btn-warning" onclick="clearLogs()">🧹 Vider les Logs</button>
            <button class="btn btn-info" onclick="checkContainers()">📦 Vérifier Conteneurs</button>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById("debug-logs");
        
        function log(message, type = "info") {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === "error" ? "#f00" : type === "success" ? "#0f0" : type === "warning" ? "#ff0" : "#0ff";
            logContainer.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(chartId, message, type) {
            const statusEl = document.getElementById(`debug-status-${chartId}`);
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function clearLogs() {
            logContainer.innerHTML = "Logs vidés...<br>";
        }
        
        function checkContainers() {
            log("🔍 Vérification des conteneurs...", "info");
            const containers = ["debugChart1", "debugChart2", "debugChart3", "debugChart4"];
            
            containers.forEach((id, index) => {
                const container = document.getElementById(id);
                if (container) {
                    log(`✅ Conteneur ${id} trouvé`, "success");
                    updateStatus(index + 1, "✅ Conteneur OK", "success");
                } else {
                    log(`❌ Conteneur ${id} MANQUANT`, "error");
                    updateStatus(index + 1, "❌ Conteneur manquant", "error");
                }
            });
        }
        
        function testApexCharts() {
            log("🧪 Test ApexCharts...", "info");
            
            if (typeof ApexCharts === "undefined") {
                log("❌ ApexCharts NON CHARGÉ", "error");
                document.getElementById("system-status").innerHTML = `<span class="text-danger">❌ ApexCharts non chargé</span>`;
                return false;
            }
            
            log(`✅ ApexCharts chargé (version: ${ApexCharts.version || "inconnue"})`, "success");
            document.getElementById("system-status").innerHTML = `<span class="text-success">✅ ApexCharts OK (v${ApexCharts.version || "?"})</span>`;
            return true;
        }
        
        function createChart(containerId, options, chartName, statusId) {
            try {
                log(`📊 Création graphique ${chartName}...`, "info");
                
                const container = document.getElementById(containerId);
                if (!container) {
                    throw new Error(`Conteneur ${containerId} non trouvé`);
                }
                
                const chart = new ApexCharts(container, options);
                chart.render().then(() => {
                    log(`✅ Graphique ${chartName} créé avec succès`, "success");
                    updateStatus(statusId, `✅ ${chartName} OK`, "success");
                }).catch(error => {
                    throw error;
                });
                
            } catch (error) {
                log(`❌ Erreur graphique ${chartName}: ${error.message}`, "error");
                updateStatus(statusId, `❌ Erreur: ${error.message}`, "error");
            }
        }
        
        function createAllCharts() {
            if (!testApexCharts()) {
                return;
            }
            
            log("🚀 Création de tous les graphiques...", "info");
            
            // Graphique 1 - Ligne
            setTimeout(() => {
                createChart("debugChart1", {
                    series: [{
                        name: "Ventes",
                        data: [10, 20, 15, 25, 30, 20, 35]
                    }],
                    chart: {
                        type: "line",
                        height: 300,
                        toolbar: { show: false }
                    },
                    xaxis: {
                        categories: ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun", "Jul"]
                    },
                    colors: ["#007bff"]
                }, "Ligne", 1);
            }, 100);
            
            // Graphique 2 - Donut
            setTimeout(() => {
                createChart("debugChart2", {
                    series: [44, 55, 13, 43],
                    chart: {
                        type: "donut",
                        height: 300
                    },
                    labels: ["Ciment", "Fer", "Sable", "Gravier"],
                    colors: ["#007bff", "#28a745", "#ffc107", "#dc3545"]
                }, "Donut", 2);
            }, 200);
            
            // Graphique 3 - Barres
            setTimeout(() => {
                createChart("debugChart3", {
                    series: [{
                        name: "Quantité",
                        data: [30, 40, 35, 50, 49, 60, 70]
                    }],
                    chart: {
                        type: "bar",
                        height: 300,
                        toolbar: { show: false }
                    },
                    xaxis: {
                        categories: ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun", "Jul"]
                    },
                    colors: ["#28a745"]
                }, "Barres", 3);
            }, 300);
            
            // Graphique 4 - Aires
            setTimeout(() => {
                createChart("debugChart4", {
                    series: [{
                        name: "Revenus",
                        data: [15000, 22000, 18000, 25000, 30000, 28000, 35000]
                    }],
                    chart: {
                        type: "area",
                        height: 300,
                        toolbar: { show: false }
                    },
                    xaxis: {
                        categories: ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun", "Jul"]
                    },
                    colors: ["#17a2b8"],
                    fill: {
                        type: "gradient",
                        gradient: {
                            shadeIntensity: 1,
                            opacityFrom: 0.7,
                            opacityTo: 0.3
                        }
                    }
                }, "Aires", 4);
            }, 400);
        }
        
        // Auto-initialisation
        document.addEventListener("DOMContentLoaded", function() {
            log("🚀 Page de debug chargée", "info");
            
            setTimeout(() => {
                checkContainers();
                testApexCharts();
            }, 500);
            
            setTimeout(() => {
                log("🎯 Création automatique des graphiques...", "info");
                createAllCharts();
            }, 1000);
        });
    </script>
</body>
</html>';

// Sauvegarder la page de debug
$debugPath = 'public/debug-dashboard.html';
if (file_put_contents($debugPath, $debugDashboardContent)) {
    echo "✅ Dashboard de debug créé: $debugPath\n";
    echo "🌐 Accédez à: http://127.0.0.1:8000/debug-dashboard.html\n";
} else {
    echo "❌ Erreur création dashboard de debug\n";
}

echo "\n🎯 INSTRUCTIONS:\n";
echo "================\n";
echo "1. 🌐 Ouvrez: http://127.0.0.1:8000/debug-dashboard.html\n";
echo "2. 👀 Regardez si les graphiques s'affichent\n";
echo "3. 🔍 Vérifiez les logs de debug en bas de page\n";
echo "4. 🧪 Utilisez les boutons de test\n";
echo "5. 📱 Ouvrez la console du navigateur (F12) pour plus de détails\n";

echo "\n💡 Si les graphiques s'affichent dans le debug:\n";
echo "   ➡️  Le problème vient du dashboard principal\n";
echo "   ➡️  Nous devrons corriger les conteneurs HTML\n";
echo "\n💡 Si les graphiques ne s'affichent pas dans le debug:\n";
echo "   ➡️  Le problème vient d'ApexCharts ou du serveur\n";
echo "   ➡️  Nous devrons changer de librairie de graphiques\n";

echo "\n🔧 Dashboard de debug créé avec succès!\n";
