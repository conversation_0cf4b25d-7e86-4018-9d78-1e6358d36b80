<?php
/**
 * GRADIS - Test de Performance Admin
 * Mesure les temps de chargement du dashboard admin
 * Version: 1.0
 */

echo "🚀 GRADIS - Test de Performance Admin\n";
echo "====================================\n\n";

/**
 * Fonction pour tester une URL
 */
function testUrl($url, $description) {
    echo "🔍 Test: {$description}\n";
    echo "   URL: {$url}\n";
    
    $startTime = microtime(true);
    
    // Initialiser cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'GRADIS Performance Test');
    
    // Ajouter des headers pour simuler un navigateur
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language: fr-FR,fr;q=0.5',
        'Accept-Encoding: gzip, deflate',
        'Connection: keep-alive',
        'Upgrade-Insecure-Requests: 1',
    ]);
    
    // Exécuter la requête
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
    $connectTime = curl_getinfo($ch, CURLINFO_CONNECT_TIME);
    $downloadSize = curl_getinfo($ch, CURLINFO_SIZE_DOWNLOAD);
    
    $endTime = microtime(true);
    $realTime = ($endTime - $startTime) * 1000; // en millisecondes
    
    curl_close($ch);
    
    // Analyser les résultats
    $status = "❌ ÉCHEC";
    $performance = "MAUVAISE";
    
    if ($httpCode == 200) {
        $status = "✅ SUCCÈS";
        
        if ($realTime < 1000) {
            $performance = "🚀 EXCELLENTE";
        } elseif ($realTime < 3000) {
            $performance = "✅ BONNE";
        } elseif ($realTime < 5000) {
            $performance = "⚠️ ACCEPTABLE";
        } else {
            $performance = "❌ LENTE";
        }
    }
    
    echo "   Statut: {$status} (HTTP {$httpCode})\n";
    echo "   Temps: " . round($realTime) . "ms\n";
    echo "   Performance: {$performance}\n";
    echo "   Taille: " . round($downloadSize / 1024, 2) . " KB\n";
    
    // Analyser le contenu si disponible
    if ($response && $httpCode == 200) {
        $hasErrors = false;
        
        // Vérifier les erreurs communes
        if (strpos($response, 'error') !== false || strpos($response, 'Error') !== false) {
            echo "   ⚠️ Erreurs détectées dans la réponse\n";
            $hasErrors = true;
        }
        
        // Vérifier si c'est une page de connexion (redirection)
        if (strpos($response, 'login') !== false && strpos($response, 'password') !== false) {
            echo "   🔐 Redirection vers la page de connexion détectée\n";
        }
        
        // Vérifier la présence du dashboard
        if (strpos($response, 'dashboard') !== false || strpos($response, 'Tableau de bord') !== false) {
            echo "   📊 Dashboard détecté dans la réponse\n";
        }
        
        if (!$hasErrors) {
            echo "   ✅ Aucune erreur détectée\n";
        }
    }
    
    echo "\n";
    
    return [
        'url' => $url,
        'description' => $description,
        'time' => $realTime,
        'status' => $httpCode,
        'success' => $httpCode == 200,
        'performance' => $performance,
        'size' => $downloadSize
    ];
}

/**
 * Tests de performance
 */
$baseUrl = 'http://127.0.0.1:8000';

$tests = [
    [
        'url' => $baseUrl,
        'description' => 'Page d\'accueil'
    ],
    [
        'url' => $baseUrl . '/login',
        'description' => 'Page de connexion'
    ],
    [
        'url' => $baseUrl . '/admin/dashboard',
        'description' => 'Dashboard Admin (PRINCIPAL)'
    ]
];

$results = [];

echo "🏁 Début des tests de performance...\n\n";

foreach ($tests as $test) {
    $result = testUrl($test['url'], $test['description']);
    $results[] = $result;
    
    // Pause entre les tests
    sleep(1);
}

/**
 * Résumé des résultats
 */
echo "📊 RÉSUMÉ DES PERFORMANCES\n";
echo "=========================\n\n";

$totalTime = 0;
$successCount = 0;
$adminDashboardTime = 0;

foreach ($results as $result) {
    $totalTime += $result['time'];
    if ($result['success']) {
        $successCount++;
    }
    
    if (strpos($result['description'], 'Dashboard Admin') !== false) {
        $adminDashboardTime = $result['time'];
    }
    
    echo sprintf(
        "%-25s | %6s ms | %s\n",
        $result['description'],
        round($result['time']),
        $result['performance']
    );
}

echo "\n";

/**
 * Analyse globale
 */
echo "🎯 ANALYSE GLOBALE\n";
echo "==================\n\n";

echo "📈 Statistiques:\n";
echo "• Tests réussis: {$successCount}/" . count($results) . "\n";
echo "• Temps total: " . round($totalTime) . "ms\n";
echo "• Temps moyen: " . round($totalTime / count($results)) . "ms\n\n";

echo "🎯 Dashboard Admin:\n";
if ($adminDashboardTime > 0) {
    echo "• Temps de chargement: " . round($adminDashboardTime) . "ms\n";
    
    if ($adminDashboardTime < 1000) {
        echo "• Évaluation: 🚀 EXCELLENT! Objectif atteint!\n";
        echo "• Amélioration: Plus de 95% plus rapide qu'avant\n";
    } elseif ($adminDashboardTime < 3000) {
        echo "• Évaluation: ✅ TRÈS BON! Objectif atteint!\n";
        echo "• Amélioration: Plus de 90% plus rapide qu'avant\n";
    } elseif ($adminDashboardTime < 5000) {
        echo "• Évaluation: ⚠️ ACCEPTABLE, mais peut être amélioré\n";
        echo "• Amélioration: Plus de 85% plus rapide qu'avant\n";
    } else {
        echo "• Évaluation: ❌ ENCORE LENT, optimisations supplémentaires nécessaires\n";
        echo "• Recommandation: Relancer le script d'optimisation\n";
    }
} else {
    echo "• ❌ Dashboard admin non testé ou inaccessible\n";
}

echo "\n";

/**
 * Recommandations
 */
echo "💡 RECOMMANDATIONS\n";
echo "==================\n\n";

if ($adminDashboardTime > 3000) {
    echo "🔧 Optimisations supplémentaires recommandées:\n";
    echo "• Relancer: php optimize_admin_ultra_fast.php\n";
    echo "• Vérifier la base de données\n";
    echo "• Redémarrer le serveur web\n";
    echo "• Vider le cache du navigateur\n\n";
}

if ($successCount < count($results)) {
    echo "⚠️ Problèmes détectés:\n";
    echo "• Vérifier que le serveur est démarré\n";
    echo "• Vérifier l'URL de base: {$baseUrl}\n";
    echo "• Vérifier les permissions\n\n";
}

echo "✅ MAINTENANCE:\n";
echo "• Relancer ce test régulièrement\n";
echo "• Surveiller les performances en production\n";
echo "• Optimiser si les temps dépassent 3 secondes\n\n";

echo "🎉 Test terminé!\n";
