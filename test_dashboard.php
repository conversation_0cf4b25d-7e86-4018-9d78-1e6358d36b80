<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "Test du Dashboard Controller...\n";
    
    // Simuler une requête
    $request = Illuminate\Http\Request::create('/admin/dashboard', 'GET');
    $app->instance('request', $request);
    
    // Créer le contrôleur
    $controller = new App\Http\Controllers\Admin\DashboardController();
    
    echo "✅ DashboardController créé avec succès\n";
    
    // Tester si on peut accéder aux variables nécessaires
    $stockService = new App\Services\StockService();
    $stockStatus = $stockService->getStockStatus();
    $stockStats = [
        'total_products' => $stockStatus['summary']['total_products'],
        'total_stock_value' => $stockStatus['summary']['total_value'],
        'low_stock_count' => $stockStatus['summary']['low_stock_count'],
        'out_of_stock_count' => $stockStatus['summary']['out_of_stock_count'],
        'normal_stock_count' => $stockStatus['summary']['normal_stock_count']
    ];
    
    echo "✅ Variables de stock créées avec succès\n";
    echo "Total produits: " . $stockStats['total_products'] . "\n";
    echo "Stock faible: " . $stockStats['low_stock_count'] . "\n";
    echo "Rupture de stock: " . $stockStats['out_of_stock_count'] . "\n";
    
    echo "\n🎉 Le dashboard devrait fonctionner correctement!\n";
    echo "Vérifiez l'URL: http://127.0.0.1:8000/admin/dashboard\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . "\n";
    echo "Ligne: " . $e->getLine() . "\n";
    echo "\nTrace:\n" . $e->getTraceAsString() . "\n";
}
