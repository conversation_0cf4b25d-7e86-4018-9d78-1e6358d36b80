<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class DashboardPerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;
    protected $accountantUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer des utilisateurs de test
        $this->adminUser = User::factory()->create();
        $this->adminUser->assignRole('admin');
        
        $this->accountantUser = User::factory()->create();
        $this->accountantUser->assignRole('accountant');
    }

    /**
     * Test des performances du dashboard Admin
     */
    public function test_admin_dashboard_performance()
    {
        // Nettoyer le cache pour un test propre
        Cache::flush();
        
        // Mesurer le temps et les requêtes
        $startTime = microtime(true);
        $startQueries = count(DB::getQueryLog());
        
        DB::enableQueryLog();
        
        $response = $this->actingAs($this->adminUser)
                        ->get('/admin/dashboard');
        
        $endTime = microtime(true);
        $endQueries = count(DB::getQueryLog());
        
        $executionTime = ($endTime - $startTime) * 1000; // en millisecondes
        $queryCount = $endQueries - $startQueries;
        
        // Assertions de performance
        $response->assertStatus(200);
        $this->assertLessThan(2000, $executionTime, "Dashboard Admin trop lent: {$executionTime}ms");
        $this->assertLessThan(15, $queryCount, "Trop de requêtes SQL: {$queryCount}");
        
        echo "\n🔍 Performance Dashboard Admin:\n";
        echo "   ⏱️  Temps d'exécution: " . round($executionTime, 2) . "ms\n";
        echo "   🗄️  Nombre de requêtes: {$queryCount}\n";
    }

    /**
     * Test des performances du dashboard Comptable
     */
    public function test_accountant_dashboard_performance()
    {
        // Nettoyer le cache pour un test propre
        Cache::flush();
        
        // Mesurer le temps et les requêtes
        $startTime = microtime(true);
        $startQueries = count(DB::getQueryLog());
        
        DB::enableQueryLog();
        
        $response = $this->actingAs($this->accountantUser)
                        ->get('/accountant/dashboard');
        
        $endTime = microtime(true);
        $endQueries = count(DB::getQueryLog());
        
        $executionTime = ($endTime - $startTime) * 1000; // en millisecondes
        $queryCount = $endQueries - $startQueries;
        
        // Assertions de performance
        $response->assertStatus(200);
        $this->assertLessThan(1500, $executionTime, "Dashboard Comptable trop lent: {$executionTime}ms");
        $this->assertLessThan(12, $queryCount, "Trop de requêtes SQL: {$queryCount}");
        
        echo "\n🔍 Performance Dashboard Comptable:\n";
        echo "   ⏱️  Temps d'exécution: " . round($executionTime, 2) . "ms\n";
        echo "   🗄️  Nombre de requêtes: {$queryCount}\n";
    }

    /**
     * Test de l'efficacité du cache
     */
    public function test_cache_effectiveness()
    {
        // Premier appel (sans cache)
        Cache::flush();
        $startTime1 = microtime(true);
        
        $response1 = $this->actingAs($this->adminUser)
                         ->get('/admin/dashboard');
        
        $time1 = (microtime(true) - $startTime1) * 1000;
        
        // Deuxième appel (avec cache)
        $startTime2 = microtime(true);
        
        $response2 = $this->actingAs($this->adminUser)
                         ->get('/admin/dashboard');
        
        $time2 = (microtime(true) - $startTime2) * 1000;
        
        // Le deuxième appel doit être significativement plus rapide
        $improvement = (($time1 - $time2) / $time1) * 100;
        
        $response1->assertStatus(200);
        $response2->assertStatus(200);
        $this->assertGreaterThan(30, $improvement, "Le cache n'améliore pas assez les performances");
        
        echo "\n📊 Efficacité du Cache:\n";
        echo "   🥇 Premier appel (sans cache): " . round($time1, 2) . "ms\n";
        echo "   🥈 Deuxième appel (avec cache): " . round($time2, 2) . "ms\n";
        echo "   📈 Amélioration: " . round($improvement, 1) . "%\n";
    }

    /**
     * Test de la mémoire utilisée
     */
    public function test_memory_usage()
    {
        $startMemory = memory_get_usage(true);
        
        $response = $this->actingAs($this->adminUser)
                        ->get('/admin/dashboard');
        
        $endMemory = memory_get_usage(true);
        $memoryUsed = ($endMemory - $startMemory) / 1024 / 1024; // en MB
        
        $response->assertStatus(200);
        $this->assertLessThan(64, $memoryUsed, "Utilisation mémoire excessive: {$memoryUsed}MB");
        
        echo "\n💾 Utilisation Mémoire:\n";
        echo "   📊 Mémoire utilisée: " . round($memoryUsed, 2) . "MB\n";
        echo "   📊 Mémoire totale: " . round(memory_get_usage(true) / 1024 / 1024, 2) . "MB\n";
    }

    /**
     * Test de charge simulée
     */
    public function test_load_simulation()
    {
        $times = [];
        $iterations = 5;
        
        echo "\n🔄 Test de Charge (5 requêtes consécutives):\n";
        
        for ($i = 1; $i <= $iterations; $i++) {
            $startTime = microtime(true);
            
            $response = $this->actingAs($this->adminUser)
                            ->get('/admin/dashboard');
            
            $executionTime = (microtime(true) - $startTime) * 1000;
            $times[] = $executionTime;
            
            $response->assertStatus(200);
            
            echo "   #{$i}: " . round($executionTime, 2) . "ms\n";
        }
        
        $averageTime = array_sum($times) / count($times);
        $maxTime = max($times);
        $minTime = min($times);
        
        echo "   📊 Temps moyen: " . round($averageTime, 2) . "ms\n";
        echo "   📊 Temps min: " . round($minTime, 2) . "ms\n";
        echo "   📊 Temps max: " . round($maxTime, 2) . "ms\n";
        
        // Le temps moyen ne doit pas dépasser 1 seconde
        $this->assertLessThan(1000, $averageTime, "Temps moyen trop élevé: {$averageTime}ms");
        
        // La variation ne doit pas être trop importante (cache efficace)
        $variation = (($maxTime - $minTime) / $averageTime) * 100;
        $this->assertLessThan(200, $variation, "Variation trop importante: {$variation}%");
    }

    /**
     * Test des requêtes SQL optimisées
     */
    public function test_sql_query_optimization()
    {
        DB::enableQueryLog();
        
        $response = $this->actingAs($this->adminUser)
                        ->get('/admin/dashboard');
        
        $queries = DB::getQueryLog();
        
        // Vérifier qu'il n'y a pas de requêtes N+1
        $selectQueries = array_filter($queries, function($query) {
            return strpos(strtolower($query['query']), 'select') === 0;
        });
        
        $response->assertStatus(200);
        $this->assertLessThan(15, count($selectQueries), "Trop de requêtes SELECT: " . count($selectQueries));
        
        // Vérifier qu'il n'y a pas de requêtes lentes simulées
        foreach ($queries as $query) {
            $this->assertLessThan(100, $query['time'], "Requête lente détectée: {$query['time']}ms");
        }
        
        echo "\n🗄️ Analyse des Requêtes SQL:\n";
        echo "   📊 Total requêtes: " . count($queries) . "\n";
        echo "   📊 Requêtes SELECT: " . count($selectQueries) . "\n";
        
        // Afficher les requêtes les plus lentes
        usort($queries, function($a, $b) {
            return $b['time'] <=> $a['time'];
        });
        
        echo "   🐌 Requête la plus lente: " . round($queries[0]['time'], 2) . "ms\n";
    }
}
