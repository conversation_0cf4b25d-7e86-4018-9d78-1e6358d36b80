<?php

echo "🔍 Test d'extraction des données de stock...\n\n";

// Simuler les différents formats de badges que nous utilisons
$testCases = [
    // Format avec icône FontAwesome
    '<span class="badge bg-danger fs-6 px-3 py-2"><i class="fas fa-times-circle me-1"></i>0 sacs</span>',
    '<span class="badge bg-warning fs-6 px-3 py-2"><i class="fas fa-exclamation-triangle me-1"></i>5 m³</span>',
    '<span class="badge bg-success fs-6 px-3 py-2"><i class="fas fa-check-circle me-1"></i>150 kg</span>',
    
    // Format simple (au cas où)
    '<span class="badge bg-primary">25 unités</span>',
    '<span class="badge">100 pièces</span>',
    
    // Cas limites
    '<span class="badge bg-danger">0 unités</span>',
    '<span class="badge bg-success">1000 tonnes</span>',
];

echo "📋 Test des expressions régulières pour extraire les nombres:\n";
echo "============================================================\n";

foreach ($testCases as $index => $html) {
    // Simuler l'extraction du textContent (sans les balises HTML)
    $textContent = strip_tags($html);
    
    // Utiliser la même regex que dans le JavaScript
    preg_match('/(\d+)/', $textContent, $matches);
    $extractedNumber = isset($matches[1]) ? intval($matches[1]) : 0;
    
    echo sprintf(
        "Test %d:\n  HTML: %s\n  Text: %s\n  Nombre extrait: %d\n\n",
        $index + 1,
        $html,
        $textContent,
        $extractedNumber
    );
}

echo "🧪 Test avec des données réelles...\n";
echo "===================================\n";

// Simuler des données comme elles apparaîtraient dans le tableau
$realWorldExamples = [
    'CIMCO-CPJ 45-SAVANE' => '0 sacs',
    'CIMCO-CPJ 45-LOMÉ' => '25 sacs', 
    'CIMTOGO-CPJ 45-INTERIEUR' => '150 sacs',
    'CIMTOGO-CPJ 45-LOMÉ' => '75 sacs',
    'Sable fin' => '5 m³',
    'Gravier' => '8 m³',
    'Fer 12mm' => '150 kg',
];

foreach ($realWorldExamples as $productName => $stockText) {
    preg_match('/(\d+)/', $stockText, $matches);
    $stock = isset($matches[1]) ? intval($matches[1]) : 0;
    
    echo sprintf("✅ %-25s: '%s' → %d\n", $productName, $stockText, $stock);
}

echo "\n🔧 JavaScript équivalent:\n";
echo "=========================\n";
echo "const stockMatch = stockText.match(/(\\d+)/);\n";
echo "const stock = stockMatch ? parseInt(stockMatch[1]) : 0;\n\n";

echo "📝 Points importants:\n";
echo "====================\n";
echo "• La regex (\\d+) capture le premier nombre trouvé\n";
echo "• parseInt() convertit la chaîne en nombre entier\n";
echo "• Si aucun nombre n'est trouvé, on retourne 0\n";
echo "• Les icônes FontAwesome sont ignorées automatiquement\n\n";

echo "🎯 Résultat attendu:\n";
echo "====================\n";
echo "Plus de messages 'NaN' dans les notifications!\n";
echo "Les changements de stock afficheront des nombres réels.\n\n";

echo "🧪 Pour tester:\n";
echo "===============\n";
echo "1. Videz le cache: php artisan view:clear\n";
echo "2. Actualisez le dashboard: Ctrl+F5\n";
echo "3. Cliquez sur 'Actualiser' dans l'onglet Stock\n";
echo "4. Vérifiez la console (F12) pour les logs de debug\n";
echo "5. Les notifications devraient maintenant afficher des nombres corrects\n";
