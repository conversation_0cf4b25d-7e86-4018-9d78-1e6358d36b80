<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeaders
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Configuration différente selon l'environnement
        if (config('app.env') === 'production') {
            $this->setProductionHeaders($response, $request);
        } else {
            $this->setDevelopmentHeaders($response);
        }

        return $response;
    }

    /**
     * Headers de sécurité pour la production
     */
    private function setProductionHeaders(Response $response, Request $request): void
    {
        // CSP strict pour la production
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' https://code.jquery.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
               "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.bunny.net https://cdnjs.cloudflare.com; " .
               "font-src 'self' https://fonts.gstatic.com https://fonts.bunny.net data:; " .
               "img-src 'self' data: https:; " .
               "connect-src 'self'; " .
               "object-src 'none'; " .
               "base-uri 'self'; " .
               "form-action 'self'; " .
               "frame-ancestors 'none'";

        $response->headers->set('Content-Security-Policy', $csp);

        // Headers de sécurité adaptés à l'environnement
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Permissions Policy pour la production
        $response->headers->set('Permissions-Policy', 'geolocation=(), microphone=(), camera=(), payment=(), usb=()');

        // HSTS si HTTPS
        if ($request->secure()) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }

        // Masquer les informations du serveur
        $response->headers->remove('X-Powered-By');
        $response->headers->remove('Server');
    }

    /**
     * Headers de sécurité pour le développement
     */
    private function setDevelopmentHeaders(Response $response): void
    {
        // CSP très permissive pour le développement - éviter les blocages
        $csp = "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' https: http: data: blob:; " .
               "style-src 'self' 'unsafe-inline' https: http: data: blob:; " .
               "font-src 'self' https: http: data: blob:; " .
               "img-src 'self' data: https: http: blob:; " .
               "connect-src 'self' https: http: ws: wss: data: blob:; " .
               "media-src 'self' data: blob:; " .
               "object-src 'self' data: blob:; " .
               "frame-ancestors 'self'";

        $response->headers->set('Content-Security-Policy', $csp);
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-XSS-Protection', '1; mode=block');

        // Permissions Policy très permissive en développement - autoriser unload pour éviter les erreurs d'extensions
        $response->headers->set('Permissions-Policy', 'geolocation=(), microphone=(), camera=(), unload=*');
    }
}
