<?php

namespace App\Services;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Http\Request;

class SecurityService
{
    /**
     * Sanitise une chaîne de caractères
     */
    public function sanitizeString(string $input): string
    {
        // Supprimer les balises HTML
        $sanitized = strip_tags($input);
        
        // Échapper les caractères spéciaux
        $sanitized = htmlspecialchars($sanitized, ENT_QUOTES, 'UTF-8');
        
        // Supprimer les caractères de contrôle
        $sanitized = preg_replace('/[\x00-\x1F\x7F]/', '', $sanitized);
        
        return trim($sanitized);
    }

    /**
     * Valide et sanitise un email
     */
    public function sanitizeEmail(string $email): ?string
    {
        $email = trim(strtolower($email));
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return null;
        }
        
        return $email;
    }

    /**
     * Valide un numéro de téléphone
     */
    public function validatePhone(string $phone): bool
    {
        // Supprimer tous les caractères non numériques sauf +
        $phone = preg_replace('/[^\d+]/', '', $phone);
        
        // Vérifier le format (8-15 chiffres, peut commencer par +)
        return preg_match('/^\+?[1-9]\d{7,14}$/', $phone);
    }

    /**
     * Génère un mot de passe sécurisé
     */
    public function generateSecurePassword(int $length = 12): string
    {
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        
        $password = '';
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $symbols[random_int(0, strlen($symbols) - 1)];
        
        $allChars = $lowercase . $uppercase . $numbers . $symbols;
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }
        
        return str_shuffle($password);
    }

    /**
     * Vérifie la force d'un mot de passe
     */
    public function checkPasswordStrength(string $password): array
    {
        $score = 0;
        $feedback = [];
        
        // Longueur
        if (strlen($password) >= 8) {
            $score += 1;
        } else {
            $feedback[] = 'Le mot de passe doit contenir au moins 8 caractères';
        }
        
        // Minuscules
        if (preg_match('/[a-z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Le mot de passe doit contenir au moins une lettre minuscule';
        }
        
        // Majuscules
        if (preg_match('/[A-Z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Le mot de passe doit contenir au moins une lettre majuscule';
        }
        
        // Chiffres
        if (preg_match('/[0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Le mot de passe doit contenir au moins un chiffre';
        }
        
        // Caractères spéciaux
        if (preg_match('/[^a-zA-Z0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Le mot de passe doit contenir au moins un caractère spécial';
        }
        
        $strength = 'Très faible';
        if ($score >= 5) $strength = 'Très fort';
        elseif ($score >= 4) $strength = 'Fort';
        elseif ($score >= 3) $strength = 'Moyen';
        elseif ($score >= 2) $strength = 'Faible';
        
        return [
            'score' => $score,
            'strength' => $strength,
            'feedback' => $feedback
        ];
    }

    /**
     * Détecte les tentatives d'injection SQL
     */
    public function detectSqlInjection(string $input): bool
    {
        $patterns = [
            '/(\bunion\b.*\bselect\b)/i',
            '/(\bselect\b.*\bfrom\b)/i',
            '/(\binsert\b.*\binto\b)/i',
            '/(\bupdate\b.*\bset\b)/i',
            '/(\bdelete\b.*\bfrom\b)/i',
            '/(\bdrop\b.*\btable\b)/i',
            '/(\bor\b.*=.*)/i',
            '/(\band\b.*=.*)/i',
            '/(\'.*or.*\'.*=.*\')/i',
            '/(\".*or.*\".*=.*\")/i',
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Détecte les tentatives XSS
     */
    public function detectXss(string $input): bool
    {
        $patterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<img[^>]+src[^>]*>/i',
            '/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/mi',
            '/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/mi',
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Enregistre une tentative d'attaque
     */
    public function logSecurityIncident(Request $request, string $type, string $details): void
    {
        Log::warning('Tentative d\'attaque détectée', [
            'type' => $type,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'details' => $details,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Valide les données d'entrée contre les attaques
     */
    public function validateInput(Request $request, array $data): array
    {
        $threats = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                if ($this->detectSqlInjection($value)) {
                    $threats[] = "Tentative d'injection SQL détectée dans le champ: {$key}";
                    $this->logSecurityIncident($request, 'SQL_INJECTION', "Champ: {$key}, Valeur: {$value}");
                }
                
                if ($this->detectXss($value)) {
                    $threats[] = "Tentative XSS détectée dans le champ: {$key}";
                    $this->logSecurityIncident($request, 'XSS', "Champ: {$key}, Valeur: {$value}");
                }
            }
        }
        
        return $threats;
    }

    /**
     * Génère un token CSRF personnalisé
     */
    public function generateCsrfToken(): string
    {
        return Str::random(40);
    }

    /**
     * Vérifie l'intégrité d'un fichier uploadé
     */
    public function validateFileIntegrity(string $filePath, array $allowedMimeTypes = []): bool
    {
        if (!file_exists($filePath)) {
            return false;
        }
        
        // Vérifier le type MIME
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $filePath);
        finfo_close($finfo);
        
        if (!empty($allowedMimeTypes) && !in_array($mimeType, $allowedMimeTypes)) {
            return false;
        }
        
        // Vérifier la signature du fichier
        $handle = fopen($filePath, 'rb');
        $signature = fread($handle, 8);
        fclose($handle);
        
        // Signatures de fichiers malveillants connus
        $maliciousSignatures = [
            "\x4D\x5A", // Exécutable Windows
            "\x7F\x45\x4C\x46", // Exécutable Linux
        ];
        
        foreach ($maliciousSignatures as $maliciousSignature) {
            if (strpos($signature, $maliciousSignature) === 0) {
                return false;
            }
        }
        
        return true;
    }
}
