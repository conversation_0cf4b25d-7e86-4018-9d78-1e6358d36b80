<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Ventes</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 10px;
            line-height: 1.4;
            color: #333;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }

        .header h1 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .header p {
            font-size: 12px;
            opacity: 0.9;
        }

        .stats-section {
            margin-bottom: 20px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .stat-card {
            background: white;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-card h3 {
            font-size: 11px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .stat-card .value {
            font-size: 14px;
            font-weight: bold;
            color: #495057;
        }

        .sales-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .sales-table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .sales-table th {
            padding: 8px 6px;
            text-align: left;
            font-weight: bold;
            font-size: 9px;
            border-right: 1px solid rgba(255,255,255,0.2);
        }

        .sales-table th:last-child {
            border-right: none;
        }

        .sales-table td {
            padding: 6px;
            border-bottom: 1px solid #e9ecef;
            font-size: 8px;
            vertical-align: top;
        }

        .sales-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .sales-table tbody tr:hover {
            background-color: #e3f2fd;
        }

        .status-badge {
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 7px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }

        .status-partial {
            background-color: #cce5ff;
            color: #004085;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }

        .status-in-progress {
            background-color: #cce5ff;
            color: #004085;
        }

        .status-cancelled {
            background-color: #f8d7da;
            color: #721c24;
        }

        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 8px;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
            padding-top: 10px;
        }

        .amount {
            text-align: right;
            font-weight: bold;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        @media print {
            body {
                font-size: 9px;
            }
            
            .header h1 {
                font-size: 16px;
            }
            
            .sales-table th,
            .sales-table td {
                padding: 4px;
                font-size: 7px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 LISTE DES VENTES</h1>
        <p>Rapport généré le {{ now()->format('d/m/Y à H:i') }}</p>
        <p>Période: {{ $sales->min('created_at')?->format('d/m/Y') ?? 'N/A' }} - {{ $sales->max('created_at')?->format('d/m/Y') ?? 'N/A' }}</p>
    </div>

    <div class="stats-section">
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Ventes</h3>
                <div class="value">{{ $sales->count() }}</div>
            </div>
            <div class="stat-card">
                <h3>Montant Total</h3>
                <div class="value">{{ number_format($sales->sum('total_amount'), 0, ',', ' ') }} FCFA</div>
            </div>
            <div class="stat-card">
                <h3>Montant Payé</h3>
                <div class="value">{{ number_format($sales->sum('amount_paid'), 0, ',', ' ') }} FCFA</div>
            </div>
        </div>
    </div>

    <table class="sales-table">
        <thead>
            <tr>
                <th style="width: 8%;">ID</th>
                <th style="width: 12%;">Date</th>
                <th style="width: 20%;">Client</th>
                <th style="width: 12%;">Téléphone</th>
                <th style="width: 15%;">Produit</th>
                <th style="width: 8%;">Qté (T)</th>
                <th style="width: 10%;">Montant</th>
                <th style="width: 8%;">Paiement</th>
                <th style="width: 7%;">Livraison</th>
            </tr>
        </thead>
        <tbody>
            @forelse($sales as $sale)
                <tr>
                    <td class="text-center">#{{ $sale->id }}</td>
                    <td>{{ $sale->created_at->format('d/m/Y H:i') }}</td>
                    <td>{{ $sale->customer_name ?? 'N/A' }}</td>
                    <td>{{ $sale->customer_phone ?? 'N/A' }}</td>
                    <td>{{ $sale->supply->details->first()->product->name ?? 'N/A' }}</td>
                    <td class="text-center">{{ number_format($sale->quantity, 1) }}</td>
                    <td class="amount">{{ number_format($sale->total_amount, 0, ',', ' ') }}</td>
                    <td class="text-center">
                        @php
                            $paymentStatus = $sale->payment_status;
                            $statusClass = 'status-' . str_replace('_', '-', $paymentStatus);
                            $statusText = [
                                'pending' => 'En attente',
                                'partial' => 'Partiel', 
                                'paid' => 'Payé',
                                'overdue' => 'Retard'
                            ][$paymentStatus] ?? $paymentStatus;
                        @endphp
                        <span class="status-badge {{ $statusClass }}">{{ $statusText }}</span>
                    </td>
                    <td class="text-center">
                        @php
                            $deliveryStatus = $sale->delivery_status;
                            $statusClass = 'status-' . str_replace('_', '-', $deliveryStatus);
                            $statusText = [
                                'pending' => 'Attente',
                                'in_progress' => 'En cours',
                                'completed' => 'Terminé',
                                'cancelled' => 'Annulé'
                            ][$deliveryStatus] ?? $deliveryStatus;
                        @endphp
                        <span class="status-badge {{ $statusClass }}">{{ $statusText }}</span>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="9" class="text-center" style="padding: 20px; color: #6c757d;">
                        Aucune vente trouvée
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="footer">
        <p>Document généré automatiquement par GRADIS - Système de Gestion des Ventes</p>
        <p>© {{ date('Y') }} GRADIS. Tous droits réservés.</p>
    </div>
</body>
</html>
