<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Payment;

class ClearPaymentsTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payments:clear {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vide complètement la table payments (suppression définitive)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== VIDAGE DE LA TABLE PAYMENTS ===');

        // Compter les paiements
        $paymentsCount = Payment::withTrashed()->count();

        if ($paymentsCount === 0) {
            $this->info('✅ La table payments est déjà vide.');
            return 0;
        }

        $this->info("Nombre total de paiements trouvés : {$paymentsCount}");

        // Demander confirmation si --force n'est pas utilisé
        if (!$this->option('force')) {
            $this->warn('⚠️  ATTENTION : Cette action va supprimer DÉFINITIVEMENT tous les paiements !');
            $this->warn('⚠️  Cela affectera les statuts de paiement des ventes liées !');
            
            if (!$this->confirm('Êtes-vous sûr de vouloir continuer ?')) {
                $this->info('❌ Opération annulée.');
                return 0;
            }
        }

        try {
            $this->clearPayments($paymentsCount);
            $this->info('✅ Opération terminée avec succès !');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la suppression : ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function clearPayments($paymentsCount)
    {
        $this->info('🔄 Vidage de la table payments...');

        try {
            // Étape 1: Supprimer définitivement tous les paiements
            $this->info('1. Suppression des paiements...');
            Payment::withTrashed()->forceDelete();

            // Étape 2: Reset auto-increment
            $this->info('2. Reset de l\'auto-increment...');
            DB::statement('ALTER TABLE payments AUTO_INCREMENT = 1');

            // Étape 3: Mettre à jour les statuts des ventes liées
            $this->info('3. Mise à jour des statuts des ventes...');
            $salesUpdated = DB::table('sales')->update([
                'amount_paid' => 0.00,
                'payment_status' => 'pending'
            ]);

            $this->info("✅ {$paymentsCount} paiements supprimés définitivement");
            $this->info("✅ {$salesUpdated} ventes mises à jour");
            $this->info('✅ Auto-increment remis à 1');

        } catch (\Exception $e) {
            throw $e;
        }
    }
}
