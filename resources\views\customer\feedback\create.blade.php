@extends('layouts.app')

@section('content')
<div class="feedback-wizard-container">
    <div class="container-fluid">
        <!-- En-tête avec design moderne -->
        <div class="feedback-header text-center mb-5">
            <div class="header-icon-wrapper">
                <div class="header-icon">
                    <i class="fas fa-comments"></i>
                </div>
            </div>
            <h1 class="feedback-title">Partager votre Expérience</h1>
            <p class="feedback-subtitle">Votre avis nous aide à améliorer nos services et à mieux vous servir</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-xl-10">
                <!-- Indicateur de progression -->
                <div class="progress-indicator mb-5">
                    <div class="progress-step active" data-step="1">
                        <div class="step-circle">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <span class="step-label">Type & Catégorie</span>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step" data-step="2">
                        <div class="step-circle">
                            <i class="fas fa-edit"></i>
                        </div>
                        <span class="step-label">Détails</span>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step" data-step="3">
                        <div class="step-circle">
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="step-label">Évaluation</span>
                    </div>
                    <div class="progress-line"></div>
                    <div class="progress-step" data-step="4">
                        <div class="step-circle">
                            <i class="fas fa-check"></i>
                        </div>
                        <span class="step-label">Finalisation</span>
                    </div>
                </div>

                <!-- Formulaire avec étapes -->
                <div class="wizard-card">
                    <form method="POST" action="{{ route('customer.feedback.store') }}" id="feedbackForm">
                        @csrf

                        <!-- Étape 1: Type et Catégorie -->
                        <div class="wizard-step active" id="step1">
                            <div class="step-header">
                                <h3 class="step-title">
                                    <i class="fas fa-clipboard-list me-2"></i>
                                    Quel type de feedback souhaitez-vous partager ?
                                </h3>
                                <p class="step-description">Sélectionnez le type et la catégorie qui correspondent le mieux à votre expérience</p>
                            </div>

                            <div class="step-content">
                                <!-- Type de feedback avec cartes -->
                                <div class="mb-5">
                                    <label class="section-label">Type de feedback <span class="text-danger">*</span></label>
                                    <div class="feedback-type-grid">
                                        <div class="feedback-type-card" data-type="delivery">
                                            <div class="type-icon">
                                                <i class="fas fa-truck"></i>
                                            </div>
                                            <h5>Livraison</h5>
                                            <p>Qualité, ponctualité, service de livraison</p>
                                        </div>
                                        <div class="feedback-type-card" data-type="service">
                                            <div class="type-icon">
                                                <i class="fas fa-users"></i>
                                            </div>
                                            <h5>Service Client</h5>
                                            <p>Accueil, support, communication</p>
                                        </div>
                                        <div class="feedback-type-card" data-type="product">
                                            <div class="type-icon">
                                                <i class="fas fa-box"></i>
                                            </div>
                                            <h5>Produit</h5>
                                            <p>Qualité, conformité, emballage</p>
                                        </div>
                                        <div class="feedback-type-card" data-type="general">
                                            <div class="type-icon">
                                                <i class="fas fa-comment-dots"></i>
                                            </div>
                                            <h5>Général</h5>
                                            <p>Expérience globale, suggestions</p>
                                        </div>
                                    </div>
                                    <input type="hidden" name="type" id="selectedType" value="{{ old('type') }}" required>
                                    @error('type')
                                        <div class="error-message">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Catégorie avec cartes -->
                                <div class="mb-5">
                                    <label class="section-label">Catégorie <span class="text-danger">*</span></label>
                                    <div class="feedback-category-grid">
                                        <div class="feedback-category-card" data-category="compliment">
                                            <div class="category-icon compliment">
                                                <i class="fas fa-thumbs-up"></i>
                                            </div>
                                            <h6>Compliment</h6>
                                            <p>Féliciter l'équipe</p>
                                        </div>
                                        <div class="feedback-category-card" data-category="suggestion">
                                            <div class="category-icon suggestion">
                                                <i class="fas fa-lightbulb"></i>
                                            </div>
                                            <h6>Suggestion</h6>
                                            <p>Proposer une amélioration</p>
                                        </div>
                                        <div class="feedback-category-card" data-category="question">
                                            <div class="category-icon question">
                                                <i class="fas fa-question-circle"></i>
                                            </div>
                                            <h6>Question</h6>
                                            <p>Demander des informations</p>
                                        </div>
                                        <div class="feedback-category-card" data-category="complaint">
                                            <div class="category-icon complaint">
                                                <i class="fas fa-exclamation-triangle"></i>
                                            </div>
                                            <h6>Réclamation</h6>
                                            <p>Signaler un problème</p>
                                        </div>
                                    </div>
                                    <input type="hidden" name="category" id="selectedCategory" value="{{ old('category') }}" required>
                                    @error('category')
                                        <div class="error-message">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Commande associée -->
                                <div class="mb-4">
                                    <label class="section-label">Commande concernée (optionnel)</label>
                                    <div class="order-selector">
                                        <select name="cement_order_detail_id" class="form-select modern-select @error('cement_order_detail_id') is-invalid @enderror">
                                            <option value="">Aucune commande spécifique</option>
                                            @foreach($recentOrders as $orderDetail)
                                                <option value="{{ $orderDetail->id }}"
                                                        {{ (old('cement_order_detail_id') == $orderDetail->id || (isset($order) && $order->id == $orderDetail->id)) ? 'selected' : '' }}>
                                                    #{{ $orderDetail->id }} - {{ $orderDetail->cementOrder->product->name ?? 'N/A' }}
                                                    ({{ $orderDetail->created_at->format('d/m/Y') }})
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('cement_order_detail_id')
                                            <div class="error-message">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Étape 2: Détails -->
                        <div class="wizard-step" id="step2">
                            <div class="step-header">
                                <h3 class="step-title">
                                    <i class="fas fa-edit me-2"></i>
                                    Partagez les détails de votre expérience
                                </h3>
                                <p class="step-description">Décrivez votre expérience pour nous aider à mieux comprendre votre situation</p>
                            </div>

                            <div class="step-content">
                                <!-- Sujet avec suggestions -->
                                <div class="mb-5">
                                    <label class="section-label">Sujet <span class="text-danger">*</span></label>
                                    <div class="input-group-modern">
                                        <div class="input-icon">
                                            <i class="fas fa-tag"></i>
                                        </div>
                                        <input type="text" name="subject" class="form-control modern-input @error('subject') is-invalid @enderror"
                                               value="{{ old('subject') }}" placeholder="Résumez votre feedback en quelques mots" required>
                                    </div>
                                    <div class="subject-suggestions" id="subjectSuggestions" style="display: none;">
                                        <p class="suggestions-label">Suggestions basées sur votre sélection :</p>
                                        <div class="suggestions-list"></div>
                                    </div>
                                    @error('subject')
                                        <div class="error-message">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Message avec compteur de caractères -->
                                <div class="mb-5">
                                    <label class="section-label">Message détaillé <span class="text-danger">*</span></label>
                                    <div class="textarea-wrapper">
                                        <textarea name="message" rows="6" class="form-control modern-textarea @error('message') is-invalid @enderror"
                                                  placeholder="Décrivez votre expérience en détail..." required>{{ old('message') }}</textarea>
                                        <div class="textarea-footer">
                                            <div class="character-count">
                                                <span id="charCount">0</span> / 1000 caractères
                                            </div>
                                            <div class="textarea-help">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Plus vous êtes précis, mieux nous pourrons vous aider
                                            </div>
                                        </div>
                                    </div>
                                    @error('message')
                                        <div class="error-message">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Informations de livraison (si type = delivery) -->
                                <div class="delivery-info-section" id="deliveryInfoStep2" style="display: none;">
                                    <h5 class="subsection-title">
                                        <i class="fas fa-truck me-2"></i>
                                        Informations de livraison (optionnel)
                                    </h5>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label class="section-label">Nom du chauffeur</label>
                                            <div class="input-group-modern">
                                                <div class="input-icon">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <input type="text" name="driver_name" class="form-control modern-input" value="{{ old('driver_name') }}" placeholder="Ex: Jean Dupont">
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label class="section-label">Numéro du camion</label>
                                            <div class="input-group-modern">
                                                <div class="input-icon">
                                                    <i class="fas fa-truck"></i>
                                                </div>
                                                <input type="text" name="truck_number" class="form-control modern-input" value="{{ old('truck_number') }}" placeholder="Ex: CAM-001">
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label class="section-label">Date de livraison</label>
                                            <div class="input-group-modern">
                                                <div class="input-icon">
                                                    <i class="fas fa-calendar"></i>
                                                </div>
                                                <input type="date" name="delivery_date" class="form-control modern-input" value="{{ old('delivery_date') }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Étape 3: Évaluation -->
                        <div class="wizard-step" id="step3">
                            <div class="step-header">
                                <h3 class="step-title">
                                    <i class="fas fa-star me-2"></i>
                                    Évaluez votre expérience
                                </h3>
                                <p class="step-description">Donnez une note pour nous aider à mesurer la qualité de nos services</p>
                            </div>

                            <div class="step-content">
                                <!-- Évaluation globale (toujours visible) -->
                                <div class="rating-section mb-5">
                                    <div class="rating-card main-rating">
                                        <div class="rating-header">
                                            <h5>
                                                <i class="fas fa-star-half-alt me-2"></i>
                                                Évaluation globale
                                            </h5>
                                            <p>Comment évaluez-vous votre expérience dans l'ensemble ?</p>
                                        </div>
                                        <div class="rating-stars-wrapper">
                                            <div class="rating-input-modern" data-rating="overall_rating">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <div class="star-wrapper">
                                                        <i class="fas fa-star rating-star" data-value="{{ $i }}"></i>
                                                        <span class="star-label">{{ $i == 1 ? 'Très mauvais' : ($i == 2 ? 'Mauvais' : ($i == 3 ? 'Correct' : ($i == 4 ? 'Bon' : 'Excellent'))) }}</span>
                                                    </div>
                                                @endfor
                                            </div>
                                            <div class="rating-feedback" id="overallFeedback"></div>
                                        </div>
                                        <input type="hidden" name="overall_rating" value="{{ old('overall_rating') }}">
                                    </div>
                                </div>

                                <!-- Évaluations spécifiques -->
                                <div class="specific-ratings">
                                    <!-- Évaluation livraison -->
                                    <div class="rating-section mb-4" id="deliveryRatingSection" style="display: none;">
                                        <div class="rating-card">
                                            <div class="rating-header">
                                                <h6>
                                                    <i class="fas fa-truck me-2"></i>
                                                    Qualité de la livraison
                                                </h6>
                                                <p>Ponctualité, état du produit, professionnalisme du livreur</p>
                                            </div>
                                            <div class="rating-stars-wrapper">
                                                <div class="rating-input-modern compact" data-rating="delivery_rating">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star rating-star" data-value="{{ $i }}"></i>
                                                    @endfor
                                                </div>
                                            </div>
                                            <input type="hidden" name="delivery_rating" value="{{ old('delivery_rating') }}">
                                        </div>
                                    </div>

                                    <!-- Évaluation service -->
                                    <div class="rating-section mb-4" id="serviceRatingSection" style="display: none;">
                                        <div class="rating-card">
                                            <div class="rating-header">
                                                <h6>
                                                    <i class="fas fa-users me-2"></i>
                                                    Qualité du service
                                                </h6>
                                                <p>Accueil, écoute, réactivité, résolution des problèmes</p>
                                            </div>
                                            <div class="rating-stars-wrapper">
                                                <div class="rating-input-modern compact" data-rating="service_rating">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star rating-star" data-value="{{ $i }}"></i>
                                                    @endfor
                                                </div>
                                            </div>
                                            <input type="hidden" name="service_rating" value="{{ old('service_rating') }}">
                                        </div>
                                    </div>

                                    <!-- Évaluation produit -->
                                    <div class="rating-section mb-4" id="productRatingSection" style="display: none;">
                                        <div class="rating-card">
                                            <div class="rating-header">
                                                <h6>
                                                    <i class="fas fa-box me-2"></i>
                                                    Qualité du produit
                                                </h6>
                                                <p>Conformité, qualité, emballage, état à la réception</p>
                                            </div>
                                            <div class="rating-stars-wrapper">
                                                <div class="rating-input-modern compact" data-rating="product_rating">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star rating-star" data-value="{{ $i }}"></i>
                                                    @endfor
                                                </div>
                                            </div>
                                            <input type="hidden" name="product_rating" value="{{ old('product_rating') }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Étape 4: Finalisation -->
                        <div class="wizard-step" id="step4">
                            <div class="step-header">
                                <h3 class="step-title">
                                    <i class="fas fa-check me-2"></i>
                                    Finalisation de votre feedback
                                </h3>
                                <p class="step-description">Vérifiez vos informations et ajoutez les derniers détails</p>
                            </div>

                            <div class="step-content">
                                <!-- Résumé du feedback -->
                                <div class="feedback-summary mb-5">
                                    <h5 class="summary-title">
                                        <i class="fas fa-clipboard-check me-2"></i>
                                        Résumé de votre feedback
                                    </h5>
                                    <div class="summary-content">
                                        <div class="summary-item">
                                            <strong>Type:</strong> <span id="summaryType">-</span>
                                        </div>
                                        <div class="summary-item">
                                            <strong>Catégorie:</strong> <span id="summaryCategory">-</span>
                                        </div>
                                        <div class="summary-item">
                                            <strong>Sujet:</strong> <span id="summarySubject">-</span>
                                        </div>
                                        <div class="summary-item">
                                            <strong>Note globale:</strong> <span id="summaryRating">-</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Options finales -->
                                <div class="final-options mb-5">
                                    <h6 class="options-title">Options supplémentaires</h6>

                                    <div class="option-card">
                                        <div class="form-check-modern">
                                            <input type="checkbox" name="is_anonymous" class="form-check-input" id="anonymous"
                                                   {{ old('is_anonymous') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="anonymous">
                                                <div class="check-content">
                                                    <div class="check-icon">
                                                        <i class="fas fa-user-secret"></i>
                                                    </div>
                                                    <div class="check-text">
                                                        <strong>Feedback anonyme</strong>
                                                        <p>Votre identité ne sera pas révélée avec ce feedback</p>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="option-card">
                                        <div class="form-check-modern">
                                            <input type="checkbox" name="request_callback" class="form-check-input" id="callback">
                                            <label class="form-check-label" for="callback">
                                                <div class="check-content">
                                                    <div class="check-icon">
                                                        <i class="fas fa-phone"></i>
                                                    </div>
                                                    <div class="check-text">
                                                        <strong>Demander un rappel</strong>
                                                        <p>Notre équipe vous contactera pour discuter de votre feedback</p>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Message de confirmation -->
                                <div class="confirmation-message">
                                    <div class="confirmation-icon">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                    <h6>Merci de partager votre expérience !</h6>
                                    <p>Votre feedback nous aide à améliorer continuellement nos services pour mieux vous servir.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation du wizard -->
                        <div class="wizard-navigation">
                            <div class="nav-buttons">
                                <button type="button" class="btn btn-outline-secondary" id="prevBtn" style="display: none;">
                                    <i class="fas fa-arrow-left me-2"></i>Précédent
                                </button>
                                <div class="nav-spacer"></div>
                                <a href="/demo/customer/dashboard" class="btn btn-outline-secondary me-3" id="cancelBtn">
                                    <i class="fas fa-times me-2"></i>Annuler
                                </a>
                                <button type="button" class="btn btn-primary" id="nextBtn">
                                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                                </button>
                                <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                                    <i class="fas fa-paper-plane me-2"></i>Envoyer le Feedback
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    /* Variables CSS pour la cohérence */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
        --shadow-medium: 0 5px 20px rgba(0,0,0,0.15);
        --shadow-heavy: 0 10px 30px rgba(0,0,0,0.2);
        --border-radius: 15px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Container principal */
    .feedback-wizard-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 2rem 0;
    }

    /* En-tête moderne */
    .feedback-header {
        margin-bottom: 3rem;
    }

    .header-icon-wrapper {
        position: relative;
        display: inline-block;
        margin-bottom: 1.5rem;
    }

    .header-icon {
        width: 80px;
        height: 80px;
        background: var(--primary-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--shadow-medium);
        animation: pulse 2s infinite;
    }

    .header-icon i {
        font-size: 2rem;
        color: white;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .feedback-title {
        font-size: 2.5rem;
        font-weight: 700;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
    }

    .feedback-subtitle {
        font-size: 1.1rem;
        color: #6c757d;
        max-width: 600px;
        margin: 0 auto;
    }

    /* Indicateur de progression */
    .progress-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin-bottom: 3rem;
    }

    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .step-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.5rem;
        transition: var(--transition);
        border: 3px solid #e9ecef;
    }

    .progress-step.active .step-circle {
        background: var(--primary-gradient);
        border-color: transparent;
        box-shadow: var(--shadow-medium);
        transform: scale(1.1);
    }

    .progress-step.completed .step-circle {
        background: var(--success-gradient);
        border-color: transparent;
    }

    .step-circle i {
        font-size: 1.2rem;
        color: #6c757d;
        transition: var(--transition);
    }

    .progress-step.active .step-circle i,
    .progress-step.completed .step-circle i {
        color: white;
    }

    .step-label {
        font-size: 0.9rem;
        font-weight: 600;
        color: #6c757d;
        text-align: center;
        transition: var(--transition);
    }

    .progress-step.active .step-label {
        color: #495057;
        font-weight: 700;
    }

    .progress-line {
        flex: 1;
        height: 3px;
        background: #e9ecef;
        margin: 0 1rem;
        position: relative;
        top: -30px;
    }

    .progress-line.completed {
        background: var(--success-gradient);
    }

    /* Carte du wizard */
    .wizard-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-medium);
        overflow: hidden;
        min-height: 600px;
        position: relative;
    }

    /* Étapes du wizard */
    .wizard-step {
        display: none;
        padding: 3rem;
        animation: fadeInUp 0.5s ease;
    }

    .wizard-step.active {
        display: block;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .step-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .step-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .step-description {
        font-size: 1rem;
        color: #6c757d;
        max-width: 500px;
        margin: 0 auto;
    }

    /* Labels de section */
    .section-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 1rem;
        display: block;
        font-size: 1rem;
    }

    /* Grille des types de feedback */
    .feedback-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .feedback-type-card {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 2rem 1.5rem;
        text-align: center;
        cursor: pointer;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .feedback-type-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--primary-gradient);
        opacity: 0.1;
        transition: var(--transition);
    }

    .feedback-type-card:hover::before {
        left: 0;
    }

    .feedback-type-card:hover {
        border-color: #667eea;
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }

    .feedback-type-card.selected {
        border-color: #667eea;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        box-shadow: var(--shadow-medium);
    }

    .type-icon {
        width: 60px;
        height: 60px;
        background: var(--primary-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        transition: var(--transition);
    }

    .feedback-type-card:hover .type-icon {
        transform: scale(1.1);
    }

    .type-icon i {
        font-size: 1.5rem;
        color: white;
    }

    .feedback-type-card h5 {
        font-weight: 700;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .feedback-type-card p {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }

    /* Grille des catégories */
    .feedback-category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .feedback-category-card {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 1.5rem 1rem;
        text-align: center;
        cursor: pointer;
        transition: var(--transition);
    }

    .feedback-category-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-light);
    }

    .feedback-category-card.selected {
        border-color: #667eea;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    }

    .category-icon {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.75rem;
        transition: var(--transition);
    }

    .category-icon.compliment {
        background: var(--success-gradient);
    }

    .category-icon.suggestion {
        background: var(--info-gradient);
    }

    .category-icon.question {
        background: var(--warning-gradient);
    }

    .category-icon.complaint {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    }

    .category-icon i {
        color: white;
        font-size: 1.1rem;
    }

    .feedback-category-card h6 {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
    }

    .feedback-category-card p {
        color: #6c757d;
        font-size: 0.8rem;
        margin: 0;
    }

    /* Éléments de formulaire modernes */
    .input-group-modern {
        position: relative;
        margin-bottom: 0.5rem;
    }

    .input-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 2;
    }

    .modern-input, .modern-select {
        padding-left: 45px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: var(--transition);
        background: white;
    }

    .modern-input:focus, .modern-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }

    .modern-textarea {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: var(--transition);
        resize: vertical;
        min-height: 120px;
    }

    .modern-textarea:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }

    .textarea-wrapper {
        position: relative;
    }

    .textarea-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.5rem;
        font-size: 0.85rem;
    }

    .character-count {
        color: #6c757d;
    }

    .textarea-help {
        color: #6c757d;
    }

    /* Suggestions de sujet */
    .subject-suggestions {
        margin-top: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .suggestions-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .suggestions-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .suggestion-tag {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 20px;
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
        cursor: pointer;
        transition: var(--transition);
    }

    .suggestion-tag:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    /* Sections d'évaluation */
    .rating-section {
        margin-bottom: 2rem;
    }

    .rating-card {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 2rem;
        border: 2px solid transparent;
        transition: var(--transition);
    }

    .rating-card.main-rating {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-color: #dee2e6;
    }

    .rating-header h5, .rating-header h6 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .rating-header p {
        color: #6c757d;
        margin: 0;
        font-size: 0.9rem;
    }

    .rating-stars-wrapper {
        margin-top: 1.5rem;
    }

    .rating-input-modern {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        justify-content: center;
    }

    .rating-input-modern.compact {
        justify-content: flex-start;
        gap: 0.25rem;
    }

    .star-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
    }

    .rating-star {
        font-size: 2rem;
        color: #dee2e6;
        cursor: pointer;
        transition: var(--transition);
        margin-bottom: 0.5rem;
    }

    .rating-input-modern.compact .rating-star {
        font-size: 1.5rem;
        margin-bottom: 0;
    }

    .rating-star:hover,
    .rating-star.active {
        color: #ffc107;
        transform: scale(1.1);
    }

    .star-label {
        font-size: 0.7rem;
        color: #6c757d;
        text-align: center;
        opacity: 0;
        transition: var(--transition);
    }

    .star-wrapper:hover .star-label {
        opacity: 1;
    }

    .rating-feedback {
        text-align: center;
        margin-top: 1rem;
        font-weight: 600;
        font-size: 1.1rem;
        min-height: 1.5rem;
    }

    /* Étape de finalisation */
    .feedback-summary {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 2rem;
        border-left: 4px solid #667eea;
    }

    .summary-title {
        color: #495057;
        margin-bottom: 1.5rem;
    }

    .summary-content {
        display: grid;
        gap: 1rem;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #dee2e6;
    }

    .summary-item:last-child {
        border-bottom: none;
    }

    .summary-item strong {
        color: #495057;
    }

    .summary-item span {
        color: #6c757d;
        font-weight: 500;
    }

    /* Options finales */
    .final-options {
        margin-top: 2rem;
    }

    .options-title {
        color: #495057;
        margin-bottom: 1.5rem;
        font-weight: 600;
    }

    .option-card {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
        transition: var(--transition);
    }

    .option-card:hover {
        border-color: #667eea;
        box-shadow: var(--shadow-light);
    }

    .form-check-modern {
        padding: 1.5rem;
    }

    .form-check-modern .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
        margin-top: 0;
    }

    .form-check-modern .form-check-label {
        cursor: pointer;
        width: 100%;
    }

    .check-content {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        margin-left: 2rem;
    }

    .check-icon {
        width: 40px;
        height: 40px;
        background: var(--primary-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .check-icon i {
        color: white;
        font-size: 1rem;
    }

    .check-text strong {
        color: #495057;
        display: block;
        margin-bottom: 0.25rem;
    }

    .check-text p {
        color: #6c757d;
        margin: 0;
        font-size: 0.9rem;
    }

    /* Message de confirmation */
    .confirmation-message {
        text-align: center;
        padding: 2rem;
        background: linear-gradient(135deg, rgba(17, 153, 142, 0.1), rgba(56, 239, 125, 0.1));
        border-radius: var(--border-radius);
        border: 2px solid rgba(17, 153, 142, 0.2);
    }

    .confirmation-icon {
        width: 60px;
        height: 60px;
        background: var(--success-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        animation: heartbeat 1.5s ease-in-out infinite;
    }

    @keyframes heartbeat {
        0% { transform: scale(1); }
        14% { transform: scale(1.1); }
        28% { transform: scale(1); }
        42% { transform: scale(1.1); }
        70% { transform: scale(1); }
    }

    .confirmation-icon i {
        color: white;
        font-size: 1.5rem;
    }

    .confirmation-message h6 {
        color: #495057;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .confirmation-message p {
        color: #6c757d;
        margin: 0;
    }

    /* Navigation du wizard */
    .wizard-navigation {
        background: white;
        border-top: 1px solid #dee2e6;
        padding: 1.5rem 3rem;
        margin-top: auto;
    }

    .nav-buttons {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .nav-spacer {
        flex: 1;
    }

    .wizard-navigation .btn {
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        transition: var(--transition);
    }

    .wizard-navigation .btn-primary {
        background: var(--primary-gradient);
        border: none;
        box-shadow: var(--shadow-light);
    }

    .wizard-navigation .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }

    .wizard-navigation .btn-success {
        background: var(--success-gradient);
        border: none;
        box-shadow: var(--shadow-light);
    }

    .wizard-navigation .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }

    /* Messages d'erreur */
    .error-message {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: block;
    }

    /* Sections spécifiques */
    .delivery-info-section {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-top: 2rem;
        border-left: 4px solid #17a2b8;
    }

    .subsection-title {
        color: #495057;
        margin-bottom: 1.5rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .feedback-wizard-container {
            padding: 1rem 0;
        }

        .feedback-title {
            font-size: 2rem;
        }

        .progress-indicator {
            flex-wrap: wrap;
            gap: 1rem;
        }

        .progress-line {
            display: none;
        }

        .wizard-step {
            padding: 2rem 1.5rem;
        }

        .wizard-navigation {
            padding: 1rem 1.5rem;
        }

        .feedback-type-grid {
            grid-template-columns: 1fr;
        }

        .feedback-category-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .nav-buttons {
            flex-direction: column;
            gap: 0.5rem;
        }

        .nav-spacer {
            display: none;
        }

        .rating-input-modern {
            flex-wrap: wrap;
            justify-content: center;
        }

        .star-wrapper {
            margin: 0.25rem;
        }
    }

    @media (max-width: 480px) {
        .feedback-category-grid {
            grid-template-columns: 1fr;
        }

        .step-title {
            font-size: 1.5rem;
        }

        .rating-star {
            font-size: 1.5rem;
        }
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Variables globales
    let currentStep = 1;
    const totalSteps = 4;
    const formData = {};

    // Éléments DOM
    const steps = document.querySelectorAll('.wizard-step');
    const progressSteps = document.querySelectorAll('.progress-step');
    const progressLines = document.querySelectorAll('.progress-line');
    const nextBtn = document.getElementById('nextBtn');
    const prevBtn = document.getElementById('prevBtn');
    const submitBtn = document.getElementById('submitBtn');

    // Suggestions de sujets par type et catégorie
    const subjectSuggestions = {
        delivery: {
            compliment: ['Livraison parfaite !', 'Chauffeur très professionnel', 'Livraison rapide et soignée'],
            complaint: ['Retard de livraison', 'Produit endommagé', 'Chauffeur impoli'],
            suggestion: ['Améliorer les horaires', 'SMS de confirmation', 'Meilleur emballage'],
            question: ['Horaires de livraison', 'Procédure de réclamation', 'Suivi de commande']
        },
        service: {
            compliment: ['Excellent service client', 'Équipe très réactive', 'Conseils utiles'],
            complaint: ['Temps d\'attente trop long', 'Manque d\'information', 'Personnel peu aimable'],
            suggestion: ['Améliorer l\'accueil', 'Formation du personnel', 'Système de rendez-vous'],
            question: ['Procédures de commande', 'Garanties produits', 'Conditions de paiement']
        },
        product: {
            compliment: ['Excellente qualité', 'Conforme à mes attentes', 'Très bon rapport qualité-prix'],
            complaint: ['Qualité décevante', 'Non conforme', 'Emballage défaillant'],
            suggestion: ['Améliorer l\'emballage', 'Plus de variétés', 'Meilleure information produit'],
            question: ['Caractéristiques techniques', 'Conditions de stockage', 'Utilisation recommandée']
        },
        general: {
            compliment: ['Très satisfait globalement', 'Entreprise recommandable', 'Expérience positive'],
            complaint: ['Expérience décevante', 'Problèmes récurrents', 'Manque de suivi'],
            suggestion: ['Améliorer la communication', 'Site web plus intuitif', 'Programme de fidélité'],
            question: ['Informations générales', 'Nouveaux services', 'Conditions générales']
        }
    };

    // Initialisation
    showStep(currentStep);
    initializeTypeSelection();
    initializeCategorySelection();
    initializeRatingSystem();
    initializeFormValidation();
    initializeCharacterCounter();

    // Navigation
    nextBtn.addEventListener('click', function() {
        if (validateCurrentStep()) {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
                updateSummary();
            }
        }
    });

    prevBtn.addEventListener('click', function() {
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
        }
    });

    // Fonctions principales
    function showStep(step) {
        // Masquer toutes les étapes
        steps.forEach(s => s.classList.remove('active'));

        // Afficher l'étape courante
        document.getElementById(`step${step}`).classList.add('active');

        // Mettre à jour l'indicateur de progression
        updateProgressIndicator(step);

        // Mettre à jour les boutons
        updateNavigationButtons(step);

        // Actions spécifiques par étape
        if (step === 3) {
            updateRatingVisibility();
        }
    }

    function updateProgressIndicator(step) {
        progressSteps.forEach((ps, index) => {
            const stepNumber = index + 1;
            ps.classList.remove('active', 'completed');

            if (stepNumber < step) {
                ps.classList.add('completed');
            } else if (stepNumber === step) {
                ps.classList.add('active');
            }
        });

        progressLines.forEach((line, index) => {
            if (index < step - 1) {
                line.classList.add('completed');
            } else {
                line.classList.remove('completed');
            }
        });
    }

    function updateNavigationButtons(step) {
        prevBtn.style.display = step > 1 ? 'block' : 'none';
        nextBtn.style.display = step < totalSteps ? 'block' : 'none';
        submitBtn.style.display = step === totalSteps ? 'block' : 'none';
    }

    function initializeTypeSelection() {
        const typeCards = document.querySelectorAll('.feedback-type-card');
        const typeInput = document.getElementById('selectedType');

        typeCards.forEach(card => {
            card.addEventListener('click', function() {
                // Désélectionner toutes les cartes
                typeCards.forEach(c => c.classList.remove('selected'));

                // Sélectionner la carte cliquée
                this.classList.add('selected');

                // Mettre à jour la valeur
                const type = this.dataset.type;
                typeInput.value = type;
                formData.type = type;

                // Mettre à jour les suggestions
                updateSubjectSuggestions();

                // Animation
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Restaurer la sélection si elle existe
        if (typeInput.value) {
            const selectedCard = document.querySelector(`[data-type="${typeInput.value}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
                formData.type = typeInput.value;
            }
        }
    }

    function initializeCategorySelection() {
        const categoryCards = document.querySelectorAll('.feedback-category-card');
        const categoryInput = document.getElementById('selectedCategory');

        categoryCards.forEach(card => {
            card.addEventListener('click', function() {
                // Désélectionner toutes les cartes
                categoryCards.forEach(c => c.classList.remove('selected'));

                // Sélectionner la carte cliquée
                this.classList.add('selected');

                // Mettre à jour la valeur
                const category = this.dataset.category;
                categoryInput.value = category;
                formData.category = category;

                // Mettre à jour les suggestions
                updateSubjectSuggestions();

                // Animation
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Restaurer la sélection si elle existe
        if (categoryInput.value) {
            const selectedCard = document.querySelector(`[data-category="${categoryInput.value}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
                formData.category = categoryInput.value;
            }
        }
    }

    function updateSubjectSuggestions() {
        const type = formData.type;
        const category = formData.category;
        const suggestionsContainer = document.getElementById('subjectSuggestions');
        const suggestionsList = suggestionsContainer.querySelector('.suggestions-list');

        if (type && category && subjectSuggestions[type] && subjectSuggestions[type][category]) {
            const suggestions = subjectSuggestions[type][category];
            suggestionsList.innerHTML = '';

            suggestions.forEach(suggestion => {
                const tag = document.createElement('span');
                tag.className = 'suggestion-tag';
                tag.textContent = suggestion;
                tag.addEventListener('click', function() {
                    document.querySelector('input[name="subject"]').value = suggestion;
                    formData.subject = suggestion;
                });
                suggestionsList.appendChild(tag);
            });

            suggestionsContainer.style.display = 'block';
        } else {
            suggestionsContainer.style.display = 'none';
        }
    }

    function initializeRatingSystem() {
        document.querySelectorAll('.rating-input-modern').forEach(ratingGroup => {
            const stars = ratingGroup.querySelectorAll('.rating-star');
            const ratingName = ratingGroup.dataset.rating;
            const hiddenInput = document.querySelector(`input[name="${ratingName}"]`);

            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    const rating = index + 1;
                    hiddenInput.value = rating;
                    formData[ratingName] = rating;

                    // Mettre à jour l'affichage des étoiles
                    updateStarDisplay(stars, rating);

                    // Feedback pour l'évaluation globale
                    if (ratingName === 'overall_rating') {
                        updateRatingFeedback(rating);
                    }

                    // Animation
                    star.style.transform = 'scale(1.3)';
                    setTimeout(() => {
                        star.style.transform = '';
                    }, 200);
                });

                star.addEventListener('mouseenter', function() {
                    const rating = index + 1;
                    updateStarDisplay(stars, rating, true);
                });
            });

            ratingGroup.addEventListener('mouseleave', function() {
                const currentRating = parseInt(hiddenInput.value) || 0;
                updateStarDisplay(stars, currentRating);
            });
        });
    }

    function updateStarDisplay(stars, rating, isHover = false) {
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
                star.style.color = '#ffc107';
            } else {
                star.classList.remove('active');
                star.style.color = isHover ? '#ffdb4d' : '#dee2e6';
            }
        });
    }

    function updateRatingFeedback(rating) {
        const feedback = document.getElementById('overallFeedback');
        const messages = {
            1: { text: 'Nous sommes désolés de votre expérience', color: '#dc3545' },
            2: { text: 'Nous pouvons faire mieux', color: '#fd7e14' },
            3: { text: 'Merci pour votre retour', color: '#ffc107' },
            4: { text: 'Nous sommes contents de vous satisfaire', color: '#20c997' },
            5: { text: 'Merci ! Votre satisfaction nous motive', color: '#28a745' }
        };

        if (messages[rating]) {
            feedback.textContent = messages[rating].text;
            feedback.style.color = messages[rating].color;
            feedback.style.opacity = '0';
            setTimeout(() => {
                feedback.style.opacity = '1';
            }, 100);
        }
    }

    function updateRatingVisibility() {
        const type = formData.type;
        const deliverySection = document.getElementById('deliveryRatingSection');
        const serviceSection = document.getElementById('serviceRatingSection');
        const productSection = document.getElementById('productRatingSection');
        const deliveryInfoStep2 = document.getElementById('deliveryInfoStep2');

        // Masquer toutes les sections spécifiques
        deliverySection.style.display = 'none';
        serviceSection.style.display = 'none';
        productSection.style.display = 'none';

        // Afficher selon le type
        if (type === 'delivery') {
            deliverySection.style.display = 'block';
            if (deliveryInfoStep2) deliveryInfoStep2.style.display = 'block';
        } else if (type === 'service') {
            serviceSection.style.display = 'block';
        } else if (type === 'product') {
            productSection.style.display = 'block';
        }

        if (deliveryInfoStep2 && type !== 'delivery') {
            deliveryInfoStep2.style.display = 'none';
        }
    }

    function initializeCharacterCounter() {
        const textarea = document.querySelector('textarea[name="message"]');
        const charCount = document.getElementById('charCount');

        if (textarea && charCount) {
            textarea.addEventListener('input', function() {
                const count = this.value.length;
                charCount.textContent = count;

                // Changer la couleur selon la limite
                if (count > 800) {
                    charCount.style.color = '#dc3545';
                } else if (count > 600) {
                    charCount.style.color = '#fd7e14';
                } else {
                    charCount.style.color = '#6c757d';
                }
            });
        }
    }

    function initializeFormValidation() {
        // Validation en temps réel
        const subjectInput = document.querySelector('input[name="subject"]');
        const messageTextarea = document.querySelector('textarea[name="message"]');

        if (subjectInput) {
            subjectInput.addEventListener('input', function() {
                formData.subject = this.value;
            });
        }

        if (messageTextarea) {
            messageTextarea.addEventListener('input', function() {
                formData.message = this.value;
            });
        }
    }

    function validateCurrentStep() {
        let isValid = true;
        const errorMessages = [];

        switch (currentStep) {
            case 1:
                if (!formData.type) {
                    errorMessages.push('Veuillez sélectionner un type de feedback');
                    isValid = false;
                }
                if (!formData.category) {
                    errorMessages.push('Veuillez sélectionner une catégorie');
                    isValid = false;
                }
                break;

            case 2:
                const subject = document.querySelector('input[name="subject"]').value.trim();
                const message = document.querySelector('textarea[name="message"]').value.trim();

                if (!subject) {
                    errorMessages.push('Veuillez saisir un sujet');
                    isValid = false;
                }
                if (!message || message.length < 10) {
                    errorMessages.push('Veuillez saisir un message d\'au moins 10 caractères');
                    isValid = false;
                }

                formData.subject = subject;
                formData.message = message;
                break;

            case 3:
                // L'évaluation est optionnelle, pas de validation requise
                break;
        }

        if (!isValid) {
            showValidationErrors(errorMessages);
        }

        return isValid;
    }

    function showValidationErrors(errors) {
        // Supprimer les anciens messages d'erreur
        document.querySelectorAll('.validation-error').forEach(el => el.remove());

        // Afficher les nouveaux messages
        errors.forEach(error => {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger validation-error mt-3';
            errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${error}`;

            const currentStepEl = document.getElementById(`step${currentStep}`);
            currentStepEl.querySelector('.step-content').appendChild(errorDiv);

            // Animation
            errorDiv.style.opacity = '0';
            setTimeout(() => {
                errorDiv.style.opacity = '1';
            }, 100);
        });
    }

    function updateSummary() {
        if (currentStep === 4) {
            // Mettre à jour le résumé
            const typeLabels = {
                delivery: 'Livraison',
                service: 'Service Client',
                product: 'Produit',
                general: 'Général'
            };

            const categoryLabels = {
                compliment: 'Compliment',
                suggestion: 'Suggestion',
                question: 'Question',
                complaint: 'Réclamation'
            };

            document.getElementById('summaryType').textContent = typeLabels[formData.type] || '-';
            document.getElementById('summaryCategory').textContent = categoryLabels[formData.category] || '-';
            document.getElementById('summarySubject').textContent = formData.subject || '-';

            const overallRating = formData.overall_rating;
            if (overallRating) {
                const stars = '★'.repeat(overallRating) + '☆'.repeat(5 - overallRating);
                document.getElementById('summaryRating').innerHTML = `${stars} (${overallRating}/5)`;
            } else {
                document.getElementById('summaryRating').textContent = 'Non évaluée';
            }
        }
    }

    // Gestion du formulaire final
    document.querySelector('form').addEventListener('submit', function(e) {
        if (currentStep !== totalSteps) {
            e.preventDefault();
            return;
        }

        // Animation de soumission
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Envoi en cours...';
        submitBtn.disabled = true;
    });
});
</script>
@endpush
@endsection
