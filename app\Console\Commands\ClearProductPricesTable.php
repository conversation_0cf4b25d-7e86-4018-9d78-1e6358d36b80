<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\ProductPrice;

class ClearProductPricesTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product-prices:clear {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vide complètement la table product_prices (suppression définitive)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== VIDAGE DE LA TABLE PRODUCT_PRICES ===');

        // Compter les prix de produits
        $productPricesCount = ProductPrice::count();

        if ($productPricesCount === 0) {
            $this->info('✅ La table product_prices est déjà vide.');
            return 0;
        }

        $this->info("Nombre total de prix de produits trouvés : {$productPricesCount}");

        // Demander confirmation si --force n'est pas utilisé
        if (!$this->option('force')) {
            $this->warn('⚠️  ATTENTION : Cette action va supprimer DÉFINITIVEMENT tous les prix de produits par ville !');
            $this->warn('⚠️  Les produits utiliseront leur prix de base après cette opération !');
            
            if (!$this->confirm('Êtes-vous sûr de vouloir continuer ?')) {
                $this->info('❌ Opération annulée.');
                return 0;
            }
        }

        try {
            $this->clearProductPrices($productPricesCount);
            $this->info('✅ Opération terminée avec succès !');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la suppression : ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function clearProductPrices($productPricesCount)
    {
        $this->info('🔄 Vidage de la table product_prices...');
        
        try {
            // Étape 1: Supprimer tous les prix de produits
            $this->info('1. Suppression des prix de produits...');
            ProductPrice::truncate(); // Utiliser truncate car pas de soft deletes
            
            // Étape 2: Reset auto-increment (déjà fait par truncate)
            $this->info('2. Reset de l\'auto-increment...');
            $this->info('   → Auto-increment remis à 1 (par truncate)');

            $this->info("✅ {$productPricesCount} prix de produits supprimés définitivement");
            $this->info('✅ Les produits utiliseront maintenant leur prix de base');
            
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
