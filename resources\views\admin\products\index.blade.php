@extends('layouts.admin_minimal')

@section('title', 'Gestion des Produits')

@section('content')
<div class="container-fluid py-4">
    <!-- Header avec statistiques -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h2 class="text-dark fw-bold mb-1">
                        <i class="fas fa-boxes text-primary me-2"></i>
                        Gestion des Produits
                    </h2>
                    <p class="text-muted mb-0">Gérez votre catalogue de produits</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                        <i class="fas fa-filter me-2"></i>Filtrer
                    </button>
                    <a href="{{ route('admin.products.create') }}" class="btn btn-primary btn-lg shadow-sm">
                        <i class="fas fa-plus me-2"></i>Nouveau produit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Total Produits</h6>
                            <h3 class="mb-0">{{ $products->total() }}</h3>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-boxes fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Produits Actifs</h6>
                            <h3 class="mb-0">{{ $products->where('is_active', true)->count() }}</h3>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-gradient-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Stock Faible</h6>
                            <h3 class="mb-0">{{ $products->where('stock_quantity', '<=', 10)->count() }}</h3>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card bg-gradient-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50">Catégories</h6>
                            <h3 class="mb-0">{{ $products->pluck('category.name')->unique()->count() }}</h3>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-tags fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card modern-card">
                <div class="card-header bg-white border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold text-dark">
                            <i class="fas fa-list me-2 text-primary"></i>
                            Liste des produits
                        </h5>
                        <div class="d-flex gap-2">
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="view-mode" id="table-view" checked>
                                <label class="btn btn-outline-primary btn-sm" for="table-view">
                                    <i class="fas fa-table"></i>
                                </label>
                                <input type="radio" class="btn-check" name="view-mode" id="grid-view">
                                <label class="btn btn-outline-primary btn-sm" for="grid-view">
                                    <i class="fas fa-th-large"></i>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body px-0 pt-3 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0 modern-table">
                            <thead class="table-header">
                                <tr>
                                    <th class="border-0">
                                        <i class="fas fa-cube me-2 text-primary"></i>Produit
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-tags me-2 text-success"></i>Catégorie
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-money-bill-wave me-2 text-warning"></i>Prix
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-warehouse me-2 text-info"></i>Stock
                                    </th>
                                    <th class="border-0">
                                        <i class="fas fa-toggle-on me-2 text-secondary"></i>Statut
                                    </th>
                                    <th class="text-end border-0">
                                        <i class="fas fa-cogs me-2 text-dark"></i>Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($products as $product)
                                <tr class="product-row" data-category="{{ strtolower($product->category->name) }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @php
                                                $categoryName = strtolower($product->category->name);
                                                $iconClass = 'fas fa-box';
                                                $iconColor = 'text-primary';
                                                $bgColor = 'bg-primary-light';

                                                switch($categoryName) {
                                                    case 'ciment':
                                                        $iconClass = 'fas fa-industry';
                                                        $iconColor = 'text-secondary';
                                                        $bgColor = 'bg-secondary-light';
                                                        break;
                                                    case 'fer':
                                                        $iconClass = 'fas fa-hammer';
                                                        $iconColor = 'text-dark';
                                                        $bgColor = 'bg-dark-light';
                                                        break;
                                                    case 'brique':
                                                        $iconClass = 'fas fa-th-large';
                                                        $iconColor = 'text-danger';
                                                        $bgColor = 'bg-danger-light';
                                                        break;
                                                    case 'sable':
                                                        $iconClass = 'fas fa-mountain';
                                                        $iconColor = 'text-warning';
                                                        $bgColor = 'bg-warning-light';
                                                        break;
                                                    case 'gravier':
                                                        $iconClass = 'fas fa-gem';
                                                        $iconColor = 'text-info';
                                                        $bgColor = 'bg-info-light';
                                                        break;
                                                    case 'bois':
                                                        $iconClass = 'fas fa-tree';
                                                        $iconColor = 'text-success';
                                                        $bgColor = 'bg-success-light';
                                                        break;
                                                    default:
                                                        $iconClass = 'fas fa-cube';
                                                        $iconColor = 'text-primary';
                                                        $bgColor = 'bg-primary-light';
                                                }
                                            @endphp
                                            <div class="product-icon {{ $bgColor }} rounded-circle p-3 me-3 shadow-sm">
                                                <i class="{{ $iconClass }} {{ $iconColor }} fa-lg"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1 fw-bold text-dark">{{ $product->name }}</h6>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-light text-dark me-2">
                                                        <i class="fas fa-barcode me-1"></i>{{ $product->code ?? 'N/A' }}
                                                    </span>
                                                    <small class="text-muted">{{ $product->unit }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="category-badge badge bg-gradient-{{ $categoryName === 'ciment' ? 'secondary' : ($categoryName === 'fer' ? 'dark' : ($categoryName === 'brique' ? 'danger' : ($categoryName === 'sable' ? 'warning' : ($categoryName === 'gravier' ? 'info' : 'success')))) }} text-white">
                                            <i class="{{ $iconClass }} me-1"></i>
                                            {{ $product->category->name }}
                                        </span>
                                    </td>
                                    <td>
                                        @if(strtolower($product->category->name) === 'ciment')
                                            @if($product->prices->count() > 0)
                                                <div class="price-display">
                                                    <div class="price-range-modern">
                                                        <i class="fas fa-coins text-warning me-1"></i>
                                                        <span class="min-price fw-bold text-success">{{ number_format($product->prices->min('price'), 0, ',', ' ') }}</span>
                                                        <span class="separator mx-1">-</span>
                                                        <span class="max-price fw-bold text-danger">{{ number_format($product->prices->max('price'), 0, ',', ' ') }}</span>
                                                        <span class="currency text-muted ms-1">FCFA</span>
                                                    </div>
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-map-marker-alt me-1"></i>Variable par ville
                                                    </small>
                                                </div>
                                            @else
                                                <span class="text-muted">
                                                    <i class="fas fa-exclamation-circle me-1"></i>Prix non défini
                                                </span>
                                            @endif
                                        @elseif(strtolower($product->category->name) === 'fer')
                                            @if($product->ironSpecification)
                                                <div class="iron-price-modern">
                                                    <div class="unit-price">
                                                        <i class="fas fa-weight-hanging text-dark me-1"></i>
                                                        <span class="fw-bold text-primary">{{ number_format($product->ironSpecification->unit_price, 0, ',', ' ') }}</span>
                                                        <span class="text-muted">FCFA/unité</span>
                                                    </div>
                                                    <div class="ton-price">
                                                        <i class="fas fa-balance-scale text-secondary me-1"></i>
                                                        <span class="fw-bold text-info">{{ number_format($product->ironSpecification->ton_price, 0, ',', ' ') }}</span>
                                                        <span class="text-muted">FCFA/tonne</span>
                                                    </div>
                                                </div>
                                            @else
                                                <span class="text-muted">
                                                    <i class="fas fa-exclamation-circle me-1"></i>Spécifications manquantes
                                                </span>
                                            @endif
                                        @else
                                            <div class="base-price-modern">
                                                <i class="fas fa-tag text-success me-1"></i>
                                                <span class="fw-bold text-primary">{{ number_format($product->price ?? 0, 0, ',', ' ') }}</span>
                                                <span class="text-muted">FCFA</span>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        @php
                                            $stockLevel = $product->stock_quantity;
                                            $stockClass = $stockLevel > 50 ? 'success' : ($stockLevel > 10 ? 'warning' : 'danger');
                                            $stockIcon = $stockLevel > 50 ? 'fa-check-circle' : ($stockLevel > 10 ? 'fa-exclamation-triangle' : 'fa-times-circle');
                                        @endphp
                                        <div class="stock-display">
                                            <span class="badge bg-{{ $stockClass }} text-white px-3 py-2">
                                                <i class="fas {{ $stockIcon }} me-1"></i>
                                                {{ number_format($product->stock_quantity, 0, ',', ' ') }} {{ $product->unit }}
                                            </span>
                                            <div class="stock-level-bar mt-1">
                                                <div class="progress" style="height: 4px;">
                                                    <div class="progress-bar bg-{{ $stockClass }}"
                                                         style="width: {{ min(100, ($stockLevel / 100) * 100) }}%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="status-toggle">
                                            <span class="badge bg-{{ $product->is_active ? 'success' : 'secondary' }} px-3 py-2">
                                                <i class="fas fa-{{ $product->is_active ? 'check-circle' : 'pause-circle' }} me-1"></i>
                                                {{ $product->is_active ? 'Actif' : 'Inactif' }}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="text-end">
                                        <div class="action-buttons">
                                            <div class="btn-group shadow-sm" role="group">
                                                <a href="{{ route('admin.products.show', $product) }}"
                                                   class="btn btn-outline-info btn-sm"
                                                   data-bs-toggle="tooltip"
                                                   title="Voir les détails">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.products.edit', $product) }}"
                                                   class="btn btn-outline-primary btn-sm"
                                                   data-bs-toggle="tooltip"
                                                   title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>

                                                <button type="button"
                                                        class="btn btn-outline-danger btn-sm delete-product"
                                                        data-product-id="{{ $product->id }}"
                                                        data-product-name="{{ $product->name }}"
                                                        data-bs-toggle="tooltip"
                                                        title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>

                        @if($products->isEmpty())
                        <div class="empty-state text-center py-5">
                            <div class="empty-icon mb-3">
                                <i class="fas fa-box-open fa-4x text-muted opacity-50"></i>
                            </div>
                            <h5 class="text-muted">Aucun produit trouvé</h5>
                            <p class="text-muted mb-4">Commencez par ajouter votre premier produit</p>
                            <a href="{{ route('admin.products.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Ajouter un produit
                            </a>
                        </div>
                        @endif
                    </div>

                    @if($products->hasPages())
                    <div class="card-footer bg-white border-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="pagination-info">
                                <small class="text-muted">
                                    Affichage de {{ $products->firstItem() }} à {{ $products->lastItem() }}
                                    sur {{ $products->total() }} résultats
                                </small>
                            </div>
                            <div class="pagination-wrapper">
                                {{ $products->links('pagination::bootstrap-4') }}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de filtrage moderne -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content modern-filter-modal">
            <!-- Header avec gradient -->
            <div class="modal-header filter-header">
                <div class="d-flex align-items-center">
                    <div class="filter-icon-wrapper me-3">
                        <i class="fas fa-filter"></i>
                    </div>
                    <div>
                        <h5 class="modal-title mb-0">Filtrer les produits</h5>
                        <small class="text-white-50">Affinez votre recherche avec des critères précis</small>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>

            <!-- Corps de la modale -->
            <div class="modal-body filter-body">
                <form id="filterForm">
                    <!-- Section Catégorie et Statut -->
                    <div class="filter-section mb-4">
                        <div class="section-header mb-3">
                            <h6 class="section-title">
                                <i class="fas fa-tags text-primary me-2"></i>
                                Classification
                            </h6>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select modern-select" name="category" id="categorySelect">
                                        <option value="">Toutes les catégories</option>
                                        @foreach($products->pluck('category.name')->unique() as $category)
                                        <option value="{{ $category }}">{{ $category }}</option>
                                        @endforeach
                                    </select>
                                    <label for="categorySelect">
                                        <i class="fas fa-layer-group me-2"></i>Catégorie
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select modern-select" name="status" id="statusSelect">
                                        <option value="">Tous les statuts</option>
                                        <option value="active">Actif</option>
                                        <option value="inactive">Inactif</option>
                                    </select>
                                    <label for="statusSelect">
                                        <i class="fas fa-toggle-on me-2"></i>Statut
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Stock -->
                    <div class="filter-section mb-4">
                        <div class="section-header mb-3">
                            <h6 class="section-title">
                                <i class="fas fa-boxes text-success me-2"></i>
                                Gestion des stocks
                            </h6>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="number" class="form-control modern-input" name="min_stock"
                                           id="minStockInput" placeholder="0" min="0">
                                    <label for="minStockInput">
                                        <i class="fas fa-arrow-up me-2"></i>Stock minimum
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="number" class="form-control modern-input" name="max_stock"
                                           id="maxStockInput" placeholder="1000" min="0">
                                    <label for="maxStockInput">
                                        <i class="fas fa-arrow-down me-2"></i>Stock maximum
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Actions rapides -->
                    <div class="filter-section">
                        <div class="section-header mb-3">
                            <h6 class="section-title">
                                <i class="fas fa-bolt text-warning me-2"></i>
                                Actions rapides
                            </h6>
                        </div>
                        <div class="quick-filters">
                            <button type="button" class="btn btn-outline-primary btn-sm quick-filter-btn"
                                    data-filter="low-stock">
                                <i class="fas fa-exclamation-triangle me-1"></i>Stock faible
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm quick-filter-btn"
                                    data-filter="active-only">
                                <i class="fas fa-check-circle me-1"></i>Produits actifs
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm quick-filter-btn"
                                    data-filter="cement-only">
                                <i class="fas fa-industry me-1"></i>Ciment uniquement
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-filter-btn"
                                    data-filter="reset">
                                <i class="fas fa-undo me-1"></i>Réinitialiser
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Footer avec boutons stylisés -->
            <div class="modal-footer filter-footer">
                <div class="d-flex justify-content-between w-100">
                    <button type="button" class="btn btn-light btn-cancel" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Annuler
                    </button>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-secondary btn-reset" id="resetFilter">
                            <i class="fas fa-undo me-2"></i>Réinitialiser
                        </button>
                        <button type="button" class="btn btn-primary btn-apply" id="applyFilter">
                            <i class="fas fa-search me-2"></i>Appliquer les filtres
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
@endsection

@push('styles')
<style>
/* Variables CSS personnalisées */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);
    --secondary-gradient: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
}

/* Cartes de statistiques */
.stats-card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.bg-gradient-primary { background: var(--primary-gradient) !important; }
.bg-gradient-success { background: var(--success-gradient) !important; }
.bg-gradient-warning { background: var(--warning-gradient) !important; }
.bg-gradient-info { background: var(--info-gradient) !important; }
.bg-gradient-danger { background: var(--danger-gradient) !important; }
.bg-gradient-dark { background: var(--dark-gradient) !important; }
.bg-gradient-secondary { background: var(--secondary-gradient) !important; }

.stats-icon {
    opacity: 0.8;
}

/* Carte moderne */
.modern-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

/* En-tête de tableau moderne */
.table-header th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #495057;
    border: none;
    padding: 1rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Tableau moderne */
.modern-table {
    border-collapse: separate;
    border-spacing: 0;
}

.modern-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.modern-table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: scale(1.01);
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.modern-table td {
    padding: 1.2rem 1rem;
    vertical-align: middle;
    border: none;
}

/* Icônes de produits */
.product-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.product-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
    border-radius: inherit;
}

.product-icon:hover {
    transform: rotate(5deg) scale(1.1);
}

/* Couleurs d'arrière-plan pour les icônes */
.bg-primary-light { background: rgba(102, 126, 234, 0.15) !important; }
.bg-secondary-light { background: rgba(108, 117, 125, 0.15) !important; }
.bg-success-light { background: rgba(40, 167, 69, 0.15) !important; }
.bg-danger-light { background: rgba(220, 53, 69, 0.15) !important; }
.bg-warning-light { background: rgba(255, 193, 7, 0.15) !important; }
.bg-info-light { background: rgba(23, 162, 184, 0.15) !important; }
.bg-dark-light { background: rgba(52, 58, 64, 0.15) !important; }

/* Badges de catégorie */
.category-badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.category-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Affichage des prix */
.price-display, .iron-price-modern, .base-price-modern {
    font-family: 'Courier New', monospace;
}

.price-range-modern {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(220, 53, 69, 0.1) 100%);
    padding: 0.5rem;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.iron-price-modern {
    background: rgba(52, 58, 64, 0.05);
    padding: 0.5rem;
    border-radius: 8px;
    border-left: 4px solid #343a40;
}

.iron-price-modern .unit-price, .iron-price-modern .ton-price {
    margin-bottom: 0.25rem;
}

.base-price-modern {
    background: rgba(102, 126, 234, 0.1);
    padding: 0.5rem;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

/* Affichage du stock */
.stock-display {
    min-width: 120px;
}

.stock-level-bar {
    width: 100%;
}

.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Boutons d'action */
.action-buttons .btn-group {
    border-radius: 25px;
    overflow: hidden;
}

.action-buttons .btn {
    border: none;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.action-buttons .btn:hover::before {
    left: 100%;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
}

/* État vide */
.empty-state {
    padding: 3rem 1rem;
}

.empty-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Pagination */
.pagination-wrapper .pagination {
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.pagination-wrapper .page-link {
    border: none;
    color: #667eea;
    transition: all 0.3s ease;
}

.pagination-wrapper .page-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
}

.pagination-wrapper .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

/* Responsive */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }

    .product-icon {
        width: 45px;
        height: 45px;
    }

    .action-buttons .btn-group {
        flex-direction: column;
    }

    .modern-table td {
        padding: 0.8rem 0.5rem;
    }
}

/* Animations d'entrée */
.product-row {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Tooltips personnalisés */
.tooltip-inner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    font-size: 0.8rem;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: #667eea;
}

/* ===== STYLES POUR LA MODALE DE FILTRAGE MODERNE ===== */

/* Modale principale */
.modern-filter-modal {
    border: none;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

/* Header avec gradient */
.filter-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 1.5rem 2rem;
    color: white;
}

.filter-icon-wrapper {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    backdrop-filter: blur(10px);
}

/* Corps de la modale */
.filter-body {
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

/* Sections de filtres */
.filter-section {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.filter-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.section-header {
    border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    padding-bottom: 0.5rem;
}

.section-title {
    color: #495057;
    font-weight: 600;
    margin: 0;
    font-size: 1rem;
}

/* Champs de formulaire modernes */
.modern-select, .modern-input {
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.8);
    backdrop-filter: blur(10px);
}

.modern-select:focus, .modern-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.form-floating > label {
    color: #6c757d;
    font-weight: 500;
}

/* Boutons de filtres rapides */
.quick-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.quick-filter-btn {
    border-radius: 25px;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.quick-filter-btn:hover::before {
    left: 100%;
}

.quick-filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Footer de la modale */
.filter-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    padding: 1.5rem 2rem;
}

.btn-cancel {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-reset {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-apply {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-apply:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Animations */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modern-filter-modal {
    animation: modalSlideIn 0.3s ease-out;
}

/* Responsive pour la modale */
@media (max-width: 768px) {
    .filter-body {
        padding: 1rem;
    }

    .filter-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .quick-filters {
        justify-content: center;
    }

    .quick-filter-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .filter-header {
        padding: 1rem;
    }

    .filter-footer {
        padding: 1rem;
    }

    .filter-footer .d-flex {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-footer .d-flex > div {
        justify-content: center;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Animation d'entrée pour les lignes de produits
    const productRows = document.querySelectorAll('.product-row');
    productRows.forEach((row, index) => {
        row.style.animationDelay = `${index * 0.1}s`;
    });

    // Gestion de la suppression de produits avec SweetAlert
    document.querySelectorAll('.delete-product').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            const productName = this.dataset.productName;

            Swal.fire({
                title: 'Êtes-vous sûr ?',
                html: `Vous êtes sur le point de supprimer le produit <strong>"${productName}"</strong>.<br>Cette action est irréversible.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="fas fa-trash me-2"></i>Oui, supprimer',
                cancelButtonText: '<i class="fas fa-times me-2"></i>Annuler',
                reverseButtons: true,
                customClass: {
                    popup: 'animated bounceIn',
                    confirmButton: 'btn btn-danger',
                    cancelButton: 'btn btn-secondary'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Créer et soumettre le formulaire de suppression
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/admin/products/${productId}`;

                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                    const methodField = document.createElement('input');
                    methodField.type = 'hidden';
                    methodField.name = '_method';
                    methodField.value = 'DELETE';

                    form.appendChild(csrfToken);
                    form.appendChild(methodField);
                    document.body.appendChild(form);

                    // Animation de suppression
                    const row = this.closest('.product-row');
                    row.style.transition = 'all 0.5s ease';
                    row.style.opacity = '0';
                    row.style.transform = 'translateX(-100%)';

                    setTimeout(() => {
                        form.submit();
                    }, 500);
                }
            });
        });
    });

    // Filtrage en temps réel
    const filterForm = document.getElementById('filterForm');
    const applyFilterBtn = document.getElementById('applyFilter');

    if (applyFilterBtn) {
        applyFilterBtn.addEventListener('click', function() {
            const formData = new FormData(filterForm);
            const category = formData.get('category');
            const status = formData.get('status');
            const minStock = formData.get('min_stock');
            const maxStock = formData.get('max_stock');

            filterProducts(category, status, minStock, maxStock);

            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('filterModal'));
            modal.hide();
        });
    }

    function filterProducts(category, status, minStock, maxStock) {
        const rows = document.querySelectorAll('.product-row');
        let visibleCount = 0;

        rows.forEach(row => {
            let show = true;

            // Filtrer par catégorie
            if (category && row.dataset.category !== category.toLowerCase()) {
                show = false;
            }

            // Filtrer par statut
            if (status) {
                const statusBadge = row.querySelector('.status-toggle .badge');
                const isActive = statusBadge.textContent.trim().includes('Actif');
                if ((status === 'active' && !isActive) || (status === 'inactive' && isActive)) {
                    show = false;
                }
            }

            // Filtrer par stock
            if (minStock || maxStock) {
                const stockText = row.querySelector('.stock-display .badge').textContent;
                const stockValue = parseInt(stockText.replace(/[^\d]/g, ''));

                if (minStock && stockValue < parseInt(minStock)) {
                    show = false;
                }
                if (maxStock && stockValue > parseInt(maxStock)) {
                    show = false;
                }
            }

            // Appliquer le filtre avec animation
            if (show) {
                row.style.display = '';
                row.style.animation = 'slideInUp 0.5s ease-out';
                visibleCount++;
            } else {
                row.style.animation = 'slideOutDown 0.3s ease-in';
                setTimeout(() => {
                    row.style.display = 'none';
                }, 300);
            }
        });

        // Afficher un message si aucun résultat
        const emptyState = document.querySelector('.empty-state');
        if (visibleCount === 0 && !emptyState) {
            const tableBody = document.querySelector('.modern-table tbody');
            const emptyRow = document.createElement('tr');
            emptyRow.className = 'empty-state-row';
            emptyRow.innerHTML = `
                <td colspan="6" class="text-center py-5">
                    <div class="empty-icon mb-3">
                        <i class="fas fa-search fa-3x text-muted opacity-50"></i>
                    </div>
                    <h5 class="text-muted">Aucun produit ne correspond aux critères</h5>
                    <p class="text-muted">Essayez de modifier vos filtres</p>
                </td>
            `;
            tableBody.appendChild(emptyRow);
        } else if (visibleCount > 0) {
            const emptyRow = document.querySelector('.empty-state-row');
            if (emptyRow) {
                emptyRow.remove();
            }
        }
    }

    // Animation au survol des cartes de statistiques
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // OPTIMISATION: Animation CSS pure au lieu de JavaScript pour performance
    // Effet de pulsation supprimé - utilise maintenant du CSS pur
    const dangerBadges = document.querySelectorAll('.badge.bg-danger');
    dangerBadges.forEach(badge => {
        badge.classList.add('pulse-animation'); // Animation CSS pure
    });

    // ===== GESTION DE LA NOUVELLE MODALE DE FILTRAGE =====

    // Boutons de filtres rapides
    document.querySelectorAll('.quick-filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const form = document.getElementById('filterForm');

            // Réinitialiser tous les champs
            form.reset();

            switch(filter) {
                case 'low-stock':
                    form.querySelector('[name="max_stock"]').value = '10';
                    break;
                case 'active-only':
                    form.querySelector('[name="status"]').value = 'active';
                    break;
                case 'cement-only':
                    form.querySelector('[name="category"]').value = 'Ciment';
                    break;
                case 'reset':
                    // Déjà réinitialisé ci-dessus
                    break;
            }

            // Effet visuel sur le bouton cliqué
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // Bouton de réinitialisation
    document.getElementById('resetFilter').addEventListener('click', function() {
        document.getElementById('filterForm').reset();

        // Effet visuel
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = '';
        }, 150);

        // Animation de réinitialisation
        const sections = document.querySelectorAll('.filter-section');
        sections.forEach((section, index) => {
            setTimeout(() => {
                section.style.animation = 'pulse 0.3s ease-in-out';
                setTimeout(() => {
                    section.style.animation = '';
                }, 300);
            }, index * 100);
        });
    });

    // Animation d'ouverture de la modale
    const filterModal = document.getElementById('filterModal');
    filterModal.addEventListener('show.bs.modal', function() {
        const sections = this.querySelectorAll('.filter-section');
        sections.forEach((section, index) => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(20px)';

            setTimeout(() => {
                section.style.transition = 'all 0.3s ease';
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });

    // Réinitialiser les animations à la fermeture
    filterModal.addEventListener('hidden.bs.modal', function() {
        const sections = this.querySelectorAll('.filter-section');
        sections.forEach(section => {
            section.style.opacity = '';
            section.style.transform = '';
            section.style.transition = '';
        });
    });
});

// Animation CSS pour le pulse
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    @keyframes slideOutDown {
        from {
            opacity: 1;
            transform: translateY(0);
        }
        to {
            opacity: 0;
            transform: translateY(30px);
        }
    }
`;
document.head.appendChild(style);
</script>
@endpush
