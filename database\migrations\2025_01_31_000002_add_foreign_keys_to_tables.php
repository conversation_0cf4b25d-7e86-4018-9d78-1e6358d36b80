<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->foreign('cement_order_id')->references('id')->on('cement_orders')->onDelete('cascade');
            $table->foreign('cement_order_detail_id')->references('id')->on('cement_order_details')->onDelete('cascade');
            $table->foreign('truck_id')->references('id')->on('trucks')->onDelete('cascade');
            $table->foreign('driver_id')->references('id')->on('drivers')->onDelete('cascade');
        });

        Schema::table('trip_assignments', function (Blueprint $table) {
            $table->foreign('cement_order_id')->references('id')->on('cement_orders')->onDelete('cascade');
            $table->foreign('cement_order_detail_id')->references('id')->on('cement_order_details')->onDelete('cascade');
            $table->foreign('trip_id')->references('id')->on('trips')->nullOnDelete();
            $table->foreign('truck_id')->references('id')->on('trucks')->onDelete('cascade');
            $table->foreign('driver_id')->references('id')->on('drivers')->onDelete('cascade');
        });

        Schema::table('cement_order_notifications', function (Blueprint $table) {
            $table->foreign('trip_assignment_id')->references('id')->on('trip_assignments')->onDelete('cascade');
        });

        Schema::table('cement_order_payments', function (Blueprint $table) {
            $table->foreign('trip_assignment_id')->references('id')->on('trip_assignments')->onDelete('cascade');
        });

        Schema::table('credit_sales', function (Blueprint $table) {
            $table->foreign('trip_assignment_id')->references('id')->on('trip_assignments')->onDelete('cascade');
        });

        // Ajouter la contrainte de clé étrangère pour expenses vers suppliers
        if (Schema::hasTable('expenses') && Schema::hasTable('suppliers')) {
            Schema::table('expenses', function (Blueprint $table) {
                $table->foreign('supplier_id')->references('id')->on('suppliers')->onDelete('set null');
            });
        }
    }

    public function down()
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->dropForeign(['cement_order_id']);
            $table->dropForeign(['cement_order_detail_id']);
            $table->dropForeign(['truck_id']);
            $table->dropForeign(['driver_id']);
        });

        Schema::table('trip_assignments', function (Blueprint $table) {
            $table->dropForeign(['cement_order_id']);
            $table->dropForeign(['cement_order_detail_id']);
            $table->dropForeign(['trip_id']);
            $table->dropForeign(['truck_id']);
            $table->dropForeign(['driver_id']);
        });

        Schema::table('cement_order_notifications', function (Blueprint $table) {
            $table->dropForeign(['trip_assignment_id']);
        });

        Schema::table('cement_order_payments', function (Blueprint $table) {
            $table->dropForeign(['trip_assignment_id']);
        });

        Schema::table('credit_sales', function (Blueprint $table) {
            $table->dropForeign(['trip_assignment_id']);
        });

        // Supprimer la contrainte de clé étrangère pour expenses
        if (Schema::hasTable('expenses')) {
            Schema::table('expenses', function (Blueprint $table) {
                $table->dropForeign(['supplier_id']);
            });
        }
    }
};
