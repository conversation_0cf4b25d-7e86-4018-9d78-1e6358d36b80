<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DatabaseOptimizationService
{
    /**
     * Cache des requêtes fréquentes
     */
    public function getCachedQuery(string $key, callable $callback, int $minutes = 60)
    {
        return Cache::remember($key, $minutes * 60, $callback);
    }

    /**
     * Optimise les requêtes avec pagination
     */
    public function optimizedPaginate($query, int $perPage = 15, array $columns = ['*'])
    {
        return $query->select($columns)->paginate($perPage);
    }

    /**
     * Charge les relations de manière optimisée
     */
    public function eagerLoadRelations($query, array $relations)
    {
        return $query->with($relations);
    }

    /**
     * Exécute une requête avec mise en cache automatique
     */
    public function cachedQuery(string $sql, array $bindings = [], int $minutes = 30)
    {
        $cacheKey = 'query_' . md5($sql . serialize($bindings));
        
        return Cache::remember($cacheKey, $minutes * 60, function () use ($sql, $bindings) {
            return DB::select($sql, $bindings);
        });
    }

    /**
     * Optimise les requêtes de statistiques
     */
    public function getOptimizedStats(string $table, array $conditions = [], int $cacheMinutes = 60)
    {
        $cacheKey = "stats_{$table}_" . md5(serialize($conditions));
        
        return Cache::remember($cacheKey, $cacheMinutes * 60, function () use ($table, $conditions) {
            $query = DB::table($table);
            
            foreach ($conditions as $field => $value) {
                $query->where($field, $value);
            }
            
            return [
                'total' => $query->count(),
                'today' => $query->whereDate('created_at', today())->count(),
                'this_month' => $query->whereMonth('created_at', now()->month)->count(),
                'this_year' => $query->whereYear('created_at', now()->year)->count(),
            ];
        });
    }

    /**
     * Nettoie le cache des requêtes
     */
    public function clearQueryCache(string $pattern = null)
    {
        if ($pattern) {
            $keys = Cache::getRedis()->keys("*{$pattern}*");
            if (!empty($keys)) {
                Cache::getRedis()->del($keys);
            }
        } else {
            Cache::flush();
        }
    }

    /**
     * Optimise les requêtes de recherche
     */
    public function optimizedSearch($query, string $searchTerm, array $searchFields)
    {
        if (empty($searchTerm)) {
            return $query;
        }

        return $query->where(function ($q) use ($searchTerm, $searchFields) {
            foreach ($searchFields as $field) {
                $q->orWhere($field, 'LIKE', "%{$searchTerm}%");
            }
        });
    }

    /**
     * Analyse les performances des requêtes
     */
    public function analyzeQueryPerformance()
    {
        if (config('app.env') !== 'production') {
            DB::listen(function ($query) {
                if ($query->time > 1000) { // Plus de 1 seconde
                    Log::warning('Slow query detected', [
                        'sql' => $query->sql,
                        'bindings' => $query->bindings,
                        'time' => $query->time . 'ms'
                    ]);
                }
            });
        }
    }

    /**
     * Optimise les requêtes de tableau de bord
     */
    public function getDashboardData(string $userRole, int $userId = null)
    {
        $cacheKey = "dashboard_{$userRole}_{$userId}";
        
        return Cache::remember($cacheKey, 30 * 60, function () use ($userRole, $userId) {
            $data = [];
            
            switch ($userRole) {
                case 'admin':
                    $data = $this->getAdminDashboardData();
                    break;
                case 'accountant':
                    $data = $this->getAccountantDashboardData();
                    break;
                case 'cement_manager':
                    $data = $this->getCementManagerDashboardData();
                    break;
                case 'customer':
                    $data = $this->getCustomerDashboardData($userId);
                    break;
                default:
                    $data = [];
            }
            
            return $data;
        });
    }

    /**
     * Données du tableau de bord admin
     */
    private function getAdminDashboardData()
    {
        return [
            'users_count' => DB::table('users')->count(),
            'orders_count' => DB::table('cement_orders')->count(),
            'revenue_total' => DB::table('cement_orders')->sum('total_amount'),
            'recent_orders' => DB::table('cement_orders')
                ->join('users', 'cement_orders.customer_id', '=', 'users.id')
                ->select('cement_orders.*', 'users.name as customer_name')
                ->orderBy('cement_orders.created_at', 'desc')
                ->limit(5)
                ->get(),
        ];
    }

    /**
     * Données du tableau de bord comptable
     */
    private function getAccountantDashboardData()
    {
        return [
            'pending_payments' => DB::table('cement_orders')
                ->where('payment_status', 'pending')
                ->count(),
            'monthly_revenue' => DB::table('cement_orders')
                ->whereMonth('created_at', now()->month)
                ->sum('total_amount'),
            'expenses_total' => DB::table('expenses')
                ->whereMonth('created_at', now()->month)
                ->sum('amount'),
        ];
    }

    /**
     * Données du tableau de bord gestionnaire ciment
     */
    private function getCementManagerDashboardData()
    {
        return [
            'active_orders' => DB::table('cement_orders')
                ->whereIn('status', ['pending', 'processing'])
                ->count(),
            'deliveries_today' => DB::table('cement_orders')
                ->whereDate('delivery_date', today())
                ->count(),
            'stock_alerts' => DB::table('products')
                ->where('stock_quantity', '<', DB::raw('minimum_stock'))
                ->count(),
        ];
    }

    /**
     * Données du tableau de bord client
     */
    private function getCustomerDashboardData($userId)
    {
        return [
            'my_orders' => DB::table('cement_orders')
                ->where('customer_id', $userId)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(),
            'total_orders' => DB::table('cement_orders')
                ->where('customer_id', $userId)
                ->count(),
            'pending_orders' => DB::table('cement_orders')
                ->where('customer_id', $userId)
                ->whereIn('status', ['pending', 'processing'])
                ->count(),
        ];
    }

    /**
     * Invalide le cache du tableau de bord
     */
    public function invalidateDashboardCache(string $userRole = null, int $userId = null)
    {
        if ($userRole && $userId) {
            Cache::forget("dashboard_{$userRole}_{$userId}");
        } else {
            // Invalider tous les caches de tableau de bord
            $this->clearQueryCache('dashboard_');
        }
    }
}
