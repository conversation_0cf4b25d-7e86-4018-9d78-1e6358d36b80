1754260556a:5:{s:11:"topProducts";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{i:0;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:6;s:4:"name";s:17:"CIMCO-CPJ 45-KARA";s:14:"stock_quantity";i:30;s:11:"category_id";i:1;}s:11:" * original";a:4:{s:2:"id";i:6;s:4:"name";s:17:"CIMCO-CPJ 45-KARA";s:14:"stock_quantity";i:30;s:11:"category_id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";O:19:"App\Models\Category":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:2:"id";i:1;s:4:"name";s:6:"Ciment";}s:11:" * original";a:2:{s:2:"id";i:1;s:4:"name";s:6:"Ciment";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:9:"is_active";i:4;s:4:"type";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:1;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:1;s:4:"name";s:20:"CIMTOGO-CPJ 45-LOMÉ";s:14:"stock_quantity";i:0;s:11:"category_id";i:1;}s:11:" * original";a:4:{s:2:"id";i:1;s:4:"name";s:20:"CIMTOGO-CPJ 45-LOMÉ";s:14:"stock_quantity";i:0;s:11:"category_id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:40;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:2;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:2;s:4:"name";s:24:"CIMTOGO-CPJ 45-INTERIEUR";s:14:"stock_quantity";i:0;s:11:"category_id";i:1;}s:11:" * original";a:4:{s:2:"id";i:2;s:4:"name";s:24:"CIMTOGO-CPJ 45-INTERIEUR";s:14:"stock_quantity";i:0;s:11:"category_id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:40;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:3;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:3;s:4:"name";s:18:"CIMCO-CPJ 45-LOMÉ";s:14:"stock_quantity";i:0;s:11:"category_id";i:1;}s:11:" * original";a:4:{s:2:"id";i:3;s:4:"name";s:18:"CIMCO-CPJ 45-LOMÉ";s:14:"stock_quantity";i:0;s:11:"category_id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:40;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:4;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:4;s:4:"name";s:19:"CIMCO-CPJ 45-SAVANE";s:14:"stock_quantity";i:0;s:11:"category_id";i:1;}s:11:" * original";a:4:{s:2:"id";i:4;s:4:"name";s:19:"CIMCO-CPJ 45-SAVANE";s:14:"stock_quantity";i:0;s:11:"category_id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:40;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}s:15:"pendingSupplies";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:17:"App\Models\Supply":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"supplies";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:2;s:9:"reference";s:12:"Bond N000256";s:11:"supplier_id";i:1;s:12:"total_amount";s:10:"7370000.00";s:6:"status";s:7:"pending";s:10:"created_at";s:19:"2025-08-03 16:45:13";}s:11:" * original";a:6:{s:2:"id";i:2;s:9:"reference";s:12:"Bond N000256";s:11:"supplier_id";i:1;s:12:"total_amount";s:10:"7370000.00";s:6:"status";s:7:"pending";s:10:"created_at";s:19:"2025-08-03 16:45:13";}s:10:" * changes";a:0:{}s:8:" * casts";a:7:{s:4:"date";s:4:"date";s:22:"expected_delivery_date";s:4:"date";s:12:"validated_at";s:8:"datetime";s:13:"total_tonnage";s:9:"decimal:2";s:12:"total_amount";s:9:"decimal:2";s:15:"total_remaining";s:9:"decimal:2";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"supplier";O:19:"App\Models\Supplier":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"suppliers";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:2:"id";i:1;s:4:"name";s:10:"CIMTOGO SA";}s:11:" * original";a:2:{s:2:"id";i:1;s:4:"name";s:10:"CIMTOGO SA";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:4:"name";i:1;s:14:"contact_person";i:2;s:5:"phone";i:3;s:5:"email";i:4;s:7:"address";i:5;s:11:"description";i:6;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:18:{i:0;s:9:"reference";i:1;s:11:"supplier_id";i:2;s:11:"category_id";i:3;s:10:"product_id";i:4;s:9:"region_id";i:5;s:22:"cement_order_reference";i:6;s:4:"date";i:7;s:22:"expected_delivery_date";i:8;s:12:"invoice_file";i:9;s:5:"notes";i:10;s:13:"total_tonnage";i:11;s:12:"total_amount";i:12;s:15:"total_remaining";i:13;s:6:"status";i:14;s:16:"rejection_reason";i:15;s:10:"created_by";i:16;s:12:"validator_id";i:17;s:12:"validated_at";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}s:11:"latestUsers";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{i:0;O:15:"App\Models\User":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:6;s:4:"name";s:13:"Test Customer";s:5:"email";s:17:"<EMAIL>";s:10:"created_at";s:19:"2025-08-02 20:14:25";}s:11:" * original";a:4:{s:2:"id";i:6;s:4:"name";s:13:"Test Customer";s:5:"email";s:17:"<EMAIL>";s:10:"created_at";s:19:"2025-08-02 20:14:25";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:17:"email_verified_at";s:8:"datetime";s:8:"password";s:6:"hashed";s:9:"is_active";s:7:"boolean";s:14:"license_expiry";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:8:"password";i:5;s:9:"is_active";i:6;s:14:"license_number";i:7;s:14:"license_expiry";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:1;O:15:"App\Models\User":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:5;s:4:"name";s:14:"CEMENT MANAGER";s:5:"email";s:24:"<EMAIL>";s:10:"created_at";s:19:"2025-08-02 20:03:03";}s:11:" * original";a:4:{s:2:"id";i:5;s:4:"name";s:14:"CEMENT MANAGER";s:5:"email";s:24:"<EMAIL>";s:10:"created_at";s:19:"2025-08-02 20:03:03";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:17:"email_verified_at";s:8:"datetime";s:8:"password";s:6:"hashed";s:9:"is_active";s:7:"boolean";s:14:"license_expiry";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:8:"password";i:5;s:9:"is_active";i:6;s:14:"license_number";i:7;s:14:"license_expiry";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:2;O:15:"App\Models\User":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:1;s:4:"name";s:5:"Admin";s:5:"email";s:16:"<EMAIL>";s:10:"created_at";s:19:"2025-08-02 19:47:31";}s:11:" * original";a:4:{s:2:"id";i:1;s:4:"name";s:5:"Admin";s:5:"email";s:16:"<EMAIL>";s:10:"created_at";s:19:"2025-08-02 19:47:31";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:17:"email_verified_at";s:8:"datetime";s:8:"password";s:6:"hashed";s:9:"is_active";s:7:"boolean";s:14:"license_expiry";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:8:"password";i:5;s:9:"is_active";i:6;s:14:"license_number";i:7;s:14:"license_expiry";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:3;O:15:"App\Models\User":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:2;s:4:"name";s:9:"Comptable";s:5:"email";s:21:"<EMAIL>";s:10:"created_at";s:19:"2025-08-02 19:47:31";}s:11:" * original";a:4:{s:2:"id";i:2;s:4:"name";s:9:"Comptable";s:5:"email";s:21:"<EMAIL>";s:10:"created_at";s:19:"2025-08-02 19:47:31";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:17:"email_verified_at";s:8:"datetime";s:8:"password";s:6:"hashed";s:9:"is_active";s:7:"boolean";s:14:"license_expiry";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:8:"password";i:5;s:9:"is_active";i:6;s:14:"license_number";i:7;s:14:"license_expiry";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}i:4;O:15:"App\Models\User":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:3;s:4:"name";s:8:"Caissier";s:5:"email";s:18:"<EMAIL>";s:10:"created_at";s:19:"2025-08-02 19:47:31";}s:11:" * original";a:4:{s:2:"id";i:3;s:4:"name";s:8:"Caissier";s:5:"email";s:18:"<EMAIL>";s:10:"created_at";s:19:"2025-08-02 19:47:31";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:17:"email_verified_at";s:8:"datetime";s:8:"password";s:6:"hashed";s:9:"is_active";s:7:"boolean";s:14:"license_expiry";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:8:"password";i:5;s:9:"is_active";i:6;s:14:"license_number";i:7;s:14:"license_expiry";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:14:" * accessToken";N;}}s:28:" * escapeWhenCastingToString";b:0;}s:14:"latestProducts";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{i:0;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:6;s:4:"name";s:17:"CIMCO-CPJ 45-KARA";s:11:"category_id";i:1;s:14:"stock_quantity";i:30;s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-06-05 14:09:10";}s:11:" * original";a:6:{s:2:"id";i:6;s:4:"name";s:17:"CIMCO-CPJ 45-KARA";s:11:"category_id";i:1;s:14:"stock_quantity";i:30;s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-06-05 14:09:10";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";O:19:"App\Models\Category":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:2:"id";i:1;s:4:"name";s:6:"Ciment";}s:11:" * original";a:2:{s:2:"id";i:1;s:4:"name";s:6:"Ciment";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:9:"is_active";i:4;s:4:"type";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:1;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:5;s:4:"name";s:21:"CIMCO-CPJ 45-CENTRALE";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-06-05 14:07:10";}s:11:" * original";a:6:{s:2:"id";i:5;s:4:"name";s:21:"CIMCO-CPJ 45-CENTRALE";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-06-05 14:07:10";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:767;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:2;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:4;s:4:"name";s:19:"CIMCO-CPJ 45-SAVANE";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-05-06 23:09:28";}s:11:" * original";a:6:{s:2:"id";i:4;s:4:"name";s:19:"CIMCO-CPJ 45-SAVANE";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-05-06 23:09:28";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:767;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:3;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:3;s:4:"name";s:18:"CIMCO-CPJ 45-LOMÉ";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-05-06 23:03:23";}s:11:" * original";a:6:{s:2:"id";i:3;s:4:"name";s:18:"CIMCO-CPJ 45-LOMÉ";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-05-06 23:03:23";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:767;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:4;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:2;s:4:"name";s:24:"CIMTOGO-CPJ 45-INTERIEUR";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-04-04 20:01:48";}s:11:" * original";a:6:{s:2:"id";i:2;s:4:"name";s:24:"CIMTOGO-CPJ 45-INTERIEUR";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";s:10:"created_at";s:19:"2025-04-04 20:01:48";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:767;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}s:20:"pendingSuppliesCount";i:1;}