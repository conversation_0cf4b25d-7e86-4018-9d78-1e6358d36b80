/**
 * Moniteur de performance pour le tableau de bord comptable
 */

class DashboardPerformanceMonitor {
    constructor() {
        this.startTime = null;
        this.loadTime = null;
        this.metrics = {};
        this.init();
    }

    init() {
        this.startTime = performance.now();
        this.monitorPageLoad();
        this.monitorAjaxRequests();
        this.addPerformanceIndicators();
        
        // Démarrer le monitoring dès que possible
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
        } else {
            this.onDOMReady();
        }
    }

    onDOMReady() {
        this.loadTime = performance.now() - this.startTime;
        this.displayPerformanceMetrics();
        this.addCacheControls();
        
        console.log('📊 Dashboard Performance Monitor initialisé');
        console.log(`⏱️ Temps de chargement DOM: ${this.loadTime.toFixed(2)}ms`);
    }

    monitorPageLoad() {
        window.addEventListener('load', () => {
            const totalLoadTime = performance.now() - this.startTime;
            this.metrics.totalLoadTime = totalLoadTime;
            
            // Analyser les ressources chargées
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                this.metrics.serverResponseTime = navigation.responseEnd - navigation.requestStart;
                this.metrics.domProcessingTime = navigation.domContentLoadedEventEnd - navigation.domLoading;
                this.metrics.resourceLoadTime = navigation.loadEventEnd - navigation.domContentLoadedEventEnd;
            }
            
            this.evaluatePerformance();
        });
    }

    monitorAjaxRequests() {
        // Intercepter les requêtes fetch
        const originalFetch = window.fetch;
        window.fetch = (...args) => {
            const startTime = performance.now();
            return originalFetch(...args).then(response => {
                const endTime = performance.now();
                this.logAjaxRequest(args[0], endTime - startTime);
                return response;
            });
        };

        // Intercepter les requêtes XMLHttpRequest
        const originalXHR = window.XMLHttpRequest;
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const startTime = performance.now();
            
            xhr.addEventListener('loadend', () => {
                const endTime = performance.now();
                window.dashboardMonitor.logAjaxRequest(xhr.responseURL, endTime - startTime);
            });
            
            return xhr;
        };
    }

    logAjaxRequest(url, duration) {
        if (!this.metrics.ajaxRequests) {
            this.metrics.ajaxRequests = [];
        }
        
        this.metrics.ajaxRequests.push({
            url: url,
            duration: duration,
            timestamp: new Date().toISOString()
        });
        
        console.log(`🌐 Requête AJAX: ${url} - ${duration.toFixed(2)}ms`);
    }

    addPerformanceIndicators() {
        // Créer un indicateur de performance dans l'interface
        const indicator = document.createElement('div');
        indicator.id = 'performance-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            min-width: 200px;
            display: none;
        `;
        
        document.body.appendChild(indicator);
        
        // Ajouter un bouton pour afficher/masquer l'indicateur
        const toggleButton = document.createElement('button');
        toggleButton.innerHTML = '📊';
        toggleButton.title = 'Afficher les métriques de performance';
        toggleButton.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            z-index: 10001;
            font-size: 16px;
        `;
        
        toggleButton.addEventListener('click', () => {
            const isVisible = indicator.style.display !== 'none';
            indicator.style.display = isVisible ? 'none' : 'block';
            toggleButton.style.right = isVisible ? '10px' : '220px';
        });
        
        document.body.appendChild(toggleButton);
    }

    displayPerformanceMetrics() {
        const indicator = document.getElementById('performance-indicator');
        if (!indicator) return;
        
        const metrics = this.getFormattedMetrics();
        indicator.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px;">📊 Performance Dashboard</div>
            ${metrics.map(metric => `<div>${metric}</div>`).join('')}
            <div style="margin-top: 10px;">
                <button onclick="window.dashboardMonitor.clearCache()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px;">Vider Cache</button>
                <button onclick="window.dashboardMonitor.refreshMetrics()" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Actualiser</button>
            </div>
        `;
    }

    getFormattedMetrics() {
        const metrics = [];
        
        if (this.loadTime) {
            metrics.push(`⏱️ DOM: ${this.loadTime.toFixed(0)}ms`);
        }
        
        if (this.metrics.totalLoadTime) {
            metrics.push(`🔄 Total: ${this.metrics.totalLoadTime.toFixed(0)}ms`);
        }
        
        if (this.metrics.serverResponseTime) {
            metrics.push(`🖥️ Serveur: ${this.metrics.serverResponseTime.toFixed(0)}ms`);
        }
        
        if (this.metrics.ajaxRequests && this.metrics.ajaxRequests.length > 0) {
            const avgAjax = this.metrics.ajaxRequests.reduce((sum, req) => sum + req.duration, 0) / this.metrics.ajaxRequests.length;
            metrics.push(`🌐 AJAX moy: ${avgAjax.toFixed(0)}ms`);
        }
        
        // Statut du cache
        const cacheStatus = this.getCacheStatus();
        metrics.push(`💾 Cache: ${cacheStatus}`);
        
        return metrics;
    }

    getCacheStatus() {
        // Vérifier si les données semblent venir du cache
        if (this.metrics.serverResponseTime && this.metrics.serverResponseTime < 100) {
            return '✅ Actif';
        } else if (this.metrics.serverResponseTime && this.metrics.serverResponseTime > 1000) {
            return '❌ Lent';
        } else {
            return '⚠️ Moyen';
        }
    }

    evaluatePerformance() {
        const totalTime = this.metrics.totalLoadTime;
        let status = '';
        let color = '';
        
        if (totalTime < 1000) {
            status = '🟢 Excellent';
            color = '#28a745';
        } else if (totalTime < 3000) {
            status = '🟡 Bon';
            color = '#ffc107';
        } else if (totalTime < 5000) {
            status = '🟠 Moyen';
            color = '#fd7e14';
        } else {
            status = '🔴 Lent';
            color = '#dc3545';
        }
        
        console.log(`📊 Performance globale: ${status} (${totalTime.toFixed(0)}ms)`);
        
        // Afficher une notification si les performances sont mauvaises
        if (totalTime > 5000) {
            this.showPerformanceWarning();
        }
    }

    showPerformanceWarning() {
        const warning = document.createElement('div');
        warning.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 10002;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        `;
        
        warning.innerHTML = `
            <h3>⚠️ Performance Dégradée</h3>
            <p>Le tableau de bord met plus de 5 secondes à charger.</p>
            <p>Recommandations:</p>
            <ul style="text-align: left; margin: 10px 0;">
                <li>Vider le cache du navigateur</li>
                <li>Vérifier la connexion réseau</li>
                <li>Contacter l'administrateur système</li>
            </ul>
            <button onclick="this.parentElement.remove(); window.dashboardMonitor.clearCache();" 
                    style="background: white; color: #dc3545; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px;">
                Vider le Cache
            </button>
            <button onclick="this.parentElement.remove();" 
                    style="background: rgba(255,255,255,0.2); color: white; border: 1px solid white; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px;">
                Fermer
            </button>
        `;
        
        document.body.appendChild(warning);
        
        // Supprimer automatiquement après 10 secondes
        setTimeout(() => {
            if (warning.parentElement) {
                warning.remove();
            }
        }, 10000);
    }

    addCacheControls() {
        // Ajouter des raccourcis clavier pour le cache
        document.addEventListener('keydown', (e) => {
            // Ctrl+Shift+C pour vider le cache
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                this.clearCache();
            }
            
            // Ctrl+Shift+R pour actualiser les métriques
            if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                e.preventDefault();
                this.refreshMetrics();
            }
        });
    }

    clearCache() {
        console.log('🗑️ Vidage du cache...');
        
        // Vider le cache du navigateur (si possible)
        if ('caches' in window) {
            caches.keys().then(names => {
                names.forEach(name => {
                    caches.delete(name);
                });
            });
        }
        
        // Vider le localStorage
        localStorage.clear();
        
        // Vider le sessionStorage
        sessionStorage.clear();
        
        // Faire une requête pour vider le cache côté serveur
        fetch('/accountant/clear-cache', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                'Content-Type': 'application/json'
            }
        }).then(response => {
            if (response.ok) {
                console.log('✅ Cache serveur vidé');
                // Recharger la page après un court délai
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                console.log('❌ Erreur lors du vidage du cache serveur');
            }
        }).catch(error => {
            console.log('❌ Erreur réseau lors du vidage du cache:', error);
            // Recharger quand même la page
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });
        
        alert('Cache vidé ! La page va se recharger...');
    }

    refreshMetrics() {
        this.metrics = {};
        this.startTime = performance.now();
        this.displayPerformanceMetrics();
        console.log('🔄 Métriques actualisées');
    }
}

// Initialiser le moniteur de performance
window.dashboardMonitor = new DashboardPerformanceMonitor();

// Fonctions utilitaires globales
window.clearDashboardCache = () => window.dashboardMonitor.clearCache();
window.showPerformanceMetrics = () => {
    const indicator = document.getElementById('performance-indicator');
    if (indicator) {
        indicator.style.display = indicator.style.display === 'none' ? 'block' : 'none';
    }
};

console.log('🚀 Dashboard Performance Monitor chargé');
console.log('💡 Utilisez Ctrl+Shift+C pour vider le cache');
console.log('💡 Utilisez Ctrl+Shift+R pour actualiser les métriques');
console.log('💡 Cliquez sur 📊 pour voir les métriques en temps réel');
