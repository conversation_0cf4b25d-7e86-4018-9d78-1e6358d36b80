<?php

/**
 * Script pour corriger les problèmes de paiement du comptable
 * Nettoie les caches et vérifie la configuration
 */

echo "🔧 Correction des problèmes de paiement du comptable\n";
echo "====================================================\n\n";

// Nettoyer tous les caches
echo "🧹 Nettoyage des caches...\n";
$commands = [
    'php artisan cache:clear',
    'php artisan config:clear',
    'php artisan route:clear',
    'php artisan view:clear',
    'php artisan optimize:clear'
];

foreach ($commands as $command) {
    echo "   Exécution: $command\n";
    $output = shell_exec($command . ' 2>&1');
    if (strpos($output, 'cleared') !== false || strpos($output, 'Configuration cache cleared') !== false) {
        echo "   ✅ Succès\n";
    } else {
        echo "   ⚠️ Résultat: " . trim($output) . "\n";
    }
}

echo "\n📋 Vérification des fichiers modifiés...\n";

// Vérifier les fichiers critiques
$files = [
    'app/Http/Middleware/SecurityHeaders.php' => 'Headers de sécurité',
    'resources/views/layouts/accountant.blade.php' => 'Layout comptable',
    'public/js/diagnostic.js' => 'Script de diagnostic',
    'public/test-assets.html' => 'Page de test'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ $description: Présent\n";
    } else {
        echo "   ❌ $description: Manquant\n";
    }
}

echo "\n🔍 Vérification des routes...\n";
$routeOutput = shell_exec('php artisan route:list --name=demo.accountant.payment 2>&1');
if (strpos($routeOutput, 'demo.accountant.payment') !== false) {
    echo "   ✅ Route de démonstration: Configurée\n";
} else {
    echo "   ❌ Route de démonstration: Manquante\n";
}

echo "\n🌐 URLs de test disponibles:\n";
echo "   📄 Page de test des assets: http://127.0.0.1:8000/test-assets.html\n";
echo "   💳 Page de paiement démo: http://127.0.0.1:8000/demo/accountant/payment\n";
echo "   🔧 Test serveur: http://127.0.0.1:8000/test-server\n";

echo "\n💡 Instructions pour tester:\n";
echo "   1. Ouvrez http://127.0.0.1:8000/test-assets.html dans votre navigateur\n";
echo "   2. Cliquez sur les boutons de test pour vérifier chaque composant\n";
echo "   3. Vérifiez la console du navigateur (F12) pour les erreurs\n";
echo "   4. Testez les cartes de paiement en cliquant dessus\n";

echo "\n🚨 Si des problèmes persistent:\n";
echo "   - Vérifiez que le serveur Laravel tourne (php artisan serve)\n";
echo "   - Désactivez temporairement les extensions de navigateur\n";
echo "   - Testez en mode navigation privée\n";
echo "   - Vérifiez la connexion internet pour les CDN\n";

echo "\n✅ Script terminé - " . date('Y-m-d H:i:s') . "\n";
echo "====================================================\n";
