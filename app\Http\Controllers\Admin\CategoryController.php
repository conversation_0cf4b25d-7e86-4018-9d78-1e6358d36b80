<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    public function index()
    {
        $categories = Category::withCount('products')->latest()->paginate(10);
        return view('admin.categories.index', compact('categories'));
    }

    public function create()
    {
        return view('admin.categories.create');
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:categories',
                'description' => 'nullable|string|max:1000',
            ], [
                'name.required' => 'Le nom de la catégorie est obligatoire',
                'name.unique' => 'Cette catégorie existe déjà',
                'name.max' => 'Le nom ne doit pas dépasser 255 caractères',
                'description.max' => 'La description ne doit pas dépasser 1000 caractères',
            ]);

            // Générer le slug à partir du nom
            $validated['slug'] = Str::slug($validated['name']);
            $validated['is_active'] = true;

            Category::create($validated);

            $notification = array(
                'message' => 'Catégorie créée avec succès',
                'alert-type' => 'success'
            );

            return redirect()->route('admin.categories.index')->with($notification);

        } catch (\Exception $e) {
            // Erreur

            $notification = array(
                'message' => 'Une erreur est survenue lors de la création de la catégorie',
                'alert-type' => 'error'
            );

            return back()->withInput()->with($notification);
        }
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit(Category $category)
    {
        return view('admin.categories.edit', compact('category'));
    }

    /**
     * Update the specified category in storage.
     */
    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:categories,name,' . $category->id,
            'description' => 'nullable|string|max:1000',
        ]);

        $category->update($validated);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Catégorie modifiée avec succès.');
    }

    public function destroy(Category $category)
    {
        try {
            $category->delete();
            $notification = array(
                'message' => 'Catégorie supprimée avec succès',
                'alert-type' => 'success'
            );
            return redirect()->route('admin.categories.index')->with($notification);
        } catch (\Exception $e) {
            $notification = array(
                'message' => 'Erreur lors de la suppression de la catégorie',
                'alert-type' => 'error'
            );
            return redirect()->route('admin.categories.index')->with($notification);
        }
    }
}
