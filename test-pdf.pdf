<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="s0ax80KZJypnryQR5UG5fzDKrS71a6vYHCYhZHyE">
    
    <title>GRADIS - Connexion</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom styles -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .auth-wrapper {
            width: 100%;
            max-width: 400px;
            margin: auto;
        }

        .auth-card {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }

        .form-control {
            border-radius: 8px;
            padding: 0.75rem 1rem;
            border: 1px solid #e2e8f0;
        }

        .form-control:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
        }

        .btn-primary {
            background-color: #4f46e5;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background-color: #4338ca;
            transform: translateY(-1px);
        }

        .text-primary {
            color: #4f46e5 !important;
            text-decoration: none;
        }

        .text-primary:hover {
            color: #4338ca !important;
            text-decoration: underline;
        }

        .alert {
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #dcfce7;
            border: 1px solid #86efac;
            color: #166534;
        }

        .alert-danger {
            background-color: #fee2e2;
            border: 1px solid #fca5a5;
            color: #991b1b;
        }

        .invalid-feedback {
            color: #dc2626;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .form-check-input:checked {
            background-color: #4f46e5;
            border-color: #4f46e5;
        }

        /* Styles pour le bouton toggle password */
        #togglePassword {
            color: #6b7280;
            padding: 0;
            margin: 0;
            width: auto;
            height: auto;
            transition: color 0.2s ease;
        }

        #togglePassword:hover {
            color: #4f46e5;
        }

        #togglePassword:focus {
            box-shadow: none;
            outline: none;
        }

        .position-relative input[type="password"],
        .position-relative input[type="text"] {
            padding-right: 3rem;
        }
    </style>
</head>
<body>
    <div class="auth-wrapper">
        <div class="auth-card">
            <div class="logo-container text-center">
    <img src="http://127.0.0.1:8000/assets/images/logo_gradis.png" alt="GRADIS Logo" class="mb-4 mx-auto" style="max-width: 150px;">
</div>

<h2 class="text-center text-2xl font-bold text-gray-900 mb-8">
    Bienvenue sur GRADIS
</h2>



<form method="POST" action="http://127.0.0.1:8000/login" class="space-y-6">
    <input type="hidden" name="_token" value="s0ax80KZJypnryQR5UG5fzDKrS71a6vYHCYhZHyE" autocomplete="off">
    <div class="form-group">
        <label for="email" class="form-label">Adresse email</label>
        <input id="email" type="email" name="email" value="" 
               class="form-control " 
               required autofocus>
            </div>

    <div class="form-group">
        <label for="password" class="form-label">Mot de passe</label>
        <div class="position-relative">
            <input id="password" type="password" name="password"
                   class="form-control "
                   required>
            <button type="button" class="btn btn-link position-absolute top-50 end-0 translate-middle-y pe-3"
                    id="togglePassword" style="border: none; background: none; z-index: 10;">
                <i class="fas fa-eye" id="eyeIcon"></i>
            </button>
        </div>
            </div>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="form-check">
            <input type="checkbox" name="remember" id="remember" class="form-check-input">
            <label class="form-check-label" for="remember">Se souvenir de moi</label>
        </div>

            </div>

    <button type="submit" class="btn btn-primary w-100">
        Se connecter
    </button>
</form>
            <footer class="footer mt-auto py-3">
                <div class="container text-center">
                    <span class="text-muted">© 2025 GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
                </div>
            </footer>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Script pour le toggle du mot de passe -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const togglePassword = document.getElementById('togglePassword');
            const passwordField = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');

            if (togglePassword && passwordField && eyeIcon) {
                togglePassword.addEventListener('click', function() {
                    // Toggle le type du champ
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);

                    // Toggle l'icône
                    if (type === 'password') {
                        eyeIcon.classList.remove('fa-eye-slash');
                        eyeIcon.classList.add('fa-eye');
                    } else {
                        eyeIcon.classList.remove('fa-eye');
                        eyeIcon.classList.add('fa-eye-slash');
                    }
                });
            }
        });
    </script>
</body>
</html>
