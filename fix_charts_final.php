<?php

echo "🔧 CORRECTION FINALE DES GRAPHIQUES\n";
echo "===================================\n\n";

$viewPath = 'resources/views/admin/dashboard.blade.php';
$content = file_get_contents($viewPath);

// Backup
$backupPath = $viewPath . '.final-fix-backup.' . date('Y-m-d-H-i-s');
copy($viewPath, $backupPath);
echo "📋 Backup créé: $backupPath\n";

// Supprimer complètement la section scripts et la recréer proprement
$scriptStart = strpos($content, '@push(\'scripts\')');
$scriptEnd = strrpos($content, '@endpush'); // Utiliser strrpos pour le dernier @endpush

if ($scriptStart !== false && $scriptEnd !== false) {
    // Extraire les parties avant et après
    $beforeScripts = substr($content, 0, $scriptStart);
    $afterScripts = substr($content, $scriptEnd + strlen('@endpush'));
    
    // Nouveau script ultra-simple et fonctionnel
    $newScript = '@push(\'scripts\')
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>
<script>
console.log("🚀 GRADIS Charts - Début initialisation");

document.addEventListener("DOMContentLoaded", function() {
    console.log("📊 DOM chargé, vérification ApexCharts...");
    
    // Vérifier ApexCharts
    if (typeof ApexCharts === "undefined") {
        console.error("❌ ApexCharts non chargé");
        return;
    }
    
    console.log("✅ ApexCharts disponible:", ApexCharts.version);
    
    // Données de test simples
    const months = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"];
    const salesData = [15000, 22000, 18000, 25000, 30000, 28000];
    const cementData = [100, 150, 120, 180, 200, 180];
    const categoryData = [45, 32, 28, 23];
    const categoryLabels = ["Ciment", "Fer", "Sable", "Gravier"];
    
    // Fonction de création sécurisée
    function createChart(containerId, config, chartName) {
        console.log(`📈 Tentative création ${chartName} dans ${containerId}`);
        
        const container = document.querySelector(containerId);
        if (!container) {
            console.error(`❌ Conteneur ${containerId} non trouvé pour ${chartName}`);
            return;
        }
        
        console.log(`✅ Conteneur ${containerId} trouvé`);
        
        try {
            const chart = new ApexCharts(container, config);
            chart.render();
            console.log(`🎉 Graphique ${chartName} créé avec succès`);
        } catch (error) {
            console.error(`❌ Erreur création ${chartName}:`, error);
        }
    }
    
    // Attendre un peu puis créer les graphiques
    setTimeout(function() {
        console.log("🎯 Début création des graphiques...");
        
        // 1. Graphique des revenus (aires)
        createChart("#revenueChart", {
            series: [{
                name: "Revenus (€)",
                data: salesData
            }],
            chart: {
                type: "area",
                height: 350,
                toolbar: { show: false },
                animations: { enabled: false }
            },
            xaxis: {
                categories: months
            },
            colors: ["#007bff"],
            fill: {
                type: "gradient",
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.3
                }
            },
            dataLabels: { enabled: false },
            stroke: { curve: "smooth" }
        }, "Revenus");
        
    }, 500);
    
    setTimeout(function() {
        // 2. Graphique des ressources (barres)
        createChart("#resourcesChart", {
            series: [{
                name: "Ressources",
                data: salesData
            }],
            chart: {
                type: "bar",
                height: 350,
                toolbar: { show: false },
                animations: { enabled: false }
            },
            xaxis: {
                categories: months
            },
            colors: ["#28a745"],
            dataLabels: { enabled: false }
        }, "Ressources");
        
    }, 700);
    
    setTimeout(function() {
        // 3. Graphique des catégories (donut)
        createChart("#categoryRevenueChart", {
            series: categoryData,
            chart: {
                type: "donut",
                height: 300,
                animations: { enabled: false }
            },
            labels: categoryLabels,
            colors: ["#007bff", "#28a745", "#ffc107", "#dc3545"],
            legend: {
                position: "bottom"
            },
            dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return Math.round(val) + "%";
                }
            }
        }, "Catégories");
        
    }, 900);
    
    setTimeout(function() {
        // 4. Graphique du ciment (barres)
        createChart("#cementOrdersChart", {
            series: [{
                name: "Tonnage (T)",
                data: cementData
            }],
            chart: {
                type: "bar",
                height: 300,
                toolbar: { show: false },
                animations: { enabled: false }
            },
            xaxis: {
                categories: months
            },
            colors: ["#17a2b8"],
            dataLabels: { enabled: false },
            yaxis: {
                title: {
                    text: "Tonnage (T)"
                }
            }
        }, "Ciment");
        
    }, 1100);
    
    console.log("⏰ Tous les graphiques programmés pour création");
});

console.log("✅ Script graphiques chargé");
</script>
@endpush';
    
    // Reconstruire le contenu
    $newContent = $beforeScripts . $newScript;
    
    // Vérifier qu'il n'y a pas d'autres @endpush qui traînent
    $afterScripts = trim($afterScripts);
    if (!empty($afterScripts)) {
        $newContent .= "\n" . $afterScripts;
    }
    
    // Sauvegarder
    if (file_put_contents($viewPath, $newContent)) {
        echo "✅ Script JavaScript ultra-simple installé\n";
        echo "🔧 @endpush en double corrigé\n";
        echo "📊 Graphiques programmés avec délais échelonnés\n";
    } else {
        echo "❌ Erreur lors de la sauvegarde\n";
        exit(1);
    }
    
} else {
    echo "❌ Section scripts non trouvée\n";
    exit(1);
}

echo "\n🎯 CORRECTIONS APPLIQUÉES:\n";
echo "==========================\n";
echo "✅ Script JavaScript complètement refait\n";
echo "✅ @endpush en double supprimé\n";
echo "✅ Délais échelonnés (500ms, 700ms, 900ms, 1100ms)\n";
echo "✅ Logs de debug détaillés\n";
echo "✅ Gestion d'erreur robuste\n";
echo "✅ Animations désactivées pour les performances\n";
echo "✅ Données de test garanties\n";

echo "\n🚀 PROCHAINES ÉTAPES:\n";
echo "====================\n";
echo "1. 🧹 Vider le cache: php artisan view:clear\n";
echo "2. 🌐 Tester: http://127.0.0.1:8000/admin/dashboard\n";
echo "3. 🔍 Ouvrir la console (F12) pour voir les logs\n";
echo "4. 📊 Les graphiques devraient apparaître progressivement\n";

echo "\n📊 CORRECTION FINALE TERMINÉE!\n";
