<?php

echo "🔧 Correction de l'API Stock...\n\n";

// Commandes à exécuter
$commands = [
    'php artisan cache:clear' => 'Vidage du cache application',
    'php artisan route:clear' => 'Vidage du cache des routes',
    'php artisan config:clear' => 'Vidage du cache de configuration',
    'php artisan view:clear' => 'Vidage du cache des vues',
];

foreach ($commands as $command => $description) {
    echo "📋 $description...\n";
    echo "   Commande: $command\n";
    
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "   ✅ Succès\n";
    } else {
        echo "   ❌ Erreur (code: $returnCode)\n";
        echo "   Sortie: " . implode("\n   ", $output) . "\n";
    }
    echo "\n";
}

echo "🚀 ÉTAPES SUIVANTES:\n";
echo "====================\n";
echo "1. Exécutez: php test_stock_api.php\n";
echo "2. Si le serveur n'est pas démarré: php artisan serve\n";
echo "3. Créez des produits de test: php create_test_products.php\n";
echo "4. Testez sur: http://127.0.0.1:8000/admin/dashboard\n";
echo "5. Ouvrez la console du navigateur (F12) pour voir les logs détaillés\n\n";

echo "🔍 DÉBOGAGE:\n";
echo "============\n";
echo "• Ouvrez F12 dans le navigateur\n";
echo "• Allez dans l'onglet 'État des Stocks'\n";
echo "• Cliquez sur 'Actualiser'\n";
echo "• Regardez la console pour les messages d'erreur détaillés\n";
echo "• Les logs Laravel sont dans: storage/logs/laravel.log\n";
