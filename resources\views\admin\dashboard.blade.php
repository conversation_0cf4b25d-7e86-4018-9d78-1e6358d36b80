@extends('layouts.admin_minimal')

@section('title', 'Tableau de bord administrateur')

@section('content')
<div class="container-fluid py-4">
    <!-- En-tête de bienvenue moderne amélioré -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="modern-welcome-header position-relative overflow-hidden">
                <!-- Arrière-plan animé -->
                <div class="welcome-bg-animated">
                    <div class="bg-shape shape-1"></div>
                    <div class="bg-shape shape-2"></div>
                    <div class="bg-shape shape-3"></div>
                </div>

                <div class="card border-0 shadow-xl modern-welcome-card">
                    <div class="card-body p-4 position-relative">
                        <div class="row align-items-center">
                            <div class="col-lg-8">
                                <div class="welcome-content">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="welcome-avatar-modern me-3">
                                            <div class="avatar-inner">
                                                <i class="fas fa-user-shield"></i>
                                            </div>
                                            <div class="avatar-ring"></div>
                                        </div>
                                        <div class="welcome-text">
                                            <h1 class="display-6 fw-bold mb-1 text-white welcome-title">
                                                Bonjour, {{ Auth::user()->name }}
                                                <span class="wave-emoji">👋</span>
                                            </h1>
                                            <p class="lead text-white-50 mb-0 welcome-subtitle">
                                                Tableau de bord administrateur - GRADIS
                                                <span class="subtitle-accent">Panel</span>
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Badges de statut améliorés -->
                                    <div class="status-badges d-flex flex-wrap gap-2">
                                        <div class="status-badge status-time">
                                            <div class="badge-icon">
                                                <i class="far fa-calendar-alt"></i>
                                            </div>
                                            <div class="badge-content">
                                                <span class="badge-title">{{ now()->format('d M Y') }}</span>
                                                <span class="badge-subtitle">{{ now()->format('H:i') }}</span>
                                            </div>
                                        </div>

                                        <div class="status-badge status-success">
                                            <div class="badge-icon">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                            <div class="badge-content">
                                                <span class="badge-title">Système opérationnel</span>
                                                <span class="badge-subtitle">Tous les services actifs</span>
                                            </div>
                                            <div class="badge-pulse"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 d-none d-lg-block text-end">
                                <div class="welcome-icon-modern">
                                    <div class="icon-wrapper">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="icon-glow"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistiques rapides améliorées -->
                    <div class="card-footer bg-white bg-opacity-10 border-0 py-3">
                        <div class="quick-stats-grid-footer">
                            <div class="quick-stat-item-footer">
                                <div class="stat-icon-wrapper-footer">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="stat-content-footer">
                                    <h5 class="stat-number-footer">{{ ($stats['total_orders'] ?? 0) + ($stats['cement_orders_count'] ?? 0) }}</h5>
                                    <span class="stat-label-footer">Total Commandes</span>
                                </div>
                                <div class="stat-trend-footer positive">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                            </div>

                            <div class="quick-stat-item-footer">
                                <div class="stat-icon-wrapper-footer">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="stat-content-footer">
                                    <h5 class="stat-number-footer">{{ number_format(($stats['monthly_revenue'] ?? 0) + ($stats['monthly_cement_revenue'] ?? 0)) }}</h5>
                                    <span class="stat-label-footer">Revenus (FCFA)</span>
                                </div>
                                <div class="stat-trend-footer positive">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                            </div>

                            <div class="quick-stat-item-footer">
                                <div class="stat-icon-wrapper-footer">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-content-footer">
                                    <h5 class="stat-number-footer">{{ $stats['active_users'] ?? 0 }}</h5>
                                    <span class="stat-label-footer">Utilisateurs actifs</span>
                                </div>
                                <div class="stat-trend-footer neutral">
                                    <i class="fas fa-minus"></i>
                                </div>
                            </div>

                            <div class="quick-stat-item-footer">
                                <div class="stat-icon-wrapper-footer">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="stat-content-footer">
                                    <h5 class="stat-number-footer">{{ $stats['available_drivers'] ?? 0 }}</h5>
                                    <span class="stat-label-footer">Chauffeurs dispo.</span>
                                </div>
                                <div class="stat-trend-footer positive">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alertes système -->
    @if($alerts['low_stock_products'] > 0 || $alerts['pending_supplies'] > 0 || $alerts['pending_cement_orders'] > 0)
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert-banner">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle text-warning me-3 fa-2x"></i>
                        <div>
                            <h6 class="mb-1 fw-bold">Alertes système</h6>
                            <p class="mb-0 text-muted">Éléments nécessitant votre attention</p>
                        </div>
                    </div>
                    <div class="d-flex gap-3">
                        @if($alerts['low_stock_products'] > 0)
                            <span class="badge bg-danger fs-6 px-3 py-2">{{ $alerts['low_stock_products'] }} stock faible</span>
                        @endif
                        @if($alerts['pending_supplies'] > 0)
                            <span class="badge bg-warning fs-6 px-3 py-2">{{ $alerts['pending_supplies'] }} approvisionnements</span>
                        @endif
                        @if($alerts['pending_cement_orders'] > 0)
                            <span class="badge bg-info fs-6 px-3 py-2">{{ $alerts['pending_cement_orders'] }} commandes ciment</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Bouton de restauration des graphiques -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-end">
                <button id="restoreChartsBtn" class="btn btn-outline-primary btn-sm" onclick="restoreChartsManually()" style="display: none;">
                    <i class="fas fa-chart-bar me-1"></i> Restaurer les graphiques
                </button>
            </div>
        </div>
    </div>

    <!-- Statistiques principales modernisées -->
    <div class="row g-4 mb-4">
        <!-- Revenus totaux -->
        <div class="col-sm-6 col-xl-3">
            <div class="stat-card stat-card-primary">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">{{ number_format(($stats['monthly_revenue'] ?? 0) + ($stats['monthly_cement_revenue'] ?? 0)) }}</h3>
                        <p class="stat-label">Revenus mensuels (FCFA)</p>
                        <div class="stat-progress">
                            <div class="progress-bar" style="width: 78%"></div>
                        </div>
                        <span class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> +15.2%
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Commandes totales -->
        <div class="col-sm-6 col-xl-3">
            <div class="stat-card stat-card-success">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">{{ ($stats['total_orders'] ?? 0) + ($stats['cement_orders_count'] ?? 0) }}</h3>
                        <p class="stat-label">Total commandes</p>
                        <div class="stat-progress">
                            <div class="progress-bar" style="width: 65%"></div>
                        </div>
                        <span class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> +8.5%
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Utilisateurs actifs -->
        <div class="col-sm-6 col-xl-3">
            <div class="stat-card stat-card-info">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">{{ $stats['active_users'] ?? 0 }}</h3>
                        <p class="stat-label">Utilisateurs actifs</p>
                        <div class="stat-progress">
                            <div class="progress-bar" style="width: {{ (($stats['active_users'] ?? 0) / max(1, ($stats['users_count'] ?? 1))) * 100 }}%"></div>
                        </div>
                        <span class="stat-change neutral">
                            <i class="fas fa-minus"></i> {{ ($stats['users_count'] ?? 1) - ($stats['active_users'] ?? 0) }} inactifs
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ressources disponibles -->
        <div class="col-sm-6 col-xl-3">
            <div class="stat-card stat-card-warning">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">{{ $stats['available_drivers'] ?? 0 }}/{{ $stats['total_trucks'] ?? 1 }}</h3>
                        <p class="stat-label">Chauffeurs/Camions</p>
                        <div class="stat-progress">
                            <div class="progress-bar" style="width: {{ (($stats['available_drivers'] ?? 0) / max(1, ($stats['total_trucks'] ?? 1))) * 100 }}%"></div>
                        </div>
                        <span class="stat-change {{ ($stats['available_drivers'] ?? 0) > 5 ? 'positive' : 'negative' }}">
                            <i class="fas fa-{{ ($stats['available_drivers'] ?? 0) > 5 ? 'check' : 'exclamation-triangle' }}"></i>
                            {{ ($stats['available_drivers'] ?? 0) > 5 ? 'Disponible' : 'Attention' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques secondaires -->
    <div class="row g-4 mb-4">
        <div class="col-sm-6 col-lg-3">
            <div class="mini-stat-card bg-gradient-primary">
                <div class="mini-stat-icon">
                    <i class="fas fa-industry"></i>
                </div>
                <div class="mini-stat-content">
                    <h4>{{ $stats['total_suppliers'] ?? 0 }}</h4>
                    <p>Fournisseurs</p>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="mini-stat-card bg-gradient-success">
                <div class="mini-stat-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="mini-stat-content">
                    <h4>{{ $stats['categories_count'] ?? 0 }}</h4>
                    <p>Catégories</p>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="mini-stat-card bg-gradient-info">
                <div class="mini-stat-icon">
                    <i class="fas fa-user-friends"></i>
                </div>
                <div class="mini-stat-content">
                    <h4>{{ $stats['total_customers'] ?? 0 }}</h4>
                    <p>Clients</p>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="mini-stat-card bg-gradient-warning">
                <div class="mini-stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="mini-stat-content">
                    <h4>{{ $stats['low_stock_count'] ?? 0 }}</h4>
                    <p>Stock faible</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques et analyses -->
    <div class="row mb-4">
        <!-- Graphique principal des revenus -->
        <div class="col-xl-8">
            <div class="chart-card">
                <div class="chart-header">
                    <div>
                        <h5 class="chart-title">Évolution des revenus</h5>
                        <p class="chart-subtitle">Analyse comparative des ventes par mois</p>
                    </div>
                    <div class="chart-controls">
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="chartPeriod" id="period-year" checked>
                            <label class="btn btn-outline-primary btn-sm" for="period-year">Année</label>

                            <input type="radio" class="btn-check" name="chartPeriod" id="period-semester">
                            <label class="btn btn-outline-primary btn-sm" for="period-semester">6 mois</label>

                            <input type="radio" class="btn-check" name="chartPeriod" id="period-quarter">
                            <label class="btn btn-outline-primary btn-sm" for="period-quarter">3 mois</label>
                        </div>
                    </div>
                </div>
                <div class="chart-body">
                    <div id="revenueChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <!-- Graphique des ressources -->
        <div class="col-xl-4">
            <div class="chart-card">
                <div class="chart-header">
                    <h5 class="chart-title">Ressources disponibles</h5>
                    <p class="chart-subtitle">État des chauffeurs et véhicules</p>
                </div>
                <div class="chart-body">
                    <div id="resourcesChart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques secondaires -->
    <div class="row mb-4">
        <!-- Revenus par catégorie -->
        <div class="col-xl-6">
            <div class="chart-card">
                <div class="chart-header">
                    <h5 class="chart-title">Revenus par catégorie</h5>
                    <p class="chart-subtitle">Top 5 des catégories les plus rentables</p>
                </div>
                <div class="chart-body">
                    <div id="categoryRevenueChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- Commandes de ciment -->
        <div class="col-xl-6">
            <div class="chart-card">
                <div class="chart-header">
                    <h5 class="chart-title">Commandes de ciment</h5>
                    <p class="chart-subtitle">Évolution mensuelle du tonnage</p>
                </div>
                <div class="chart-body">
                    <div id="cementOrdersChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Widgets d'actions rapides et informations importantes -->
    <div class="row mb-4">
        <!-- Actions rapides modernisées -->
        <div class="col-xl-4">
            <div class="quick-actions-card">
                <div class="quick-actions-header">
                    <h5 class="title">Actions rapides</h5>
                    <p class="subtitle">Accès direct aux fonctionnalités</p>
                </div>
                <div class="quick-actions-body">
                    <div class="action-grid">
                        <a href="{{ route('admin.supplies.index') }}" class="action-item action-primary">
                            <div class="action-icon">
                                <i class="fas fa-truck-loading"></i>
                            </div>
                            <div class="action-content">
                                <h6>Approvisionnements</h6>
                                <p>Gérer les stocks</p>
                            </div>
                            @if($alerts['pending_supplies'] > 0)
                                <span class="action-badge badge-danger">{{ $alerts['pending_supplies'] }}</span>
                            @endif
                        </a>

                        <a href="{{ route('admin.drivers.index') }}" class="action-item action-info">
                            <div class="action-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="action-content">
                                <h6>Chauffeurs</h6>
                                <p>Gestion équipes</p>
                            </div>
                            <span class="action-badge badge-info">{{ $stats['available_drivers'] ?? 0 }}</span>
                        </a>

                        <a href="{{ route('admin.products.index') }}" class="action-item action-success">
                            <div class="action-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="action-content">
                                <h6>Produits</h6>
                                <p>Catalogue & prix</p>
                            </div>
                            <span class="action-badge badge-success">{{ $stats['products_count'] ?? 0 }}</span>
                        </a>

                        <a href="{{ route('admin.sales.index') }}" class="action-item action-warning">
                            <div class="action-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="action-content">
                                <h6>Ventes</h6>
                                <p>Suivi commandes</p>
                            </div>
                        </a>

                        <a href="{{ route('admin.users.index') }}" class="action-item action-secondary">
                            <div class="action-icon">
                                <i class="fas fa-user-cog"></i>
                            </div>
                            <div class="action-content">
                                <h6>Utilisateurs</h6>
                                <p>Gestion comptes</p>
                            </div>
                            <span class="action-badge badge-secondary">{{ $stats['users_count'] ?? 1 }}</span>
                        </a>

                        <a href="{{ route('admin.trucks.index') }}" class="action-item action-dark">
                            <div class="action-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="action-content">
                                <h6>Camions</h6>
                                <p>Flotte véhicules</p>
                            </div>
                            <span class="action-badge badge-dark">{{ $stats['total_trucks'] ?? 1 }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- Informations importantes -->
        <div class="col-xl-8">
            <div class="info-tabs-card">
                <div class="info-tabs-header">
                    <ul class="nav nav-pills" id="infoTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="supplies-tab" data-bs-toggle="pill" data-bs-target="#supplies" type="button" role="tab">
                                <i class="fas fa-truck-loading me-2"></i>Approvisionnements
                                @if($alerts['pending_supplies'] > 0)
                                    <span class="badge bg-danger ms-2">{{ $alerts['pending_supplies'] }}</span>
                                @endif
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="stock-tab" data-bs-toggle="pill" data-bs-target="#stock" type="button" role="tab">
                                <i class="fas fa-exclamation-triangle me-2"></i>Stock faible
                                @if($alerts['low_stock_products'] > 0)
                                    <span class="badge bg-warning ms-2">{{ $alerts['low_stock_products'] }}</span>
                                @endif
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="stock-status-tab" data-bs-toggle="pill" data-bs-target="#stock-status" type="button" role="tab">
                                <i class="fas fa-boxes me-2"></i>État des Stocks
                                @if(isset($stockStats) && $stockStats['out_of_stock_count'] > 0)
                                    <span class="badge bg-danger ms-2">{{ $stockStats['out_of_stock_count'] }}</span>
                                @elseif(isset($stockStats) && $stockStats['low_stock_count'] > 0)
                                    <span class="badge bg-warning ms-2">{{ $stockStats['low_stock_count'] }}</span>
                                @endif
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="drivers-tab" data-bs-toggle="pill" data-bs-target="#drivers" type="button" role="tab">
                                <i class="fas fa-star me-2"></i>Top Chauffeurs
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="info-tabs-body">
                    <div class="tab-content" id="infoTabsContent">
                        <!-- Onglet Approvisionnements -->
                        <div class="tab-pane fade show active" id="supplies" role="tabpanel">
                            @if($pendingSupplies->isNotEmpty())
                                <div class="data-table">
                                    <table class="table table-hover align-middle mb-0">
                                        <thead>
                                            <tr>
                                                <th>Référence</th>
                                                <th>Fournisseur</th>
                                                <th>Montant</th>
                                                <th>Date</th>
                                                <th class="text-end">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($pendingSupplies as $supply)
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="table-icon bg-warning">
                                                            <i class="fas fa-clipboard-list"></i>
                                                        </div>
                                                        <span class="fw-bold">{{ $supply->reference }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary text-white me-2">
                                                            {{ $supply->supplier ? substr($supply->supplier->name, 0, 1) : 'N' }}
                                                        </div>
                                                        {{ $supply->supplier->name ?? 'N/A' }}
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="fw-bold text-success">{{ number_format($supply->total_amount) }} FCFA</span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">{{ $supply->created_at ? $supply->created_at->format('d/m/Y H:i') : 'N/A' }}</small>
                                                </td>
                                                <td class="text-end">
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-success" onclick="validateSupply({{ $supply->id }})" title="Valider">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-danger" onclick="rejectSupply({{ $supply->id }})" title="Rejeter">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                        <a href="{{ route('admin.supplies.show', $supply) }}" class="btn btn-info" title="Détails">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="empty-state">
                                    <div class="empty-state-icon">
                                        <i class="fas fa-clipboard-check"></i>
                                    </div>
                                    <h6>Aucun approvisionnement en attente</h6>
                                    <p>Tous les approvisionnements sont à jour</p>
                                </div>
                            @endif
                        </div>

                        <!-- Onglet Stock faible -->
                        <div class="tab-pane fade" id="stock" role="tabpanel">
                            @if($lowStockProducts->isNotEmpty())
                                <div class="data-table">
                                    <table class="table table-hover align-middle mb-0">
                                        <thead>
                                            <tr>
                                                <th>Produit</th>
                                                <th>Catégorie</th>
                                                <th>Stock actuel</th>
                                                <th class="text-end">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($lowStockProducts as $product)
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="table-icon bg-danger">
                                                            <i class="fas fa-box"></i>
                                                        </div>
                                                        <span class="fw-bold">{{ $product->name }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-light text-dark">{{ $product->category->name ?? 'Sans catégorie' }}</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-danger">{{ ($product->stock_quantity ?? 0) }} {{ ($product->unit ?? 'unité') }}</span>
                                                </td>
                                                <td class="text-end">
                                                    <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit me-1"></i>Réapprovisionner
                                                    </a>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="empty-state">
                                    <div class="empty-state-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <h6>Stock suffisant</h6>
                                    <p>Tous les produits ont un stock adéquat</p>
                                </div>
                            @endif
                        </div>

                        <!-- Onglet Top Chauffeurs -->
                        <div class="tab-pane fade" id="drivers" role="tabpanel">
                            @if($topDrivers->isNotEmpty())
                                <div class="data-table">
                                    <table class="table table-hover align-middle mb-0">
                                        <thead>
                                            <tr>
                                                <th>Chauffeur</th>
                                                <th>Voyages ce mois</th>
                                                <th>Statut</th>
                                                <th class="text-end">Performance</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($topDrivers as $driver)
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-info text-white me-2">
                                                            {{ substr($driver->full_name, 0, 1) }}
                                                        </div>
                                                        <span class="fw-bold">{{ $driver->full_name }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">{{ $driver->completed_trips + $driver->completed_deliveries }}</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ ($driver->status ?? 'available') === 'available' ? 'success' : 'secondary' }}">
                                                        {{ ($driver->status ?? 'available') === 'available' ? 'Disponible' : 'Indisponible' }}
                                                    </span>
                                                </td>
                                                <td class="text-end">
                                                    <div class="progress" style="width: 60px; height: 8px;">
                                                        <div class="progress-bar bg-success" style="width: {{ min(100, ($driver->completed_trips + $driver->completed_deliveries) * 10) }}%"></div>
                                                    </div>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="empty-state">
                                    <div class="empty-state-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h6>Aucune donnée disponible</h6>
                                    <p>Pas de voyages enregistrés ce mois</p>
                                </div>
                            @endif
                        </div>

                        <!-- Onglet État des Stocks -->
                        <div class="tab-pane fade" id="stock-status" role="tabpanel">
                            <div class="alert alert-success mb-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Système de Suivi des Stocks Activé!</strong>
                                <p class="mb-0 mt-2">Cette section affiche l'état en temps réel de tous vos stocks.</p>
                            </div>
                            <div class="row">
                                <!-- Résumé des stocks -->
                                <div class="col-12 mb-3">
                                    <div class="stock-summary-cards">
                                        <div class="row g-3">
                                            <div class="col-md-3">
                                                <div class="stock-summary-card bg-primary">
                                                    <div class="stock-summary-icon">
                                                        <i class="fas fa-weight-hanging"></i>
                                                    </div>
                                                    <div class="stock-summary-content">
                                                        <h4 id="total-tonnage-display">{{ $stockStatus['summary']['total_tonnage_display'] ?? '0 T' }}</h4>
                                                        <p>Tonnage total</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="stock-summary-card bg-info">
                                                    <div class="stock-summary-icon">
                                                        <i class="fas fa-boxes"></i>
                                                    </div>
                                                    <div class="stock-summary-content">
                                                        <h4 id="total-products-count">{{ $stockStats['total_products'] }}</h4>
                                                        <p>Produits actifs</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="stock-summary-card bg-warning">
                                                    <div class="stock-summary-icon">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                    </div>
                                                    <div class="stock-summary-content">
                                                        <h4 id="low-stock-count">{{ $stockStats['low_stock_count'] }}</h4>
                                                        <p>Stock faible</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="stock-summary-card bg-danger">
                                                    <div class="stock-summary-icon">
                                                        <i class="fas fa-times-circle"></i>
                                                    </div>
                                                    <div class="stock-summary-content">
                                                        <h4 id="out-of-stock-count">{{ $stockStats['out_of_stock_count'] }}</h4>
                                                        <p>Rupture de stock</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tableau des stocks -->
                                <div class="col-12">
                                    <div class="stock-table-container">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">État détaillé des stocks</h6>
                                            <div class="stock-controls">
                                                <button class="btn btn-sm btn-outline-primary" onclick="refreshStockData()">
                                                    <i class="fas fa-sync-alt me-1"></i>Actualiser
                                                </button>
                                                <span class="text-muted ms-2" id="last-update">
                                                    @if($stockStats['last_stock_update'])
                                                        Dernière MAJ: {{ $stockStats['last_stock_update'] ? $stockStats['last_stock_update']->format('d/m/Y H:i') : 'N/A' }}
                                                    @else
                                                        Aucune mise à jour
                                                    @endif
                                                </span>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-hover align-middle mb-0" id="stock-table">
                                                <thead>
                                                    <tr>
                                                        <th>Produit</th>
                                                        <th>Catégorie</th>
                                                        <th>Stock actuel</th>
                                                        <th>Valeur</th>
                                                        <th>Statut</th>
                                                        <th>Dernière MAJ</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="stock-table-body">
                                                    @foreach($stockStatus['products'] as $product)
                                                    <tr data-product-id="{{ $product['id'] }}">
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="table-icon bg-primary">
                                                                    <i class="fas fa-box"></i>
                                                                </div>
                                                                <span class="fw-bold">{{ $product['name'] }}</span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-light text-dark">{{ $product['category'] }}</span>
                                                        </td>
                                                        <td>
                                                            @if($product['status'] === 'out_of_stock')
                                                                <span class="badge bg-danger px-2 py-1" style="font-size: 0.75rem;">
                                                                    <i class="fas fa-times-circle me-1" style="font-size: 0.7rem;"></i>
                                                                    {{ $product['tonnage_display'] ?? ($product['current_stock'] . ' ' . $product['unit']) }}
                                                                </span>
                                                            @elseif($product['status'] === 'low_stock')
                                                                <span class="badge bg-warning px-2 py-1" style="font-size: 0.75rem;">
                                                                    <i class="fas fa-exclamation-triangle me-1" style="font-size: 0.7rem;"></i>
                                                                    {{ $product['tonnage_display'] ?? ($product['current_stock'] . ' ' . $product['unit']) }}
                                                                </span>
                                                            @else
                                                                <span class="badge bg-success px-2 py-1" style="font-size: 0.75rem;">
                                                                    <i class="fas fa-check-circle me-1" style="font-size: 0.7rem;"></i>
                                                                    {{ $product['tonnage_display'] ?? ($product['current_stock'] . ' ' . $product['unit']) }}
                                                                </span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-light text-dark fs-6 px-3 py-2">
                                                                <i class="fas fa-coins me-1"></i>
                                                                {{ number_format($product['total_value'], 0, ',', ' ') }} FCFA
                                                            </span>
                                                        </td>
                                                        <td>
                                                            @if($product['status'] === 'out_of_stock')
                                                                <span class="badge bg-danger fs-6 px-3 py-2">
                                                                    <i class="fas fa-ban me-1"></i>Rupture
                                                                </span>
                                                            @elseif($product['status'] === 'low_stock')
                                                                <span class="badge bg-warning fs-6 px-3 py-2">
                                                                    <i class="fas fa-exclamation-triangle me-1"></i>Stock faible
                                                                </span>
                                                            @else
                                                                <span class="badge bg-success fs-6 px-3 py-2">
                                                                    <i class="fas fa-check me-1"></i>Normal
                                                                </span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <small class="text-muted">{{ $product['last_updated'] ? $product['last_updated']->format('d/m/Y H:i') : 'N/A' }}</small>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dernières activités et informations -->
    <div class="row">
        <!-- Top produits -->
        <div class="col-xl-6 mb-4">
            <div class="data-card">
                <div class="data-card-header">
                    <h5 class="title">Top 5 des produits</h5>
                    <p class="subtitle">Produits les plus commandés</p>
                </div>
                <div class="data-card-body">
                    @if($topProducts->isNotEmpty())
                        <div class="product-list">
                            @foreach($topProducts as $index => $product)
                            <div class="product-item">
                                <div class="product-rank">
                                    <span class="rank-number">#{{ $index + 1 }}</span>
                                </div>
                                <div class="product-info">
                                    <h6 class="product-name">{{ $product->name }}</h6>
                                    <span class="product-category">{{ $product->category ? $product->category->name ?? 'Sans catégorie' : 'Sans catégorie' }}</span>
                                </div>
                                <div class="product-stats">
                                    <span class="product-count">{{ $product->order_items_count }}</span>
                                    <div class="product-progress">
                                        <div class="progress-bar" style="width: {{ min(100, $product->order_items_count * 10) }}%"></div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <h6>Aucun produit vendu</h6>
                            <p>Aucune vente enregistrée pour le moment</p>
                        </div>
                    @endif
                </div>
                <div class="data-card-footer">
                    <a href="{{ route('admin.products.index') }}" class="btn btn-outline-primary btn-sm">
                        Voir tous les produits <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Derniers utilisateurs -->
        <div class="col-xl-6 mb-4">
            <div class="data-card">
                <div class="data-card-header">
                    <h5 class="title">Derniers utilisateurs</h5>
                    <p class="subtitle">Utilisateurs récemment inscrits</p>
                </div>
                <div class="data-card-body">
                    @if($latest_users->isNotEmpty())
                        <div class="user-list">
                            @foreach($latest_users as $user)
                            <div class="user-item">
                                <div class="user-avatar">
                                    {{ substr($user->name, 0, 1) }}
                                </div>
                                <div class="user-info">
                                    <h6 class="user-name">{{ $user->name }}</h6>
                                    <p class="user-role">{{ $user->roles && $user->roles->first() ? ($user->roles && $user->roles->first() ? $user->roles->first()->name : 'Utilisateur') : 'Utilisateur' }}</p>
                                    <small class="user-email">{{ $user->email }}</small>
                                </div>
                                <div class="user-meta">
                                    <span class="user-date">{{ $user->created_at ? $user->created_at->format('d/m/Y') : 'N/A' }}</span>
                                    <span class="user-status active">
                                        <i class="fas fa-circle"></i>
                                    </span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h6>Aucun utilisateur récent</h6>
                            <p>Aucune inscription récente</p>
                        </div>
                    @endif
                </div>
                <div class="data-card-footer">
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary btn-sm">
                        Voir tous les utilisateurs <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations système du comptable -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="system-info-section">
                <div class="row g-3">
                    <!-- Système en ligne -->
                    <div class="col-lg-3 col-md-6">
                        <div class="system-card online-card">
                            <div class="card-icon">
                                <i class="fas fa-heart text-success"></i>
                            </div>
                            <div class="card-content">
                                <h6 class="card-title">Système en ligne</h6>
                                <p class="card-subtitle">Dernière mise à jour</p>
                                <div class="card-value">{{ now()->format('d/m/Y') }} à {{ now()->format('H:i') }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Session active -->
                    <div class="col-lg-2 col-md-6">
                        <div class="system-card session-card">
                            <div class="card-icon">
                                <i class="fas fa-clock text-primary"></i>
                            </div>
                            <div class="card-content">
                                <div class="card-value">{{ gmdate('H:i:s', time() - session()->get('login_time', time())) }}</div>
                                <p class="card-label">SESSION ACTIVE</p>
                            </div>
                        </div>
                    </div>

                    <!-- Mémoire -->
                    <div class="col-lg-2 col-md-6">
                        <div class="system-card memory-card">
                            <div class="card-icon">
                                <i class="fas fa-memory text-info"></i>
                            </div>
                            <div class="card-content">
                                <div class="card-value">{{ round(memory_get_usage(true) / 1024 / 1024, 1) }}MB</div>
                                <p class="card-label">MÉMOIRE</p>
                            </div>
                        </div>
                    </div>

                    <!-- Performance -->
                    <div class="col-lg-2 col-md-6">
                        <div class="system-card performance-card">
                            <div class="card-icon">
                                <i class="fas fa-tachometer-alt text-warning"></i>
                            </div>
                            <div class="card-content">
                                <div class="card-value">98%</div>
                                <p class="card-label">PERFORMANCE</p>
                            </div>
                        </div>
                    </div>

                    <!-- GRADIS Info -->
                    <div class="col-lg-3 col-md-12">
                        <div class="system-card gradis-card">
                            <div class="card-icon">
                                <i class="fas fa-cube text-purple"></i>
                            </div>
                            <div class="card-content">
                                <h6 class="card-title">GRADIS</h6>
                                <p class="card-subtitle">v2.1.4</p>
                                <div class="card-details">
                                    <small><i class="fas fa-hammer"></i> Build {{ now()->format('Y.m.d') }}</small>
                                    <small><i class="fas fa-shield-alt"></i> Sécurisé SSL</small>
                                    <small class="text-muted">© {{ now()->year }} GRADIS. Tous droits réservés.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Barre de progression et informations supplémentaires -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="system-status-bar">
                            <div class="status-progress">
                                <div class="progress-label">
                                    <i class="fas fa-sync-alt"></i>
                                    <span>Actualisation automatique dans 30s</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%"></div>
                                </div>
                            </div>
                            <div class="status-info">
                                <span class="update-time">
                                    <i class="fas fa-clock"></i>
                                    Mise à jour : {{ now()->format('d/m/Y H:i') }}
                                </span>
                                <div class="action-links">
                                    <a href="#" class="text-primary me-3">
                                        <i class="fas fa-question-circle"></i> Aide
                                    </a>
                                    <a href="#" class="text-info">
                                        <i class="fas fa-cog"></i> Paramètres
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-white border-top">
    <div class="container text-center">
        <span class="text-muted"> {{ date('Y') }} GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>

@endsection

@push('styles')
<style>
/* Variables CSS pour la cohérence des couleurs */
:root {
    --primary-color: #4f46e5;
    --primary-light: #818cf8;
    --primary-dark: #3730a3;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --secondary-color: #6b7280;
    --dark-color: #1f2937;
    --light-color: #f9fafb;
    --white: #ffffff;
    --border-color: #e5e7eb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ===== STYLES POUR LES CARTES SYSTÈME ===== */
.system-info-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.system-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(226, 232, 240, 0.6);
    transition: all 0.15s ease;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 15px;
}

.system-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.system-card .card-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.online-card .card-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.session-card .card-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.memory-card .card-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.performance-card .card-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.gradis-card .card-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.system-card .card-content {
    flex: 1;
}

.system-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.system-card .card-subtitle {
    font-size: 0.8rem;
    color: #64748b;
    margin-bottom: 8px;
}

.system-card .card-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #059669;
}

.system-card .card-label {
    font-size: 0.7rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
}

.gradis-card .card-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.gradis-card .card-details small {
    font-size: 0.7rem;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 4px;
}

.system-status-bar {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-progress {
    flex: 1;
    margin-right: 20px;
}

.progress-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: #64748b;
    margin-bottom: 8px;
}

.progress-label i {
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.progress {
    height: 6px;
    background: #f1f5f9;
    border-radius: 3px;
}

.progress-bar {
    border-radius: 3px;
}

.status-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.update-time {
    font-size: 0.8rem;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 6px;
}

.action-links a {
    font-size: 0.8rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.15s ease;
}

.action-links a:hover {
    transform: translateY(-1px);
}

.text-purple {
    color: #8b5cf6 !important;
}

/* Responsive pour les cartes système */
@media (max-width: 768px) {
    .system-info-section {
        padding: 15px;
    }

    .system-card {
        padding: 15px;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .system-status-bar {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .status-progress {
        margin-right: 0;
        width: 100%;
    }

    .status-info {
        flex-direction: column;
        gap: 10px;
    }
}

/* ===== STYLES MODERNES POUR LA SIDEBAR ===== */

/* Sidebar principale */
.modern-sidebar {
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

/* Header de la sidebar */
.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.sidebar-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.brand-text {
    flex: 1;
}

.brand-title {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
    letter-spacing: 0.5px;
}

.brand-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Indicateur de statut système */
.system-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 6px;
    font-size: 0.75rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-indicator.online {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
}

.status-text {
    color: #10b981;
    font-weight: 500;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Navigation */
.sidebar-nav {
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-heading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    position: relative;
}

.nav-heading-icon {
    color: var(--primary-light);
    font-size: 0.875rem;
}

.nav-heading-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    flex: 1;
}

.nav-heading-line {
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
    margin-left: 0.5rem;
}

/* Groupe de navigation */
.nav-group {
    padding: 0 0.5rem;
}

/* Liens de navigation */
.nav-link {
    display: block;
    text-decoration: none;
    margin-bottom: 0.25rem;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    position: relative;
    z-index: 1;
}

.nav-icon-wrapper {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.15s ease;
}

.nav-icon {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    transition: all 0.15s ease;
}

.nav-text {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: 0.875rem;
    flex: 1;
    transition: all 0.15s ease;
}

.nav-indicator {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: transparent;
    transition: all 0.15s ease;
}

/* États des liens */
.nav-link:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateX(4px);
}

.nav-link:hover .nav-icon-wrapper {
    background: rgba(79, 70, 229, 0.2);
    transform: scale(1.1);
}

.nav-link:hover .nav-icon {
    color: var(--primary-light);
    transform: scale(1.1);
}

.nav-link:hover .nav-text {
    color: #ffffff;
}

.nav-link:hover .nav-indicator {
    background: var(--primary-light);
    box-shadow: 0 0 8px rgba(79, 70, 229, 0.5);
}

/* Lien actif */
.nav-link.active {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.2) 0%, rgba(129, 140, 248, 0.1) 100%);
    border: 1px solid rgba(79, 70, 229, 0.3);
    transform: translateX(4px);
}

.nav-link.active .nav-icon-wrapper {
    background: var(--primary-color);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.nav-link.active .nav-icon {
    color: #ffffff;
}

.nav-link.active .nav-text {
    color: #ffffff;
    font-weight: 600;
}

.nav-link.active .nav-indicator {
    background: #ffffff;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* Footer de la sidebar */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    border-radius: 50%;
    color: #ffffff;
    font-size: 1.25rem;
}

.user-details {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.user-name {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-role {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
    font-weight: 500;
}

/* ===== RESPONSIVE DESIGN AMÉLIORÉ ===== */

/* Overlay pour mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 1039;
    opacity: 0;
    visibility: hidden;
    transition: all 0.15s ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Tablettes (768px - 991px) */
@media (max-width: 991.98px) {
    .modern-sidebar {
        transform: translateX(-100%);
        width: 280px !important;
        z-index: 1040;
        box-shadow: 4px 0 30px rgba(0, 0, 0, 0.3);
    }

    .modern-sidebar.show {
        transform: translateX(0);
    }

    .sidebar-footer {
        position: relative;
    }

    /* Ajustements pour le contenu principal */
    .main-content {
        margin-left: 0 !important;
        width: 100%;
        padding: 1rem;
    }

    /* En-tête responsive */
    .modern-welcome-header {
        margin-bottom: 1.5rem;
    }

    .welcome-content {
        text-align: center;
    }

    .welcome-avatar-modern {
        width: 60px;
        height: 60px;
    }

    .avatar-inner {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .welcome-title {
        font-size: 1.75rem !important;
    }

    .welcome-subtitle {
        font-size: 1rem !important;
    }

    .status-badges {
        justify-content: center;
        margin-top: 1.5rem;
    }

    .status-badge {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
        min-width: 140px;
    }

    .badge-content {
        align-items: center;
        margin-top: 0.5rem;
    }

    /* Icône de bienvenue cachée sur tablette */
    .welcome-icon-modern {
        display: none;
    }

    /* Statistiques footer responsive */
    .quick-stats-grid-footer {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .quick-stat-item-footer {
        padding: 0.75rem;
    }

    .stat-number-footer {
        font-size: 1.1rem;
    }
}

/* Mobiles (576px - 767px) */
@media (max-width: 767.98px) {
    .modern-sidebar {
        width: 100% !important;
        max-width: 320px;
    }

    /* Container principal */
    .container-fluid {
        padding: 0.75rem;
    }

    /* En-tête mobile */
    .modern-welcome-header {
        margin-bottom: 1rem;
    }

    .modern-welcome-card .card-body {
        padding: 1.5rem;
    }

    .welcome-content {
        text-align: center;
    }

    .d-flex.align-items-center.mb-3 {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .welcome-avatar-modern {
        width: 50px;
        height: 50px;
        margin-bottom: 0.5rem;
    }

    .avatar-inner {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .welcome-title {
        font-size: 1.5rem !important;
        text-align: center;
    }

    .welcome-subtitle {
        font-size: 0.9rem !important;
        text-align: center;
    }

    .status-badges {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
        margin-top: 1rem;
    }

    .status-badge {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    /* Statistiques en une seule colonne sur mobile */
    .quick-stats-grid-footer {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .quick-stat-item-footer {
        padding: 1rem;
        text-align: center;
    }

    .stat-content-footer {
        text-align: center;
    }

    .stat-number-footer {
        font-size: 1.25rem;
    }

    .stat-label-footer {
        font-size: 0.75rem;
    }

    /* Cartes de statistiques principales */
    .stat-card {
        margin-bottom: 1rem;
    }

    .stat-card-body {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
        margin: 0 auto 0.5rem;
    }

    .stat-content {
        text-align: center;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    /* Graphiques responsive */
    .chart-card {
        margin-bottom: 1rem;
    }

    .chart-body {
        padding: 0.5rem;
    }

    /* Actions rapides */
    .quick-actions-card {
        margin-bottom: 1rem;
    }

    .action-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .action-item {
        padding: 1rem;
        text-align: center;
    }

    /* Tableaux responsive */
    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table {
        font-size: 0.875rem;
    }

    .table th,
    .table td {
        padding: 0.5rem;
        white-space: nowrap;
    }
}

/* Très petits écrans (moins de 576px) */
@media (max-width: 575.98px) {
    .container-fluid {
        padding: 0.5rem;
    }

    .modern-welcome-card .card-body {
        padding: 1rem;
    }

    .welcome-title {
        font-size: 1.25rem !important;
    }

    .welcome-subtitle {
        font-size: 0.8rem !important;
    }

    .status-badge {
        padding: 0.75rem;
        font-size: 0.8rem;
    }

    .badge-title {
        font-size: 0.75rem;
    }

    .badge-subtitle {
        font-size: 0.7rem;
    }

    .quick-stat-item-footer {
        padding: 0.75rem;
    }

    .stat-number-footer {
        font-size: 1.1rem;
    }

    .stat-label-footer {
        font-size: 0.7rem;
    }

    /* Sidebar pleine largeur sur très petits écrans */
    .modern-sidebar {
        width: 100% !important;
        max-width: none;
    }

    /* Réduire les espacements */
    .row.g-4 {
        --bs-gutter-x: 0.75rem;
        --bs-gutter-y: 0.75rem;
    }

    .mb-4 {
        margin-bottom: 1rem !important;
    }

    /* Optimisations spécifiques pour les cartes */
    .chart-card .chart-body {
        padding: 0.5rem;
        min-height: 250px;
    }

    .chart-card .chart-header {
        padding: 0.75rem;
    }

    .chart-title {
        font-size: 0.9rem;
    }

    .chart-subtitle {
        font-size: 0.75rem;
    }
}

/* ===== AMÉLIORATIONS POUR LES GRAPHIQUES RESPONSIVES ===== */

/* Conteneurs de graphiques */
@media (max-width: 767.98px) {
    #revenueChart,
    #resourceChart,
    #categoryRevenueChart,
    #cementOrdersChart {
        height: 250px !important;
    }

    .chart-card {
        margin-bottom: 1rem;
    }

    .chart-header {
        text-align: center;
        padding: 1rem 0.75rem 0.5rem;
    }

    .chart-title {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .chart-subtitle {
        font-size: 0.8rem;
        color: rgba(107, 114, 128, 0.8);
    }
}

/* ===== AMÉLIORATIONS POUR LES TABLEAUX RESPONSIVES ===== */

@media (max-width: 767.98px) {
    .table-responsive {
        border: none;
        box-shadow: none;
    }

    .table {
        font-size: 0.8rem;
        margin-bottom: 0;
    }

    .table th {
        padding: 0.5rem 0.25rem;
        font-size: 0.7rem;
        border-bottom: 2px solid #e5e7eb;
        background: #f8fafc;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .table td {
        padding: 0.5rem 0.25rem;
        border-bottom: 1px solid #f1f5f9;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 120px;
    }

    /* Styles pour les badges dans les tableaux */
    .badge {
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
    }

    /* Boutons d'action plus petits */
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
    }
}

/* ===== AMÉLIORATIONS POUR LES ALERTES ===== */

@media (max-width: 767.98px) {
    .alert-banner {
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 8px;
    }

    .alert-banner .alert {
        margin-bottom: 0.5rem;
        padding: 0.75rem;
        font-size: 0.85rem;
    }

    .alert-banner .alert:last-child {
        margin-bottom: 0;
    }
}

/* ===== OPTIMISATIONS POUR LES ACTIONS RAPIDES ===== */

@media (max-width: 767.98px) {
    .quick-actions-card {
        margin-bottom: 1rem;
    }

    .quick-actions-header {
        padding: 1rem;
        text-align: center;
    }

    .quick-actions-body {
        padding: 0.75rem;
    }

    .action-grid {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .action-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.15s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .action-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        text-decoration: none;
    }

    .action-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        margin-right: 1rem;
        font-size: 1.1rem;
    }

    .action-content {
        flex: 1;
    }

    .action-content h6 {
        margin: 0 0 0.25rem 0;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .action-content p {
        margin: 0;
        font-size: 0.75rem;
        color: #6b7280;
    }

    .action-badge {
        margin-left: auto;
    }
}

/* ===== STYLES POUR LES MINI-STATISTIQUES ===== */

@media (max-width: 767.98px) {
    .mini-stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .mini-stat-card {
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.15s ease;
    }

    .mini-stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .mini-stat-icon {
        width: 40px;
        height: 40px;
        margin: 0 auto 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        font-size: 1.1rem;
        color: #ffffff;
    }

    .mini-stat-value {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
        color: #111827;
    }

    .mini-stat-label {
        font-size: 0.75rem;
        color: #6b7280;
        font-weight: 500;
    }
}

/* ===== ANIMATIONS ET TRANSITIONS MOBILES ===== */

@media (max-width: 767.98px) {
    /* Animation d'entrée pour les cartes */
    .stat-card,
    .chart-card,
    .quick-actions-card,
    .mini-stat-card {
        animation: slideInUp 0.6s ease forwards;
    }

    .stat-card:nth-child(1) { animation-delay: 0.1s; }
    .stat-card:nth-child(2) { animation-delay: 0.2s; }
    .stat-card:nth-child(3) { animation-delay: 0.3s; }
    .stat-card:nth-child(4) { animation-delay: 0.4s; }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Amélioration des transitions tactiles */
    .action-item,
    .stat-card,
    .mini-stat-card {
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }

    /* Feedback visuel pour les interactions tactiles */
    .action-item:active,
    .stat-card:active,
    .mini-stat-card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}

/* Animation d'entrée pour les éléments de navigation */
.nav-link {
    animation: slideInLeft 0.3s ease forwards;
    opacity: 0;
    transform: translateX(-20px);
}

.nav-link:nth-child(1) { animation-delay: 0.1s; }
.nav-link:nth-child(2) { animation-delay: 0.2s; }
.nav-link:nth-child(3) { animation-delay: 0.3s; }
.nav-link:nth-child(4) { animation-delay: 0.4s; }
.nav-link:nth-child(5) { animation-delay: 0.1s; }

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== STYLES POUR L'EN-TÊTE MODERNE ===== */

/* Container principal de l'en-tête */
.modern-welcome-header {
    border-radius: 16px;
    overflow: hidden;
    position: relative;
}

/* Arrière-plan animé */
.welcome-bg-animated {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    z-index: 1;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.bg-shape.shape-1 {
    width: 200px;
    height: 200px;
    top: -50px;
    right: -50px;
    animation-delay: 0s;
}

.bg-shape.shape-2 {
    width: 150px;
    height: 150px;
    bottom: -30px;
    left: -30px;
    animation-delay: 2s;
}

.bg-shape.shape-3 {
    width: 100px;
    height: 100px;
    top: 50%;
    right: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Carte principale */
.modern-welcome-card {
    position: relative;
    z-index: 2;
    background: transparent !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px);
}

/* Avatar moderne */
.welcome-avatar-modern {
    position: relative;
    width: 80px;
    height: 80px;
}

.avatar-inner {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #ffffff;
    position: relative;
    z-index: 2;
    backdrop-filter: blur(10px);
}

.avatar-ring {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid transparent;
    border-radius: 50%;
    background: linear-gradient(45deg, #ffffff, transparent, #ffffff);
    background-clip: padding-box;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Texte de bienvenue */
.welcome-title {
    background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 2s ease-in-out infinite alternate;
}

.wave-emoji {
    display: inline-block;
    animation: wave 2s ease-in-out infinite;
    margin-left: 0.5rem;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(20deg); }
    75% { transform: rotate(-10deg); }
}

@keyframes titleGlow {
    0% { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
    100% { text-shadow: 0 0 30px rgba(255, 255, 255, 0.8); }
}

.welcome-subtitle {
    position: relative;
}

.subtitle-accent {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    margin-left: 0.5rem;
}

/* Badges de statut */
.status-badges {
    margin-top: 1rem;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.15s ease;
    position: relative;
    overflow: hidden;
}

.status-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.status-badge.status-success {
    border-color: rgba(16, 185, 129, 0.3);
}

.status-badge.status-time {
    border-color: rgba(59, 130, 246, 0.3);
}

.badge-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
}

.badge-content {
    display: flex;
    flex-direction: column;
}

.badge-title {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.2;
}

.badge-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-pulse {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Icône de bienvenue moderne */
.welcome-icon-modern {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-wrapper {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    font-size: 3rem;
    color: #ffffff;
    position: relative;
    z-index: 2;
    backdrop-filter: blur(10px);
}

.icon-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    animation: iconGlow 3s ease-in-out infinite alternate;
}

@keyframes iconGlow {
    0% { opacity: 0.5; transform: scale(1); }
    100% { opacity: 1; transform: scale(1.1); }
}

/* Statistiques du footer */
.quick-stats-grid-footer {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    padding: 0.5rem 0;
}

.quick-stat-item-footer {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: all 0.15s ease;
    position: relative;
    overflow: hidden;
}

.quick-stat-item-footer:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon-wrapper-footer {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: #ffffff;
    font-size: 1.1rem;
    transition: all 0.15s ease;
}

.quick-stat-item-footer:hover .stat-icon-wrapper-footer {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

.stat-content-footer {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.stat-number-footer {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.25rem;
    margin: 0;
    line-height: 1.2;
}

.stat-label-footer {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-trend-footer {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 0.75rem;
    font-weight: 600;
}

.stat-trend-footer.positive {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.stat-trend-footer.neutral {
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
}

.stat-trend-footer.negative {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

/* Responsive pour les statistiques */
@media (max-width: 768px) {
    .quick-stats-grid-footer {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .quick-stat-item-footer {
        padding: 0.75rem;
    }

    .stat-number-footer {
        font-size: 1rem;
    }

    .stat-label-footer {
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .quick-stats-grid-footer {
        grid-template-columns: 1fr;
    }
}

/* Styles généraux */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Poppins', sans-serif;
}

/* En-tête de bienvenue */
.welcome-header {
    margin-bottom: 2rem;
}

.welcome-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 1rem;
}

.welcome-avatar {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.welcome-icon {
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.3);
}

/* Bannière d'alertes */
.alert-banner {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
}

/* Cartes de statistiques principales */
.stat-card {
    background: var(--white);
    border-radius: 1rem;
    padding: 0;
    box-shadow: var(--shadow-md);
    transition: all 0.15s ease;
    overflow: hidden;
    position: relative;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stat-card-body {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.stat-card-primary .stat-icon { background: linear-gradient(135deg, var(--primary-color), var(--primary-light)); }
.stat-card-success .stat-icon { background: linear-gradient(135deg, var(--success-color), #34d399); }
.stat-card-info .stat-icon { background: linear-gradient(135deg, var(--info-color), #22d3ee); }
.stat-card-warning .stat-icon { background: linear-gradient(135deg, var(--warning-color), #fbbf24); }

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.2;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0.25rem 0;
}

.stat-progress {
    height: 4px;
    background: var(--light-color);
    border-radius: 2px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.stat-progress .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 2px;
    transition: width 0.3s ease;
}

.stat-change {
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-change.positive { color: var(--success-color); }
.stat-change.negative { color: var(--danger-color); }
.stat-change.neutral { color: var(--text-secondary); }

/* Mini cartes statistiques */
.mini-stat-card {
    background: var(--white);
    border-radius: 1rem;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.15s ease;
    position: relative;
    overflow: hidden;
}

.mini-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.mini-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    border-radius: 1rem;
}

.bg-gradient-primary::before { background: linear-gradient(135deg, var(--primary-color), var(--primary-light)); }
.bg-gradient-success::before { background: linear-gradient(135deg, var(--success-color), #34d399); }
.bg-gradient-info::before { background: linear-gradient(135deg, var(--info-color), #22d3ee); }
.bg-gradient-warning::before { background: linear-gradient(135deg, var(--warning-color), #fbbf24); }

.mini-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--white);
    position: relative;
    z-index: 1;
}

.bg-gradient-primary .mini-stat-icon { background: linear-gradient(135deg, var(--primary-color), var(--primary-light)); }
.bg-gradient-success .mini-stat-icon { background: linear-gradient(135deg, var(--success-color), #34d399); }
.bg-gradient-info .mini-stat-icon { background: linear-gradient(135deg, var(--info-color), #22d3ee); }
.bg-gradient-warning .mini-stat-icon { background: linear-gradient(135deg, var(--warning-color), #fbbf24); }

.mini-stat-content {
    position: relative;
    z-index: 1;
}

.mini-stat-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.mini-stat-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

/* Cartes de graphiques */
.chart-card {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.15s ease;
}

.chart-card:hover {
    box-shadow: var(--shadow-lg);
}

.chart-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: between;
    align-items: flex-start;
    gap: 1rem;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.chart-subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
}

.chart-controls .btn-group .btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.chart-body {
    padding: 1rem;
}

/* Cartes d'actions rapides */
.quick-actions-card {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.quick-actions-header {
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
}

.quick-actions-header .title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.quick-actions-header .subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
}

.quick-actions-body {
    padding: 1rem 1.5rem 1.5rem;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.action-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 0.75rem;
    text-decoration: none;
    transition: all 0.15s ease;
    position: relative;
    overflow: hidden;
}

.action-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    border-radius: 0.75rem;
    transition: opacity 0.3s ease;
}

.action-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
}

.action-item:hover::before {
    opacity: 0.15;
}

.action-primary::before { background: var(--primary-color); }
.action-success::before { background: var(--success-color); }
.action-info::before { background: var(--info-color); }
.action-warning::before { background: var(--warning-color); }
.action-secondary::before { background: var(--secondary-color); }
.action-dark::before { background: var(--dark-color); }

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--white);
    position: relative;
    z-index: 1;
}

.action-primary .action-icon { background: var(--primary-color); }
.action-success .action-icon { background: var(--success-color); }
.action-info .action-icon { background: var(--info-color); }
.action-warning .action-icon { background: var(--warning-color); }
.action-secondary .action-icon { background: var(--secondary-color); }
.action-dark .action-icon { background: var(--dark-color); }

.action-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.action-content h6 {
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.action-content p {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0.125rem 0 0 0;
}

.action-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--white);
    position: relative;
    z-index: 1;
}

.badge-primary { background: var(--primary-color); }
.badge-success { background: var(--success-color); }
.badge-info { background: var(--info-color); }
.badge-warning { background: var(--warning-color); }
.badge-danger { background: var(--danger-color); }
.badge-secondary { background: var(--secondary-color); }
.badge-dark { background: var(--dark-color); }

/* Cartes d'onglets d'informations */
.info-tabs-card {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.info-tabs-header {
    padding: 1.5rem 1.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.info-tabs-header .nav-pills .nav-link {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    background: transparent;
    border: 1px solid transparent;
    transition: all 0.15s ease;
}

.info-tabs-header .nav-pills .nav-link:hover {
    background: var(--light-color);
    color: var(--text-primary);
}

.info-tabs-header .nav-pills .nav-link.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.info-tabs-body {
    padding: 1.5rem;
}

/* Tableaux de données */
.data-table {
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.data-table .table {
    margin: 0;
}

.data-table .table thead th {
    background: var(--light-color);
    border: none;
    padding: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.data-table .table tbody td {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    vertical-align: middle;
}

.data-table .table tbody tr:hover {
    background: var(--light-color);
}

.table-icon {
    width: 32px;
    height: 32px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: var(--white);
    margin-right: 0.75rem;
}

.avatar-sm {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
}

/* États vides */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--light-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-secondary);
    margin: 0 auto 1rem;
}

.empty-state h6 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

/* Cartes de données */
.data-card {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.15s ease;
}

.data-card:hover {
    box-shadow: var(--shadow-lg);
}

.data-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.data-card-header .title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.data-card-header .subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
}

.data-card-body {
    padding: 1.5rem;
}

.data-card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--light-color);
    text-align: center;
}

/* Listes de produits et utilisateurs */
.product-list, .user-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-item, .user-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.75rem;
    background: var(--light-color);
    transition: all 0.15s ease;
}

.product-item:hover, .user-item:hover {
    background: #e5e7eb;
    transform: translateX(5px);
}

.product-rank {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    font-size: 0.875rem;
}

.product-info, .user-info {
    flex: 1;
}

.product-name, .user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.product-category, .user-role {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

.user-email {
    font-size: 0.75rem;
    color: var(--text-secondary);
    display: block;
    margin-top: 0.25rem;
}

.product-stats, .user-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.product-count {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.product-progress {
    width: 60px;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
}

.product-progress .progress-bar {
    height: 100%;
    background: var(--primary-color);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--info-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1rem;
}

.user-date {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.user-status {
    font-size: 0.75rem;
}

.user-status.active {
    color: var(--success-color);
}

.user-status.inactive {
    color: var(--secondary-color);
}

/* Responsive */
@media (max-width: 768px) {
    .action-grid {
        grid-template-columns: 1fr;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .stat-card-body {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .welcome-header .row {
        text-align: center;
    }

    .welcome-icon {
        display: none;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card, .chart-card, .quick-actions-card, .info-tabs-card, .data-card {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* Styles pour la section des stocks */
.stock-summary-cards {
    margin-bottom: 1.5rem;
}

.stock-summary-card {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 12px;
    padding: 1.5rem;
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.15s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stock-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.stock-summary-card.bg-primary {
    background: linear-gradient(135deg, #4f46e5, #818cf8);
}

.stock-summary-card.bg-success {
    background: linear-gradient(135deg, #10b981, #34d399);
}

.stock-summary-card.bg-warning {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.stock-summary-card.bg-danger {
    background: linear-gradient(135deg, #ef4444, #f87171);
}

.stock-summary-icon {
    font-size: 2rem;
    opacity: 0.9;
}

.stock-summary-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.stock-summary-content p {
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
    opacity: 0.9;
}

/* Style spécial pour le tonnage qui peut être plus long */
#total-tonnage-display {
    font-size: 1.25rem !important;
    word-break: break-word;
}

.stock-summary-content p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.stock-table-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stock-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stock-controls .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.15s ease;
}

.stock-controls .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#stock-table {
    margin-top: 1rem;
}

#stock-table th {
    background-color: #f8fafc;
    border: none;
    font-weight: 600;
    color: #374151;
    padding: 1rem 0.75rem;
    font-size: 0.875rem;
}

#stock-table td {
    padding: 1rem 0.75rem;
    border: none;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: middle;
}

#stock-table tbody tr:hover {
    background-color: #f9fafb;
}

.table-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: white;
    font-size: 0.875rem;
}

/* Styles pour les étiquettes de stock avec taille réduite */
.badge {
    font-size: 0.75rem !important;
    font-weight: 600;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.15s ease;
    padding: 0.25rem 0.5rem !important;
    white-space: nowrap;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
}

.badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.badge i {
    font-size: 0.7rem !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107, #f39c12) !important;
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #212529 !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #198754, #27ae60) !important;
    border: 1px solid rgba(25, 135, 84, 0.3);
}

.badge.bg-light {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #495057 !important;
}

/* Animation pour l'actualisation */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.fa-spinner.fa-spin {
    animation: spin 1s linear infinite;
}

/* Responsive pour les cartes de résumé des stocks */
@media (max-width: 768px) {
    .stock-summary-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .stock-summary-content h4 {
        font-size: 1.25rem;
    }

    .stock-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .stock-controls .btn {
        width: 100%;
    }
}
</style>
@endpush

@push("scripts")
<script src="/js/error-suppression.js"></script>
<!-- Optimisations de performance -->
<script>
// Désactiver les extensions qui interfèrent
if (window.chrome && window.chrome.runtime) {
    console.log("🛡️ Extensions détectées - Optimisations activées");
}

// Optimiser les performances
window.addEventListener("beforeunload", function() {
    // Nettoyer les timers
    for (let i = 1; i < 99999; i++) {
        window.clearTimeout(i);
        window.clearInterval(i);
    }
});
</script>

<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>

<!-- Script de correctif pour restaurer les graphiques -->
<script src="{{ asset('js/dashboard-charts-fix.js') }}"></script>

<script>
// GRADIS Charts - Version ultra-optimisée sans erreurs
(function() {
    "use strict";
    
    console.log("🚀 GRADIS Charts v4.0 - Ultra-optimisé");
    
    // Supprimer toutes les sources d'erreurs
    window.onerror = function(msg, url, line, col, error) {
        if (msg.includes("inpage.js") || msg.includes("Cannot read properties of null")) {
            return true; // Supprimer ces erreurs
        }
    };
    
    // Fonction d'initialisation ultra-rapide
    function initChartsUltraFast() {
        console.log("⚡ Initialisation ultra-rapide des graphiques");
        
        if (typeof ApexCharts === "undefined") {
            console.warn("⚠️ ApexCharts non disponible - Chargement différé");
            setTimeout(initChartsUltraFast, 100);
            return;
        }
        
        console.log("✅ ApexCharts prêt");
        
        // Données optimisées
        const data = [15, 25, 20, 30, 28, 35];
        const months = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"];
        const categories = [45, 30, 15, 10];
        const labels = ["Ciment", "Fer", "Sable", "Gravier"];
        
        // Fonction de création ultra-rapide
        function fastChart(id, config, name) {
            const el = document.getElementById(id);
            if (!el) return;
            
            try {
                el.innerHTML = "";
                new ApexCharts(el, config).render();
                console.log(`✅ ${name} OK`);
            } catch (e) {
                console.warn(`⚠️ ${name} erreur:`, e.message);
            }
        }
        
        // Créer tous les graphiques rapidement
        setTimeout(() => fastChart("revenueChart", {
            series: [{ name: "Revenus", data: data }],
            chart: { type: "area", height: 350, toolbar: { show: false }, animations: { enabled: false } },
            xaxis: { categories: months },
            colors: ["#007bff"],
            fill: { type: "gradient", gradient: { opacityFrom: 0.7, opacityTo: 0.3 } }
        }, "Revenus"), 200);
        
        setTimeout(() => fastChart("resourcesChart", {
            series: [{ name: "Ressources", data: data }],
            chart: { type: "bar", height: 350, toolbar: { show: false }, animations: { enabled: false } },
            xaxis: { categories: months },
            colors: ["#28a745"]
        }, "Ressources"), 400);
        
        setTimeout(() => fastChart("categoryRevenueChart", {
            series: categories,
            chart: { type: "donut", height: 300, animations: { enabled: false } },
            labels: labels,
            colors: ["#007bff", "#28a745", "#ffc107", "#dc3545"],
            legend: { position: "bottom" }
        }, "Catégories"), 600);
        
        setTimeout(() => fastChart("cementOrdersChart", {
            series: [{ name: "Tonnage", data: [100, 150, 120, 180, 200, 160] }],
            chart: { type: "bar", height: 300, toolbar: { show: false }, animations: { enabled: false } },
            xaxis: { categories: months },
            colors: ["#17a2b8"]
        }, "Ciment"), 800);
        
        console.log("🎯 Tous les graphiques programmés (ultra-rapide)");
    }
    
    // Démarrage immédiat
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", initChartsUltraFast);
    } else {
        initChartsUltraFast();
    }
    
})()

// Fonction de restauration manuelle des graphiques
function restoreChartsManually() {
    console.log('🔧 Restauration manuelle des graphiques...');

    if (window.DashboardChartsFix) {
        window.DashboardChartsFix.restore();
        document.getElementById('restoreChartsBtn').innerHTML = '<i class="fas fa-check me-1"></i> Graphiques restaurés';
        document.getElementById('restoreChartsBtn').classList.remove('btn-outline-primary');
        document.getElementById('restoreChartsBtn').classList.add('btn-success');

        setTimeout(() => {
            document.getElementById('restoreChartsBtn').style.display = 'none';
        }, 3000);
    } else {
        console.error('❌ Script de correctif non disponible');
        alert('Erreur: Script de correctif non disponible. Rechargez la page.');
    }
}

// Afficher le bouton de restauration si les graphiques ne se chargent pas
setTimeout(() => {
    const charts = ['revenueChart', 'resourcesChart', 'categoryRevenueChart', 'cementOrdersChart'];
    let emptyCharts = 0;

    charts.forEach(id => {
        const element = document.getElementById(id);
        if (element && element.innerHTML.trim() === '') {
            emptyCharts++;
        }
    });

    if (emptyCharts > 0) {
        console.warn('⚠️ Graphiques vides détectés, affichage du bouton de restauration');
        document.getElementById('restoreChartsBtn').style.display = 'inline-block';
    }
}, 3000);

</script>
@endpush
