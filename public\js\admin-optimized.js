/**
 * GRADIS Admin - Scripts Optimisés
 * Remplace les setTimeout/setInterval bloquants par des solutions performantes
 * Version: 1.0 - Optimisé pour performance
 */

// Configuration globale d'optimisation
const ADMIN_CONFIG = {
    // Délais réduits pour les animations
    ANIMATION_DELAY: 50,
    NOTIFICATION_DELAY: 100,
    SEARCH_DEBOUNCE: 200,
    
    // Auto-refresh désactivé par défaut
    AUTO_REFRESH_ENABLED: false,
    AUTO_REFRESH_INTERVAL: 300000, // 5 minutes au lieu de 30 secondes
    
    // Optimisations CSS
    USE_CSS_ANIMATIONS: true,
    REDUCE_MOTION: false
};

/**
 * Remplace setTimeout avec des délais optimisés
 */
function optimizedTimeout(callback, delay = 0) {
    if (delay > 1000) {
        console.warn('Délai setTimeout trop long détecté:', delay, 'ms - Réduit à 100ms');
        delay = 100;
    }
    return setTimeout(callback, Math.min(delay, 100));
}

/**
 * Animation CSS optimisée pour remplacer les animations JavaScript
 */
function animateElement(element, animationType = 'fadeIn') {
    if (!element) return;
    
    const animations = {
        fadeIn: 'opacity: 1; transform: translateY(0); transition: all 0.3s ease;',
        slideUp: 'opacity: 1; transform: translateY(0); transition: all 0.3s ease;',
        pulse: 'animation: pulse 0.5s ease-in-out;'
    };
    
    element.style.cssText += animations[animationType] || animations.fadeIn;
}

/**
 * Notification optimisée sans délais
 */
function showOptimizedNotification(message, type = 'info', duration = 3000) {
    // Utilise les notifications natives du navigateur si disponibles
    if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('GRADIS Admin', {
            body: message,
            icon: '/favicon.ico'
        });
        return;
    }
    
    // Fallback vers SweetAlert2 avec délai optimisé
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            text: message,
            icon: type,
            timer: Math.min(duration, 3000),
            showConfirmButton: false,
            position: 'top-end',
            toast: true
        });
    }
}

/**
 * Recherche optimisée avec debounce réduit
 */
function createOptimizedSearch(inputElement, callback) {
    if (!inputElement || typeof callback !== 'function') return;
    
    let searchTimeout;
    inputElement.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            callback(this.value);
        }, ADMIN_CONFIG.SEARCH_DEBOUNCE);
    });
}

/**
 * Chargement optimisé des tableaux
 */
function optimizeTableLoading() {
    const tables = document.querySelectorAll('table tbody tr');
    tables.forEach((row, index) => {
        // Animation CSS pure au lieu de setTimeout
        row.style.cssText = `
            opacity: 1;
            transform: translateY(0);
            transition: all 0.3s ease ${index * 20}ms;
        `;
    });
}

/**
 * Auto-refresh intelligent (désactivé par défaut)
 */
class OptimizedAutoRefresh {
    constructor(callback, interval = ADMIN_CONFIG.AUTO_REFRESH_INTERVAL) {
        this.callback = callback;
        this.interval = interval;
        this.intervalId = null;
        this.isActive = false;
        this.isVisible = true;
        
        // Écouter la visibilité de la page
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            if (!this.isVisible && this.isActive) {
                this.pause();
            } else if (this.isVisible && ADMIN_CONFIG.AUTO_REFRESH_ENABLED) {
                this.resume();
            }
        });
    }
    
    start() {
        if (!ADMIN_CONFIG.AUTO_REFRESH_ENABLED) {
            console.log('Auto-refresh désactivé pour optimiser les performances');
            return;
        }
        
        this.isActive = true;
        this.intervalId = setInterval(() => {
            if (this.isVisible && typeof this.callback === 'function') {
                this.callback();
            }
        }, this.interval);
    }
    
    pause() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    resume() {
        if (this.isActive && !this.intervalId) {
            this.start();
        }
    }
    
    stop() {
        this.pause();
        this.isActive = false;
    }
}

/**
 * Optimisation globale au chargement de la page
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 GRADIS Admin - Scripts optimisés chargés');
    
    // Optimiser les tableaux existants
    optimizeTableLoading();
    
    // Remplacer les animations JavaScript par du CSS
    const elementsToAnimate = document.querySelectorAll('.animate-on-load, .table-row-hover');
    elementsToAnimate.forEach(element => {
        animateElement(element, 'fadeIn');
    });
    
    // Optimiser les formulaires
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            // Désactiver le bouton de soumission immédiatement
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Traitement...';
            }
        });
    });
    
    // Notification d'optimisation
    showOptimizedNotification('Interface admin optimisée - Performances améliorées', 'success', 2000);
});

/**
 * Styles CSS pour les animations optimisées
 */
const optimizedStyles = `
<style>
/* Animations CSS optimisées */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse-animation {
    animation: pulse 2s infinite ease-in-out;
}

.fade-in-animation {
    animation: fadeIn 0.3s ease-out;
}

/* Optimisations pour les performances */
.table-row-hover {
    transition: all 0.2s ease !important;
}

.btn {
    transition: all 0.2s ease !important;
}

/* Réduction des animations si préféré par l'utilisateur */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
</style>
`;

// Injecter les styles optimisés
document.head.insertAdjacentHTML('beforeend', optimizedStyles);

/**
 * Nettoyage global - Stoppe tous les processus JavaScript en arrière-plan
 */
class BackgroundProcessCleaner {
    constructor() {
        this.intervals = new Set();
        this.timeouts = new Set();
        this.fetchControllers = new Set();
        this.originalSetInterval = window.setInterval;
        this.originalSetTimeout = window.setTimeout;
        this.originalFetch = window.fetch;
        
        this.interceptBackgroundProcesses();
    }
    
    interceptBackgroundProcesses() {
        const self = this;
        
        // Intercepter tous les setInterval
        window.setInterval = function(callback, delay) {
            console.warn('⚠️ setInterval détecté - Bloqué pour optimisation:', delay, 'ms');
            // Bloquer tous les intervals sauf ceux vraiment nécessaires
            if (delay < 10000) { // Bloquer les intervals de moins de 10 secondes
                console.log('🚫 setInterval bloqué (délai trop court):', delay, 'ms');
                return null;
            }
            const id = self.originalSetInterval.call(this, callback, delay);
            self.intervals.add(id);
            return id;
        };
        
        // Intercepter tous les setTimeout longs
        window.setTimeout = function(callback, delay) {
            if (delay > 1000) {
                console.warn('⚠️ setTimeout long détecté - Réduit:', delay, 'ms → 100ms');
                delay = 100;
            }
            const id = self.originalSetTimeout.call(this, callback, delay);
            self.timeouts.add(id);
            return id;
        };
        
        // Intercepter fetch pour éviter les requêtes multiples
        window.fetch = function(...args) {
            const controller = new AbortController();
            self.fetchControllers.add(controller);
            
            const options = args[1] || {};
            options.signal = controller.signal;
            
            return self.originalFetch.call(this, args[0], options)
                .finally(() => {
                    self.fetchControllers.delete(controller);
                });
        };
    }
    
    clearAllBackgroundProcesses() {
        console.log('🧹 Nettoyage de tous les processus en arrière-plan...');
        
        // Stopper tous les intervals
        this.intervals.forEach(id => {
            if (id) {
                clearInterval(id);
                console.log('❌ Interval arrêté:', id);
            }
        });
        this.intervals.clear();
        
        // Stopper tous les timeouts
        this.timeouts.forEach(id => {
            if (id) {
                clearTimeout(id);
            }
        });
        this.timeouts.clear();
        
        // Annuler toutes les requêtes fetch en cours
        this.fetchControllers.forEach(controller => {
            controller.abort();
        });
        this.fetchControllers.clear();
        
        console.log('✅ Tous les processus en arrière-plan ont été stoppés');
    }
    
    // Méthode pour nettoyer périodiquement
    startPeriodicCleanup() {
        // Nettoyage toutes les 30 secondes
        const cleanupId = this.originalSetInterval.call(window, () => {
            this.clearAllBackgroundProcesses();
        }, 30000);
        
        console.log('🔄 Nettoyage périodique activé (30s)');
        return cleanupId;
    }
}

// Initialiser le nettoyeur de processus
const backgroundCleaner = new BackgroundProcessCleaner();

// Nettoyer immédiatement au chargement
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        backgroundCleaner.clearAllBackgroundProcesses();
    }, 2000); // Attendre 2s que tous les scripts se chargent
});

// Nettoyer avant de quitter la page
window.addEventListener('beforeunload', function() {
    backgroundCleaner.clearAllBackgroundProcesses();
});

// Nettoyer quand la page devient invisible
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        backgroundCleaner.clearAllBackgroundProcesses();
    }
});

// Export pour utilisation dans d'autres scripts
window.AdminOptimized = {
    config: ADMIN_CONFIG,
    optimizedTimeout,
    animateElement,
    showOptimizedNotification,
    createOptimizedSearch,
    OptimizedAutoRefresh,
    backgroundCleaner
};
