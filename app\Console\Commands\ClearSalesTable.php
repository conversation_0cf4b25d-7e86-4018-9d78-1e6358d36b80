<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Sale;
use App\Models\Payment;
use App\Models\SaleItem;
use App\Models\PaymentSchedule;

class ClearSalesTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sales:clear {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vide complètement la table sales et ses tables liées (suppression définitive)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== VIDAGE DE LA TABLE SALES ===');

        // Compter les enregistrements
        $salesCount = Sale::withTrashed()->count();
        $paymentsCount = Payment::withTrashed()->count();

        // Vérifier si les tables existent avant de compter
        $saleItemsCount = 0;
        $paymentSchedulesCount = 0;

        try {
            if (DB::getSchemaBuilder()->hasTable('sale_items')) {
                $saleItemsCount = SaleItem::withTrashed()->count();
            }
        } catch (\Exception $e) {
            $this->warn('Table sale_items non trouvée, ignorée.');
        }

        try {
            if (DB::getSchemaBuilder()->hasTable('payment_schedules')) {
                $paymentSchedulesCount = PaymentSchedule::withTrashed()->count();
            }
        } catch (\Exception $e) {
            $this->warn('Table payment_schedules non trouvée, ignorée.');
        }

        if ($salesCount === 0) {
            $this->info('✅ La table sales est déjà vide.');
            return 0;
        }

        $this->info("Nombre total de ventes trouvées : {$salesCount}");
        $this->info("Nombre total de paiements liés : {$paymentsCount}");
        $this->info("Nombre total d'articles de vente : {$saleItemsCount}");
        $this->info("Nombre total d'échéanciers : {$paymentSchedulesCount}");

        // Demander confirmation si --force n'est pas utilisé
        if (!$this->option('force')) {
            $this->warn('⚠️  ATTENTION : Cette action va supprimer DÉFINITIVEMENT :');
            $this->warn("   - {$salesCount} ventes");
            $this->warn("   - {$paymentsCount} paiements liés");
            $this->warn("   - {$saleItemsCount} articles de vente");
            $this->warn("   - {$paymentSchedulesCount} échéanciers de paiement");
            
            if (!$this->confirm('Êtes-vous sûr de vouloir continuer ?')) {
                $this->info('❌ Opération annulée.');
                return 0;
            }
        }

        try {
            $this->clearSales($salesCount, $paymentsCount, $saleItemsCount, $paymentSchedulesCount);
            $this->info('✅ Opération terminée avec succès !');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la suppression : ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function clearSales($salesCount, $paymentsCount, $saleItemsCount, $paymentSchedulesCount)
    {
        $this->info('🔄 Vidage de la table sales et tables liées...');
        
        try {
            // Étape 1: Supprimer les paiements liés
            if ($paymentsCount > 0) {
                $this->info('1. Suppression des paiements...');
                Payment::withTrashed()->forceDelete();
                $this->info("   → {$paymentsCount} paiements supprimés");
            }

            // Étape 2: Supprimer les articles de vente (si la table existe)
            if ($saleItemsCount > 0 && DB::getSchemaBuilder()->hasTable('sale_items')) {
                $this->info('2. Suppression des articles de vente...');
                SaleItem::withTrashed()->forceDelete();
                $this->info("   → {$saleItemsCount} articles supprimés");
            }

            // Étape 3: Supprimer les échéanciers de paiement (si la table existe)
            if ($paymentSchedulesCount > 0 && DB::getSchemaBuilder()->hasTable('payment_schedules')) {
                $this->info('3. Suppression des échéanciers...');
                PaymentSchedule::withTrashed()->forceDelete();
                $this->info("   → {$paymentSchedulesCount} échéanciers supprimés");
            }

            // Étape 4: Supprimer les ventes
            $this->info('4. Suppression des ventes...');
            Sale::withTrashed()->forceDelete();

            // Étape 5: Reset des auto-increments
            $this->info('5. Reset des auto-increments...');
            DB::statement('ALTER TABLE sales AUTO_INCREMENT = 1');
            if ($paymentsCount > 0) {
                DB::statement('ALTER TABLE payments AUTO_INCREMENT = 1');
            }
            if ($saleItemsCount > 0 && DB::getSchemaBuilder()->hasTable('sale_items')) {
                DB::statement('ALTER TABLE sale_items AUTO_INCREMENT = 1');
            }
            if ($paymentSchedulesCount > 0 && DB::getSchemaBuilder()->hasTable('payment_schedules')) {
                DB::statement('ALTER TABLE payment_schedules AUTO_INCREMENT = 1');
            }

            $this->info("✅ {$salesCount} ventes supprimées définitivement");
            if ($paymentsCount > 0) {
                $this->info("✅ {$paymentsCount} paiements supprimés définitivement");
            }
            if ($saleItemsCount > 0) {
                $this->info("✅ {$saleItemsCount} articles de vente supprimés définitivement");
            }
            if ($paymentSchedulesCount > 0) {
                $this->info("✅ {$paymentSchedulesCount} échéanciers supprimés définitivement");
            }
            $this->info('✅ Auto-increments remis à 1');
            
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
