<?php

/**
 * Script de test des performances pour les pages admin
 */

require_once __DIR__ . '/../vendor/autoload.php';

class PerformanceTest
{
    private $baseUrl;
    private $results = [];
    
    public function __construct($baseUrl = 'http://127.0.0.1:8000')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }
    
    /**
     * Teste les performances des pages principales
     */
    public function run()
    {
        $this->log("🚀 Test de performance des pages admin...");
        $this->log("URL de base: {$this->baseUrl}");
        $this->log("");
        
        $pages = [
            'Dashboard Admin' => '/admin/dashboard',
            'Page Ventes' => '/admin/sales',
            'Page Accueil' => '/',
        ];
        
        foreach ($pages as $name => $path) {
            $this->testPage($name, $path);
        }
        
        $this->displaySummary();
    }
    
    /**
     * Teste une page spécifique
     */
    private function testPage($name, $path)
    {
        $url = $this->baseUrl . $path;
        $this->log("🔍 Test: {$name}");
        $this->log("   URL: {$url}");
        
        $startTime = microtime(true);
        
        // Configuration cURL
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_USERAGENT => 'Performance Test Script',
            CURLOPT_HEADER => true,
            CURLOPT_NOBODY => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
        $connectTime = curl_getinfo($ch, CURLINFO_CONNECT_TIME);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $contentLength = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        $endTime = microtime(true);
        $realTime = $endTime - $startTime;
        
        // Analyser la réponse
        $result = [
            'name' => $name,
            'url' => $url,
            'http_code' => $httpCode,
            'total_time' => $totalTime,
            'connect_time' => $connectTime,
            'real_time' => $realTime,
            'content_type' => $contentType,
            'content_length' => $contentLength,
            'error' => $error,
            'success' => empty($error) && $httpCode >= 200 && $httpCode < 400
        ];
        
        // Analyser les headers
        if ($response) {
            $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $headers = substr($response, 0, $headerSize);
            $body = substr($response, $headerSize);
            
            $result['has_content_encoding'] = strpos($headers, 'Content-Encoding:') !== false;
            $result['has_cache_control'] = strpos($headers, 'Cache-Control:') !== false;
            $result['has_debug_header'] = strpos($headers, 'X-Debug-Mode:') !== false;
            $result['body_size'] = strlen($body);
            
            // Vérifier les erreurs spécifiques
            if (strpos($body, 'ERR_CONTENT_DECODING_FAILED') !== false) {
                $result['has_decoding_error'] = true;
            }
            
            if (strpos($body, 'Whoops') !== false || strpos($body, 'Exception') !== false) {
                $result['has_exception'] = true;
            }
        }
        
        $this->results[] = $result;
        $this->displayResult($result);
    }
    
    /**
     * Affiche le résultat d'un test
     */
    private function displayResult($result)
    {
        if ($result['success']) {
            $status = "✅ OK";
            $color = "\033[32m"; // Vert
        } else {
            $status = "❌ ERREUR";
            $color = "\033[31m"; // Rouge
        }
        
        $resetColor = "\033[0m";
        
        $this->log("   {$color}{$status}{$resetColor}");
        $this->log("   Code HTTP: {$result['http_code']}");
        $this->log("   Temps total: " . number_format($result['real_time'] * 1000, 2) . " ms");
        
        if ($result['content_length'] > 0) {
            $this->log("   Taille: " . $this->formatBytes($result['content_length']));
        } elseif (isset($result['body_size'])) {
            $this->log("   Taille: " . $this->formatBytes($result['body_size']));
        }
        
        if (!empty($result['error'])) {
            $this->log("   ⚠️  Erreur: {$result['error']}");
        }
        
        if (isset($result['has_decoding_error']) && $result['has_decoding_error']) {
            $this->log("   ⚠️  Erreur de décodage détectée!");
        }
        
        if (isset($result['has_exception']) && $result['has_exception']) {
            $this->log("   ⚠️  Exception détectée dans la réponse!");
        }
        
        if (isset($result['has_debug_header']) && $result['has_debug_header']) {
            $this->log("   🔧 Mode debug activé");
        }
        
        $this->log("");
    }
    
    /**
     * Affiche le résumé des tests
     */
    private function displaySummary()
    {
        $this->log("📊 RÉSUMÉ DES TESTS");
        $this->log("==================");
        
        $totalTests = count($this->results);
        $successfulTests = array_filter($this->results, function($r) { return $r['success']; });
        $successCount = count($successfulTests);
        $failureCount = $totalTests - $successCount;
        
        $this->log("Total des tests: {$totalTests}");
        $this->log("Succès: {$successCount}");
        $this->log("Échecs: {$failureCount}");
        
        if ($successCount > 0) {
            $avgTime = array_sum(array_column($successfulTests, 'real_time')) / $successCount;
            $this->log("Temps moyen: " . number_format($avgTime * 1000, 2) . " ms");
        }
        
        $this->log("");
        
        if ($failureCount > 0) {
            $this->log("❌ PROBLÈMES DÉTECTÉS:");
            foreach ($this->results as $result) {
                if (!$result['success']) {
                    $this->log("   - {$result['name']}: {$result['error']} (Code: {$result['http_code']})");
                }
            }
            $this->log("");
            $this->log("💡 SOLUTIONS RECOMMANDÉES:");
            $this->log("   1. Vérifiez que le serveur de développement est démarré");
            $this->log("   2. Exécutez: php scripts/clear-dev-cache.php");
            $this->log("   3. Redémarrez le serveur: php artisan serve");
            $this->log("   4. Vérifiez les logs: tail -f storage/logs/laravel.log");
        } else {
            $this->log("🎉 TOUS LES TESTS SONT PASSÉS!");
            $this->log("   L'application fonctionne correctement.");
        }
    }
    
    /**
     * Formate les bytes en unités lisibles
     */
    private function formatBytes($bytes)
    {
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
    
    /**
     * Log un message
     */
    private function log($message)
    {
        echo $message . "\n";
    }
}

// Exécuter le test
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://127.0.0.1:8000';
    
    $tester = new PerformanceTest($baseUrl);
    $tester->run();
} else {
    echo "Ce script doit être exécuté en ligne de commande.\n";
    echo "Usage: php test-performance.php [http://127.0.0.1:8000]\n";
    exit(1);
}
