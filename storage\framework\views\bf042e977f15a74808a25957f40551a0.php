<?php
// Protection contre les variables manquantes
$supplyStats = $supplyStats ?? [
    'totalSupplies' => 0,
    'validatedSupplies' => 0,
    'pendingSupplies' => 0,
    'rejectedSupplies' => 0,
    'totalTonnage' => 0,
    'validatedTonnage' => 0,
    'pendingTonnage' => 0,
    'rejectedTonnage' => 0,
    'validatedPercentage' => 0,
    'pendingPercentage' => 0,
    'rejectedPercentage' => 0,
    'totalSupplyAmount' => 0
];
?>

<!-- Section des statistiques d'approvisionnement -->
<div class="row g-4 mb-4 sequential-fade-in">
    <!-- Total des approvisionnements -->
    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
        <div class="stat-card">
            <div class="stat-icon info">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stat-title">Total approvisionnements</div>
            <div class="stat-value"><?php echo e(number_format($supplyStats['totalSupplies'] ?? 0)); ?></div>
            <div class="stat-subtitle">Montant: <?php echo e(number_format($supplyStats['totalSupplyAmount'] ?? 0, 0, ',', ' ')); ?> F</div>
        </div>
    </div>

    <!-- Tonnage total -->
    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
        <div class="stat-card">
            <div class="stat-icon primary">
                <i class="fas fa-weight-hanging"></i>
            </div>
            <div class="stat-title">Tonnage total</div>
            <div class="stat-value"><?php echo e(number_format($supplyStats['totalTonnage'] ?? 0, 1, ',', ' ')); ?> T</div>
            <div class="stat-subtitle">Répartition par produits</div>
        </div>
    </div>

    <!-- Approvisionnements validés -->
    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
        <div class="stat-card">
            <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-title">Approvisionnements validés</div>
            <div class="stat-value"><?php echo e(number_format($supplyStats['validatedSupplies'] ?? 0)); ?></div>
            <div class="stat-subtitle">
                <div class="progress-bar-container mt-2">
                    <div class="progress-bar success" style="width: <?php echo e(round($supplyStats['validatedPercentage'] ?? 0)); ?>%;"></div>
                </div>
                <div class="progress-text">
                    <span><?php echo e(round($supplyStats['validatedPercentage'] ?? 0)); ?>%</span>
                    <span><?php echo e($supplyStats['totalSupplies'] ?? 0); ?> total</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Approvisionnements en attente -->
    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
        <div class="stat-card">
            <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-title">En attente de validation</div>
            <div class="stat-value"><?php echo e(number_format($supplyStats['pendingSupplies'] ?? 0)); ?></div>
            <div class="stat-subtitle">
                <div class="progress-bar-container mt-2">
                    <div class="progress-bar warning" style="width: <?php echo e(round($supplyStats['pendingPercentage'] ?? 0)); ?>%;"></div>
                </div>
                <div class="progress-text">
                    <span><?php echo e(round($supplyStats['pendingPercentage'] ?? 0)); ?>%</span>
                    <span><?php echo e($supplyStats['totalSupplies'] ?? 0); ?> total</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Deuxième rangée : Statistiques de tonnage détaillées -->
<div class="row g-4 mb-4 sequential-fade-in">
    <!-- Tonnage validé -->
    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
        <div class="stat-card">
            <div class="stat-icon success">
                <i class="fas fa-check-double"></i>
            </div>
            <div class="stat-title">Tonnage validé</div>
            <div class="stat-value"><?php echo e(number_format(($supplyStats['validatedTonnage'] ?? 0), 1, ',', ' ')); ?> T</div>
            <div class="stat-subtitle">
                <div class="progress-bar-container mt-2">
                    <div class="progress-bar success" style="width: <?php echo e((isset($supplyStats['totalTonnage']) && $supplyStats['totalTonnage'] > 0) ? round((($supplyStats['validatedTonnage'] ?? 0) / $supplyStats['totalTonnage']) * 100) : 0); ?>%;"></div>
                </div>
                <div class="progress-text">
                    <span><?php echo e((isset($supplyStats['totalTonnage']) && $supplyStats['totalTonnage'] > 0) ? round((($supplyStats['validatedTonnage'] ?? 0) / $supplyStats['totalTonnage']) * 100) : 0); ?>% du tonnage total</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Total rejeté -->
    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
        <div class="stat-card">
            <div class="stat-icon danger">
                <i class="fas fa-ban"></i>
            </div>
            <div class="stat-title">Total rejeté</div>
            <div class="stat-value"><?php echo e(number_format($supplyStats['rejectedTonnage'] ?? 0, 1, ',', ' ')); ?> T</div>
            <div class="stat-subtitle">
                <div class="progress-bar-container mt-2">
                    <div class="progress-bar danger" style="width: <?php echo e(round($supplyStats['rejectedPercentage'] ?? 0)); ?>%;"></div>
                </div>
                <div class="progress-text">
                    <span><?php echo e(round($supplyStats['rejectedPercentage'] ?? 0)); ?>% du tonnage total</span>
                    <span>sur <?php echo e(number_format($supplyStats['rejectedSupplies'] ?? 0)); ?> commandes</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tonnage en attente -->
    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6">
        <div class="stat-card">
            <div class="stat-icon warning">
                <i class="fas fa-hourglass-half"></i>
            </div>
            <div class="stat-title">Tonnage en attente</div>
            <div class="stat-value"><?php echo e(number_format($supplyStats['pendingTonnage'] ?? 0, 1, ',', ' ')); ?> T</div>
            <div class="stat-subtitle">
                <div class="progress-bar-container mt-2">
                    <div class="progress-bar warning" style="width: <?php echo e(($supplyStats['totalTonnage'] ?? 0) > 0 ? round((($supplyStats['pendingTonnage'] ?? 0) / $supplyStats['totalTonnage']) * 100) : 0); ?>%;"></div>
                </div>
                <div class="progress-text">
                    <span><?php echo e(($supplyStats['totalTonnage'] ?? 0) > 0 ? round((($supplyStats['pendingTonnage'] ?? 0) / $supplyStats['totalTonnage']) * 100) : 0); ?>% du tonnage total</span>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/dashboard-section-approvisionnements.blade.php ENDPATH**/ ?>