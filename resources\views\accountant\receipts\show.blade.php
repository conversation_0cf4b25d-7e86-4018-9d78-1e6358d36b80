@extends('layouts.app')

@section('title', 'Reçu de paiement')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card" id="receipt-content">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        REÇU DE PAIEMENT
                    </h4>
                    <small>Référence: {{ $payment->reference }}</small>
                </div>
                
                <div class="card-body">
                    <!-- En-tête de l'entreprise -->
                    <div class="text-center mb-4">
                        <h3 class="text-primary">GRADIS</h3>
                        <p class="mb-1">Système de Gestion des Recouvrements</p>
                        <p class="mb-0 text-muted">Adresse de l'entreprise • Téléphone • Email</p>
                    </div>
                    
                    <hr>
                    
                    <!-- Informations du paiement -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-primary">Informations du client</h6>
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>Nom:</strong></td>
                                    <td>{{ $payment->sale->customer_name }}</td>
                                </tr>
                                @if($payment->sale->customer_phone)
                                <tr>
                                    <td><strong>Téléphone:</strong></td>
                                    <td>{{ $payment->sale->customer_phone }}</td>
                                </tr>
                                @endif
                                @if($payment->sale->customer_address)
                                <tr>
                                    <td><strong>Adresse:</strong></td>
                                    <td>{{ $payment->sale->customer_address }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-primary">Détails du paiement</h6>
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>Date:</strong></td>
                                    <td>{{ $payment->payment_date ? $payment->payment_date->format('d/m/Y') : $payment->created_at->format('d/m/Y') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Heure:</strong></td>
                                    <td>{{ $payment->created_at->format('H:i') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Caissier:</strong></td>
                                    <td>{{ $payment->cashier->name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Poste:</strong></td>
                                    <td>{{ $payment->position ?? 'Comptable' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Détails de la vente -->
                    <div class="mb-4">
                        <h6 class="text-primary">Détails de la vente</h6>
                        <table class="table table-bordered">
                            <tr>
                                <td><strong>Référence de la vente:</strong></td>
                                <td>{{ $payment->sale->reference ?? 'VENTE-' . $payment->sale->id }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date de la vente:</strong></td>
                                <td>{{ $payment->sale->created_at->format('d/m/Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Montant total de la vente:</strong></td>
                                <td class="text-end"><strong>{{ number_format($payment->sale->total_amount, 0, ',', ' ') }} FCFA</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Montant déjà payé:</strong></td>
                                <td class="text-end">{{ number_format($payment->sale->amount_paid, 0, ',', ' ') }} FCFA</td>
                            </tr>
                            <tr>
                                <td><strong>Montant restant dû:</strong></td>
                                <td class="text-end">{{ number_format($payment->sale->total_amount - $payment->sale->amount_paid, 0, ',', ' ') }} FCFA</td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- Détails du paiement actuel -->
                    <div class="mb-4">
                        <h6 class="text-primary">Ce paiement</h6>
                        <table class="table table-bordered">
                            <tr>
                                <td><strong>Montant payé:</strong></td>
                                <td class="text-end"><strong class="text-success">{{ number_format($payment->amount, 0, ',', ' ') }} FCFA</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Méthode de paiement:</strong></td>
                                <td>
                                    @if($payment->payment_method == 'cash')
                                        <span class="badge bg-success"><i class="fas fa-money-bill-wave me-1"></i>Espèces</span>
                                    @elseif($payment->payment_method == 'bank_transfer')
                                        <span class="badge bg-primary"><i class="fas fa-university me-1"></i>Virement bancaire</span>
                                    @elseif($payment->payment_method == 'check')
                                        <span class="badge bg-info"><i class="fas fa-money-check-alt me-1"></i>Chèque</span>
                                    @elseif($payment->payment_method == 'mobile_money')
                                        <span class="badge bg-warning"><i class="fas fa-mobile-alt me-1"></i>Mobile Money</span>
                                    @endif
                                </td>
                            </tr>
                            @if($payment->reference_number)
                            <tr>
                                <td><strong>Numéro de référence:</strong></td>
                                <td>{{ $payment->reference_number }}</td>
                            </tr>
                            @endif
                            @if($payment->notes)
                            <tr>
                                <td><strong>Notes:</strong></td>
                                <td>{{ $payment->notes }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                    
                    <!-- Statut du paiement -->
                    <div class="text-center mb-4">
                        @if($payment->status == 'completed')
                            <span class="badge bg-success fs-6 p-3">
                                <i class="fas fa-check-circle me-2"></i>PAIEMENT CONFIRMÉ
                            </span>
                        @elseif($payment->status == 'pending')
                            <span class="badge bg-warning fs-6 p-3">
                                <i class="fas fa-clock me-2"></i>PAIEMENT EN ATTENTE
                            </span>
                        @else
                            <span class="badge bg-danger fs-6 p-3">
                                <i class="fas fa-times-circle me-2"></i>PAIEMENT ÉCHOUÉ
                            </span>
                        @endif
                    </div>
                    
                    <!-- Signature -->
                    <div class="row mt-5">
                        <div class="col-md-6">
                            <div class="text-center">
                                <p class="mb-4">Signature du client</p>
                                <div style="border-bottom: 1px solid #000; height: 60px;"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <p class="mb-4">Signature du caissier</p>
                                <div style="border-bottom: 1px solid #000; height: 60px;"></div>
                                <small class="text-muted mt-2 d-block">{{ $payment->cashier->name ?? 'N/A' }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pied de page -->
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            Ce reçu a été généré automatiquement le {{ now()->format('d/m/Y à H:i') }}
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Boutons d'action (cachés lors de l'impression) -->
            @unless($isPrint ?? false)
            <div class="text-center mt-3 no-print">
                <button type="button" class="btn btn-primary me-2" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>Imprimer
                </button>
                <button type="button" class="btn btn-secondary" onclick="window.close()">
                    <i class="fas fa-times me-1"></i>Fermer
                </button>
            </div>
            @endunless
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    @media print {
        .no-print {
            display: none !important;
        }
        
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        
        body {
            background: white !important;
        }
        
        .container-fluid {
            padding: 0 !important;
        }
    }
    
    .table-borderless td {
        border: none !important;
        padding: 0.25rem 0.5rem;
    }
    
    .badge {
        font-size: 0.9em;
    }
</style>
@endpush
