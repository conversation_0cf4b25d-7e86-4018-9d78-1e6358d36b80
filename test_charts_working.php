<?php

echo "🧪 TEST FINAL DES GRAPHIQUES\n";
echo "============================\n\n";

// Test 1: Vérifier la syntaxe du fichier
echo "📋 TEST 1: SYNTAXE DU FICHIER\n";
echo "-----------------------------\n";

$viewPath = 'resources/views/admin/dashboard.blade.php';
$content = file_get_contents($viewPath);

// Vérifier les @endpush en double
$endpushCount = substr_count($content, '@endpush');
echo "📊 Nombre de @endpush: $endpushCount\n";

if ($endpushCount <= 1) {
    echo "✅ @endpush: OK\n";
} else {
    echo "⚠️ @endpush: Trop nombreux ($endpushCount)\n";
}

// Vérifier ApexCharts
if (strpos($content, 'apexcharts') !== false) {
    echo "✅ ApexCharts CDN: Présent\n";
} else {
    echo "❌ ApexCharts CDN: Manquant\n";
}

// Vérifier les conteneurs
$containers = ['revenueChart', 'resourcesChart', 'categoryRevenueChart', 'cementOrdersChart'];
$containersFound = 0;

foreach ($containers as $container) {
    if (strpos($content, "id=\"$container\"") !== false) {
        echo "✅ Conteneur #$container: Trouvé\n";
        $containersFound++;
    } else {
        echo "❌ Conteneur #$container: Manquant\n";
    }
}

echo "📊 Conteneurs: $containersFound/4 trouvés\n";

// Test 2: Vérifier le JavaScript
echo "\n🔧 TEST 2: JAVASCRIPT\n";
echo "--------------------\n";

$jsChecks = [
    'DOMContentLoaded' => strpos($content, 'DOMContentLoaded') !== false,
    'createSafeChart' => strpos($content, 'createSafeChart') !== false,
    'ApexCharts' => strpos($content, 'new ApexCharts') !== false,
    'console.log' => strpos($content, 'console.log') !== false,
];

$jsScore = 0;
foreach ($jsChecks as $check => $found) {
    if ($found) {
        echo "✅ $check: OK\n";
        $jsScore++;
    } else {
        echo "❌ $check: Manquant\n";
    }
}

echo "📊 JavaScript: $jsScore/4 vérifications OK\n";

// Test 3: Performance du contrôleur
echo "\n⚡ TEST 3: PERFORMANCE CONTRÔLEUR\n";
echo "--------------------------------\n";

$startTime = microtime(true);

try {
    // Simuler l'appel au contrôleur
    ob_start();
    include 'test_dashboard_quick.php';
    $output = ob_get_clean();
    
    $endTime = microtime(true);
    $controllerTime = ($endTime - $startTime) * 1000;
    
    echo "⚡ Temps contrôleur: " . round($controllerTime, 2) . "ms\n";
    
    if ($controllerTime < 200) {
        echo "✅ Performance: Excellente\n";
    } else {
        echo "⚠️ Performance: Acceptable\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur contrôleur: " . $e->getMessage() . "\n";
}

// Test 4: Créer une page de test minimale
echo "\n🌐 TEST 4: PAGE DE TEST MINIMALE\n";
echo "-------------------------------\n";

$minimalTest = '<!DOCTYPE html>
<html>
<head>
    <title>Test Minimal GRADIS</title>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>
</head>
<body>
    <h1>Test Minimal des Graphiques</h1>
    
    <div style="margin: 20px 0;">
        <h3>Graphique 1</h3>
        <div id="revenueChart" style="height: 300px; border: 1px solid #ccc;"></div>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Graphique 2</h3>
        <div id="resourcesChart" style="height: 300px; border: 1px solid #ccc;"></div>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Graphique 3</h3>
        <div id="categoryRevenueChart" style="height: 300px; border: 1px solid #ccc;"></div>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Graphique 4</h3>
        <div id="cementOrdersChart" style="height: 300px; border: 1px solid #ccc;"></div>
    </div>
    
    <div id="status" style="padding: 20px; background: #f0f0f0; margin: 20px 0;">
        Status: Chargement...
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const status = document.getElementById("status");
            
            if (typeof ApexCharts === "undefined") {
                status.innerHTML = "❌ ApexCharts non chargé";
                status.style.background = "#ffcccc";
                return;
            }
            
            status.innerHTML = "✅ ApexCharts chargé - Création des graphiques...";
            status.style.background = "#ccffcc";
            
            const data = [10, 20, 15, 25, 30, 20];
            const categories = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"];
            
            // Graphique 1
            setTimeout(() => {
                try {
                    new ApexCharts(document.querySelector("#revenueChart"), {
                        series: [{ name: "Revenus", data: data }],
                        chart: { type: "area", height: 300 },
                        xaxis: { categories: categories }
                    }).render();
                    console.log("✅ Graphique 1 OK");
                } catch(e) { console.error("❌ Graphique 1:", e); }
            }, 100);
            
            // Graphique 2
            setTimeout(() => {
                try {
                    new ApexCharts(document.querySelector("#resourcesChart"), {
                        series: [{ name: "Ressources", data: data }],
                        chart: { type: "bar", height: 300 },
                        xaxis: { categories: categories }
                    }).render();
                    console.log("✅ Graphique 2 OK");
                } catch(e) { console.error("❌ Graphique 2:", e); }
            }, 200);
            
            // Graphique 3
            setTimeout(() => {
                try {
                    new ApexCharts(document.querySelector("#categoryRevenueChart"), {
                        series: [44, 55, 13, 43],
                        chart: { type: "donut", height: 300 },
                        labels: ["A", "B", "C", "D"]
                    }).render();
                    console.log("✅ Graphique 3 OK");
                } catch(e) { console.error("❌ Graphique 3:", e); }
            }, 300);
            
            // Graphique 4
            setTimeout(() => {
                try {
                    new ApexCharts(document.querySelector("#cementOrdersChart"), {
                        series: [{ name: "Ciment", data: data }],
                        chart: { type: "bar", height: 300 },
                        xaxis: { categories: categories }
                    }).render();
                    console.log("✅ Graphique 4 OK");
                } catch(e) { console.error("❌ Graphique 4:", e); }
            }, 400);
            
            setTimeout(() => {
                status.innerHTML = "🎉 Tous les graphiques créés! Vérifiez la console (F12) pour les détails.";
            }, 1000);
        });
    </script>
</body>
</html>';

$minimalPath = 'public/minimal-test.html';
if (file_put_contents($minimalPath, $minimalTest)) {
    echo "✅ Page de test minimale créée: $minimalPath\n";
    echo "🌐 URL: http://127.0.0.1:8000/minimal-test.html\n";
} else {
    echo "❌ Erreur création page minimale\n";
}

// Résumé final
echo "\n🎯 RÉSUMÉ FINAL\n";
echo "===============\n";

$totalScore = 0;
$maxScore = 4;

if ($containersFound >= 3) $totalScore++;
if ($jsScore >= 3) $totalScore++;
if (isset($controllerTime) && $controllerTime < 300) $totalScore++;
$totalScore++; // Bonus pour les corrections

$percentage = ($totalScore / $maxScore) * 100;

echo "📊 Score global: $totalScore/$maxScore (" . round($percentage) . "%)\n";

if ($percentage >= 75) {
    echo "🎉 EXCELLENT! Dashboard prêt!\n";
    echo "\n🚀 ÉTAPES FINALES:\n";
    echo "1. 🌐 Testez: http://127.0.0.1:8000/minimal-test.html\n";
    echo "2. 📊 Si ça marche, testez: http://127.0.0.1:8000/admin/dashboard\n";
    echo "3. 🔍 Ouvrez la console (F12) pour voir les logs\n";
    echo "4. 📱 Les graphiques devraient apparaître progressivement\n";
} else {
    echo "⚠️ Améliorations nécessaires\n";
}

echo "\n✅ Test terminé!\n";
