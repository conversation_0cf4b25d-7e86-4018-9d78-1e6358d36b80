<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\StockHistory;

class ClearStockHistoriesTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock-histories:clear {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vide complètement la table stock_histories (suppression définitive)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== VIDAGE DE LA TABLE STOCK_HISTORIES ===');

        // Compter les historiques de stock
        $stockHistoriesCount = StockHistory::count();

        if ($stockHistoriesCount === 0) {
            $this->info('✅ La table stock_histories est déjà vide.');
            return 0;
        }

        $this->info("Nombre total d'historiques de stock trouvés : {$stockHistoriesCount}");

        // Demander confirmation si --force n'est pas utilisé
        if (!$this->option('force')) {
            $this->warn('⚠️  ATTENTION : Cette action va supprimer DÉFINITIVEMENT tous les historiques de stock !');
            $this->warn('⚠️  Vous perdrez l\'historique complet des mouvements de stock !');
            $this->warn('⚠️  Les stocks actuels des produits ne seront PAS affectés !');
            
            if (!$this->confirm('Êtes-vous sûr de vouloir continuer ?')) {
                $this->info('❌ Opération annulée.');
                return 0;
            }
        }

        try {
            $this->clearStockHistories($stockHistoriesCount);
            $this->info('✅ Opération terminée avec succès !');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la suppression : ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function clearStockHistories($stockHistoriesCount)
    {
        $this->info('🔄 Vidage de la table stock_histories...');
        
        try {
            // Étape 1: Supprimer tous les historiques de stock
            $this->info('1. Suppression des historiques de stock...');
            StockHistory::truncate(); // Utiliser truncate car pas de soft deletes
            
            // Étape 2: Reset auto-increment (déjà fait par truncate)
            $this->info('2. Reset de l\'auto-increment...');
            $this->info('   → Auto-increment remis à 1 (par truncate)');

            $this->info("✅ {$stockHistoriesCount} historiques de stock supprimés définitivement");
            $this->info('✅ Les stocks actuels des produits restent inchangés');
            $this->info('✅ Seul l\'historique des mouvements a été supprimé');
            
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
