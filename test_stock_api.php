<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "🔍 Test de l'API Stock Data...\n\n";
    
    // Test 1: Vérifier que le StockService fonctionne
    echo "1. Test du StockService...\n";
    $stockService = new App\Services\StockService();
    $stockStatus = $stockService->getStockStatus();
    $recentMovements = $stockService->getRecentStockMovements(10);
    $alerts = $stockService->getStockAlerts();
    
    echo "   ✅ StockService fonctionne\n";
    echo "   - Produits: " . count($stockStatus['products']) . "\n";
    echo "   - Mouvements récents: " . count($recentMovements) . "\n";
    echo "   - Alertes: " . count($alerts) . "\n\n";
    
    // Test 2: Simuler l'appel API
    echo "2. Test de la méthode getStockData...\n";
    $controller = new App\Http\Controllers\Admin\DashboardController();
    
    try {
        $response = $controller->getStockData();
        $data = $response->getData(true);
        
        echo "   ✅ API getStockData fonctionne\n";
        echo "   - Structure: " . json_encode(array_keys($data)) . "\n";
        echo "   - Timestamp: " . $data['timestamp'] . "\n\n";
        
    } catch (Exception $e) {
        echo "   ❌ Erreur dans getStockData: " . $e->getMessage() . "\n\n";
    }
    
    // Test 3: Vérifier la route
    echo "3. Test de la route...\n";
    try {
        $url = route('admin.dashboard.stock-data');
        echo "   ✅ Route générée: " . $url . "\n\n";
    } catch (Exception $e) {
        echo "   ❌ Erreur de route: " . $e->getMessage() . "\n\n";
    }
    
    // Test 4: Test cURL direct
    echo "4. Test cURL direct...\n";
    $url = 'http://127.0.0.1:8000/admin/dashboard/stock-data';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ Erreur cURL: " . $error . "\n";
    } else {
        echo "   Code HTTP: " . $httpCode . "\n";
        if ($httpCode === 200) {
            $data = json_decode($response, true);
            if ($data) {
                echo "   ✅ API accessible via cURL\n";
                echo "   - Clés: " . json_encode(array_keys($data)) . "\n";
            } else {
                echo "   ❌ Réponse non-JSON: " . substr($response, 0, 200) . "...\n";
            }
        } else {
            echo "   ❌ Erreur HTTP " . $httpCode . "\n";
            echo "   Réponse: " . substr($response, 0, 200) . "...\n";
        }
    }
    
    echo "\n📋 DIAGNOSTIC:\n";
    echo "==============\n";
    
    if (count($stockStatus['products']) === 0) {
        echo "⚠️  Aucun produit trouvé - Exécutez: php create_test_products.php\n";
    }
    
    echo "\n🔧 SOLUTIONS POSSIBLES:\n";
    echo "======================\n";
    echo "1. Vérifiez que le serveur Laravel fonctionne: php artisan serve\n";
    echo "2. Videz le cache: php artisan cache:clear\n";
    echo "3. Videz le cache des routes: php artisan route:clear\n";
    echo "4. Vérifiez les logs: tail -f storage/logs/laravel.log\n";
    echo "5. Créez des produits de test: php create_test_products.php\n";
    
} catch (Exception $e) {
    echo "❌ Erreur générale: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . "\n";
    echo "Ligne: " . $e->getLine() . "\n";
}
