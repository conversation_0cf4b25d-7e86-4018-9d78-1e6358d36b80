<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Supplier;
use App\Models\Supply;

class ClearSuppliersTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'suppliers:clear {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vide complètement la table suppliers et ses approvisionnements liés (suppression définitive)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== VIDAGE DE LA TABLE SUPPLIERS ===');

        // Compter les enregistrements
        $suppliersCount = Supplier::withTrashed()->count();
        $suppliesCount = Supply::withTrashed()->count();

        if ($suppliersCount === 0) {
            $this->info('✅ La table suppliers est déjà vide.');
            return 0;
        }

        $this->info("Nombre total de fournisseurs trouvés : {$suppliersCount}");
        $this->info("Nombre total d'approvisionnements liés : {$suppliesCount}");

        // Demander confirmation si --force n'est pas utilisé
        if (!$this->option('force')) {
            $this->warn('⚠️  ATTENTION : Cette action va supprimer DÉFINITIVEMENT :');
            $this->warn("   - {$suppliersCount} fournisseurs");
            $this->warn("   - {$suppliesCount} approvisionnements liés");
            $this->warn('⚠️  Cela affectera aussi les ventes, stock_histories, et supply_cities liés !');
            
            if (!$this->confirm('Êtes-vous sûr de vouloir continuer ?')) {
                $this->info('❌ Opération annulée.');
                return 0;
            }
        }

        try {
            $this->clearSuppliers($suppliersCount, $suppliesCount);
            $this->info('✅ Opération terminée avec succès !');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la suppression : ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function clearSuppliers($suppliersCount, $suppliesCount)
    {
        $this->info('🔄 Vidage de la table suppliers et tables liées...');
        
        try {
            // Étape 1: Supprimer les supply_cities liées
            $this->info('1. Suppression des supply_cities liées...');
            $supplyCitiesCount = DB::table('supply_cities')->count();
            if ($supplyCitiesCount > 0) {
                DB::table('supply_cities')->delete();
                $this->info("   → {$supplyCitiesCount} supply_cities supprimées");
            }

            // Étape 2: Supprimer les stock_histories liées aux supplies
            $this->info('2. Suppression des stock_histories liées...');
            $stockHistoriesCount = DB::table('stock_histories')->whereNotNull('supply_id')->count();
            if ($stockHistoriesCount > 0) {
                DB::table('stock_histories')->whereNotNull('supply_id')->delete();
                $this->info("   → {$stockHistoriesCount} stock_histories supprimées");
            }

            // Étape 3: Supprimer les approvisionnements
            if ($suppliesCount > 0) {
                $this->info('3. Suppression des approvisionnements...');
                Supply::withTrashed()->forceDelete();
                $this->info("   → {$suppliesCount} approvisionnements supprimés");
            }

            // Étape 4: Supprimer les fournisseurs
            $this->info('4. Suppression des fournisseurs...');
            Supplier::withTrashed()->forceDelete();

            // Étape 5: Reset des auto-increments
            $this->info('5. Reset des auto-increments...');
            DB::statement('ALTER TABLE suppliers AUTO_INCREMENT = 1');
            if ($suppliesCount > 0) {
                DB::statement('ALTER TABLE supplies AUTO_INCREMENT = 1');
            }

            $this->info("✅ {$suppliersCount} fournisseurs supprimés définitivement");
            if ($suppliesCount > 0) {
                $this->info("✅ {$suppliesCount} approvisionnements supprimés définitivement");
            }
            if ($supplyCitiesCount > 0) {
                $this->info("✅ {$supplyCitiesCount} supply_cities supprimées définitivement");
            }
            if ($stockHistoriesCount > 0) {
                $this->info("✅ {$stockHistoriesCount} stock_histories supprimées définitivement");
            }
            $this->info('✅ Auto-increments remis à 1');
            
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
