<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "🔍 Diagnostic complet de l'onglet État des Stocks...\n\n";
    
    // Test 1: Vérifier le StockService
    echo "1. Test du StockService...\n";
    $stockService = new App\Services\StockService();
    $stockStatus = $stockService->getStockStatus();
    
    echo "   ✅ StockService fonctionne\n";
    echo "   - Total produits: " . $stockStatus['summary']['total_products'] . "\n";
    echo "   - Stock normal: " . $stockStatus['summary']['normal_stock_count'] . "\n";
    echo "   - Stock faible: " . $stockStatus['summary']['low_stock_count'] . "\n";
    echo "   - Rupture: " . $stockStatus['summary']['out_of_stock_count'] . "\n\n";
    
    // Test 2: Simuler les variables du contrôleur
    echo "2. Test des variables du contrôleur...\n";
    $stockStats = [
        'total_products' => $stockStatus['summary']['total_products'],
        'total_stock_value' => $stockStatus['summary']['total_value'],
        'low_stock_count' => $stockStatus['summary']['low_stock_count'],
        'out_of_stock_count' => $stockStatus['summary']['out_of_stock_count'],
        'normal_stock_count' => $stockStatus['summary']['normal_stock_count'],
        'stock_turnover_rate' => 0,
        'last_stock_update' => null
    ];
    
    echo "   ✅ Variables stockStats créées:\n";
    foreach ($stockStats as $key => $value) {
        echo "   - $key: " . ($value ?? 'null') . "\n";
    }
    echo "\n";
    
    // Test 3: Vérifier si les produits existent
    echo "3. Vérification des produits...\n";
    $totalProducts = App\Models\Product::count();
    $activeProducts = App\Models\Product::where('is_active', true)->count();
    
    echo "   - Total produits en DB: $totalProducts\n";
    echo "   - Produits actifs: $activeProducts\n";
    
    if ($totalProducts === 0) {
        echo "   ⚠️  PROBLÈME: Aucun produit en base de données!\n";
        echo "   → Exécutez: php create_test_products.php\n\n";
    } else {
        echo "   ✅ Produits présents en base\n\n";
    }
    
    // Test 4: Vérifier les erreurs potentielles dans la vue
    echo "4. Test de rendu de la vue...\n";
    
    // Simuler les variables nécessaires pour la vue
    $pendingSupplies = collect([]);
    $lowStockProducts = App\Models\Product::where('stock_quantity', '<=', 10)->take(5)->get();
    $topDrivers = collect([]);
    $recentStockMovements = $stockService->getRecentStockMovements(15);
    $stockAlerts = $stockService->getStockAlerts();
    $alerts = [
        'pending_supplies' => 0,
        'low_stock_products' => $lowStockProducts->count()
    ];
    
    echo "   ✅ Variables de vue préparées\n";
    echo "   - pendingSupplies: " . $pendingSupplies->count() . "\n";
    echo "   - lowStockProducts: " . $lowStockProducts->count() . "\n";
    echo "   - stockAlerts: " . count($stockAlerts) . "\n\n";
    
    // Test 5: Vérifier la structure HTML
    echo "5. Vérification de la structure HTML...\n";
    $viewPath = 'resources/views/admin/dashboard.blade.php';
    
    if (file_exists($viewPath)) {
        $content = file_get_contents($viewPath);
        
        // Vérifier les éléments clés
        $checks = [
            'stock-status-tab' => strpos($content, 'id="stock-status-tab"') !== false,
            'stock-status div' => strpos($content, 'id="stock-status"') !== false,
            'stockStats variable' => strpos($content, '$stockStats') !== false,
            'total-products-count' => strpos($content, 'id="total-products-count"') !== false,
        ];
        
        foreach ($checks as $check => $result) {
            echo "   " . ($result ? "✅" : "❌") . " $check\n";
        }
        echo "\n";
    }
    
    // Test 6: Créer un fichier de test HTML simple
    echo "6. Création d'un fichier de test HTML...\n";
    
    $testHtml = '<!DOCTYPE html>
<html>
<head>
    <title>Test Onglet Stock</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Test de l\'onglet État des Stocks</h2>
        
        <ul class="nav nav-pills mb-3">
            <li class="nav-item">
                <button class="nav-link active" data-bs-toggle="pill" data-bs-target="#test1">Test 1</button>
            </li>
            <li class="nav-item">
                <button class="nav-link" id="stock-status-tab" data-bs-toggle="pill" data-bs-target="#stock-status">État des Stocks</button>
            </li>
        </ul>
        
        <div class="tab-content">
            <div class="tab-pane fade show active" id="test1">
                <p>Premier onglet de test</p>
            </div>
            <div class="tab-pane fade" id="stock-status">
                <div class="alert alert-success">
                    <h4>✅ Onglet État des Stocks fonctionne!</h4>
                    <p>Total produits: ' . $stockStats['total_products'] . '</p>
                    <p>Stock faible: ' . $stockStats['low_stock_count'] . '</p>
                    <p>Rupture: ' . $stockStats['out_of_stock_count'] . '</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
    
    file_put_contents('test_stock_tab.html', $testHtml);
    echo "   ✅ Fichier test_stock_tab.html créé\n";
    echo "   → Ouvrez ce fichier dans votre navigateur pour tester\n\n";
    
    // Résumé et recommandations
    echo "📋 RÉSUMÉ DU DIAGNOSTIC:\n";
    echo "========================\n";
    
    if ($totalProducts === 0) {
        echo "❌ PROBLÈME PRINCIPAL: Pas de produits en base\n";
        echo "   → Solution: php create_test_products.php\n\n";
    } else {
        echo "✅ Données disponibles\n";
        echo "✅ StockService fonctionne\n";
        echo "✅ Variables correctement définies\n\n";
    }
    
    echo "🔧 ÉTAPES DE RÉSOLUTION:\n";
    echo "========================\n";
    echo "1. Créer des produits: php create_test_products.php\n";
    echo "2. Vider les caches: php artisan cache:clear && php artisan view:clear\n";
    echo "3. Redémarrer le serveur: php artisan serve\n";
    echo "4. Tester le fichier HTML: ouvrir test_stock_tab.html\n";
    echo "5. Vérifier le dashboard: http://127.0.0.1:8000/admin/dashboard\n";
    echo "6. Ouvrir la console (F12) pour voir les erreurs JavaScript\n\n";
    
    echo "🌐 Si le problème persiste, vérifiez:\n";
    echo "=====================================\n";
    echo "• Que vous êtes connecté en tant qu'admin\n";
    echo "• Qu'il n'y a pas d'erreurs PHP dans les logs\n";
    echo "• Que Bootstrap est bien chargé\n";
    echo "• Que les variables sont bien passées à la vue\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . "\n";
    echo "Ligne: " . $e->getLine() . "\n";
}
