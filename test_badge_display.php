<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "🏷️  Test de l'affichage optimisé des badges...\n\n";
    
    // Test du StockService avec conversion de tonnage
    $stockService = new App\Services\StockService();
    $stockStatus = $stockService->getStockStatus();
    
    echo "📊 AFFICHAGE DES BADGES OPTIMISÉ:\n";
    echo "=================================\n\n";
    
    echo "🎨 MODIFICATIONS APPLIQUÉES:\n";
    echo "• Taille de police réduite: 0.75rem (au lieu de 0.875rem)\n";
    echo "• Icônes plus petites: 0.7rem\n";
    echo "• Padding réduit: 0.25rem 0.5rem\n";
    echo "• Largeur max: 100px avec ellipsis\n";
    echo "• Hauteur de ligne optimisée: 1.2\n\n";
    
    echo "📦 EXEMPLES D'AFFICHAGE:\n";
    echo "========================\n";
    
    $examples = [
        ['text' => '15.5 T', 'status' => 'normal', 'description' => 'Tonnage élevé'],
        ['text' => '2.3 T', 'status' => 'normal', 'description' => 'Tonnage moyen'],
        ['text' => '750 kg', 'status' => 'low_stock', 'description' => 'Stock faible'],
        ['text' => '125 kg', 'status' => 'low_stock', 'description' => 'Stock très faible'],
        ['text' => '0 kg', 'status' => 'out_of_stock', 'description' => 'Rupture de stock'],
        ['text' => '50.2 T', 'status' => 'normal', 'description' => 'Très gros tonnage'],
    ];
    
    foreach ($examples as $example) {
        $icon = match($example['status']) {
            'out_of_stock' => '🔴',
            'low_stock' => '🟡',
            default => '🟢'
        };
        
        $color = match($example['status']) {
            'out_of_stock' => 'ROUGE',
            'low_stock' => 'ORANGE',
            default => 'VERT'
        };
        
        echo sprintf(
            "%s Badge %-8s | %-12s | %s\n",
            $icon,
            $color,
            $example['text'],
            $example['description']
        );
    }
    
    echo "\n🔍 PRODUITS RÉELS AVEC NOUVEAUX BADGES:\n";
    echo "=======================================\n";
    
    foreach ($stockStatus['products'] as $index => $product) {
        if ($index >= 10) break; // Limiter à 10 produits
        
        $statusIcon = match($product['status']) {
            'out_of_stock' => '🔴',
            'low_stock' => '🟡',
            default => '🟢'
        };
        
        $tonnageDisplay = $product['tonnage_display'] ?? ($product['current_stock'] . ' ' . $product['unit']);
        
        echo sprintf(
            "%s %-25s | Badge: %-10s | Catégorie: %s\n",
            $statusIcon,
            substr($product['name'], 0, 25),
            $tonnageDisplay,
            $product['category']
        );
    }
    
    echo "\n📱 RESPONSIVE DESIGN:\n";
    echo "====================\n";
    echo "• Mobile: Badges encore plus compacts\n";
    echo "• Tablette: Taille optimale\n";
    echo "• Desktop: Affichage confortable\n";
    echo "• Ellipsis (...) pour textes trop longs\n\n";
    
    echo "🎯 AVANTAGES DE LA NOUVELLE TAILLE:\n";
    echo "===================================\n";
    echo "✅ Plus de place pour le texte du tonnage\n";
    echo "✅ Meilleur alignement dans les tableaux\n";
    echo "✅ Lecture plus facile sur mobile\n";
    echo "✅ Design plus moderne et compact\n";
    echo "✅ Icônes proportionnelles au texte\n\n";
    
    echo "🔧 CSS APPLIQUÉ:\n";
    echo "================\n";
    echo ".badge {\n";
    echo "    font-size: 0.75rem !important;\n";
    echo "    padding: 0.25rem 0.5rem !important;\n";
    echo "    max-width: 100px;\n";
    echo "    white-space: nowrap;\n";
    echo "    text-overflow: ellipsis;\n";
    echo "    line-height: 1.2;\n";
    echo "}\n\n";
    echo ".badge i {\n";
    echo "    font-size: 0.7rem !important;\n";
    echo "}\n\n";
    
    echo "🚀 ÉTAPES DE VÉRIFICATION:\n";
    echo "==========================\n";
    echo "1. Vider le cache: php artisan view:clear\n";
    echo "2. Actualiser le dashboard\n";
    echo "3. Vérifier l'onglet 'État des Stocks'\n";
    echo "4. Les badges devraient être plus compacts!\n\n";
    
    echo "📊 RÉSUMÉ FINAL:\n";
    echo "================\n";
    echo "Total produits: " . $stockStatus['summary']['total_products'] . "\n";
    echo "Tonnage total: " . $stockStatus['summary']['total_tonnage_display'] . "\n";
    echo "Badges optimisés: ✅\n";
    echo "Affichage tonnage: ✅\n";
    echo "Taille réduite: ✅\n\n";
    
    echo "✨ BADGES MAINTENANT OPTIMISÉS POUR LE TONNAGE! ✨\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . "\n";
    echo "Ligne: " . $e->getLine() . "\n";
}
