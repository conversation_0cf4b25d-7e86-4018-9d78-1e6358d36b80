/**
 * GRADIS Admin - Script d'Arrêt d'Urgence
 * Stoppe IMMÉDIATEMENT tous les processus JavaScript en arrière-plan
 * Version: 1.0 - Arrêt d'urgence total
 */

console.log('🚨 Script d\'arrêt d\'urgence chargé');

/**
 * Fonction d'arrêt d'urgence - Stoppe TOUT
 */
function emergencyStopAllScripts() {
    console.log('🛑 ARRÊT D\'URGENCE - Stopping ALL JavaScript processes...');
    
    // 1. Stopper tous les intervals existants (méthode brutale)
    let intervalId = setInterval(() => {}, 1);
    for (let i = 1; i <= intervalId; i++) {
        clearInterval(i);
    }
    console.log('❌ Tous les intervals stoppés (1 à', intervalId, ')');
    
    // 2. Stopper tous les timeouts existants (méthode brutale)
    let timeoutId = setTimeout(() => {}, 1);
    for (let i = 1; i <= timeoutId; i++) {
        clearTimeout(i);
    }
    console.log('❌ Tous les timeouts stoppés (1 à', timeoutId, ')');
    
    // 3. Annuler toutes les requêtes fetch en cours
    if (window.AbortController) {
        window.globalAbortController = new AbortController();
        window.globalAbortController.abort();
        console.log('❌ Toutes les requêtes fetch annulées');
    }
    
    // 4. Stopper tous les Web Workers
    if (window.Worker) {
        // Pas de workers détectés mais on s'assure
        console.log('❌ Web Workers vérifiés');
    }
    
    // 5. Désactiver tous les event listeners qui peuvent causer des problèmes
    const problematicEvents = ['scroll', 'resize', 'mousemove', 'touchmove'];
    problematicEvents.forEach(eventType => {
        const elements = document.querySelectorAll('*');
        elements.forEach(element => {
            element.removeEventListener(eventType, () => {});
        });
    });
    console.log('❌ Event listeners problématiques supprimés');
    
    // 6. Forcer l'arrêt des animations CSS
    const style = document.createElement('style');
    style.textContent = `
        *, *::before, *::after {
            animation-duration: 0s !important;
            animation-delay: 0s !important;
            transition-duration: 0s !important;
            transition-delay: 0s !important;
        }
    `;
    document.head.appendChild(style);
    console.log('❌ Toutes les animations CSS stoppées');
    
    // 7. Nettoyer le cache et les données stockées
    try {
        localStorage.removeItem('admin_dashboard_stats');
        localStorage.removeItem('admin_dashboard_charts');
        sessionStorage.clear();
        console.log('❌ Cache et stockage nettoyés');
    } catch (e) {
        console.log('⚠️ Impossible de nettoyer le cache:', e.message);
    }
    
    // 8. Afficher une notification de succès
    showEmergencyNotification('🛑 ARRÊT D\'URGENCE EFFECTUÉ', 'Tous les processus JavaScript ont été stoppés', 'success');
    
    console.log('✅ ARRÊT D\'URGENCE TERMINÉ - Interface optimisée');
}

/**
 * Notification d'urgence
 */
function showEmergencyNotification(title, message, type = 'info') {
    // Créer une notification simple sans dépendances
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#ef4444'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 9999;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        max-width: 350px;
    `;
    
    notification.innerHTML = `
        <div style="font-weight: 600; margin-bottom: 5px;">${title}</div>
        <div style="font-size: 14px; opacity: 0.9;">${message}</div>
    `;
    
    document.body.appendChild(notification);
    
    // Supprimer après 5 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

/**
 * Créer le bouton d'arrêt d'urgence
 */
function createEmergencyStopButton() {
    const button = document.createElement('button');
    button.id = 'emergency-stop-btn';
    button.innerHTML = '🛑 STOP';
    button.title = 'Arrêt d\'urgence - Stopper tous les scripts JavaScript';
    button.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #ef4444;
        color: white;
        border: none;
        padding: 12px 16px;
        border-radius: 50px;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
        z-index: 9999;
        transition: all 0.2s ease;
        font-family: 'Poppins', sans-serif;
    `;
    
    button.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.1)';
        this.style.boxShadow = '0 6px 16px rgba(239, 68, 68, 0.6)';
    });
    
    button.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
        this.style.boxShadow = '0 4px 12px rgba(239, 68, 68, 0.4)';
    });
    
    button.addEventListener('click', function() {
        // Confirmation avant arrêt d'urgence
        if (confirm('🛑 ARRÊT D\'URGENCE\n\nVoulez-vous stopper TOUS les processus JavaScript en cours ?\n\nCela va considérablement améliorer les performances.')) {
            emergencyStopAllScripts();
            
            // Changer le bouton en mode "stoppé"
            this.innerHTML = '✅ STOPPÉ';
            this.style.background = '#10b981';
            this.disabled = true;
        }
    });
    
    document.body.appendChild(button);
    console.log('🛑 Bouton d\'arrêt d\'urgence créé');
}

/**
 * Surveillance automatique des processus
 */
function startProcessMonitoring() {
    let processCount = 0;
    
    // Surveiller les nouveaux intervals/timeouts
    const originalSetInterval = window.setInterval;
    const originalSetTimeout = window.setTimeout;
    
    window.setInterval = function(...args) {
        processCount++;
        console.warn('⚠️ Nouveau setInterval détecté (#' + processCount + ')');
        
        if (processCount > 5) {
            console.error('🚨 TROP DE PROCESSUS DÉTECTÉS - Arrêt automatique recommandé');
            showEmergencyNotification('⚠️ ALERTE PERFORMANCE', 'Trop de processus JavaScript détectés. Cliquez sur STOP pour optimiser.', 'warning');
        }
        
        return originalSetInterval.apply(this, args);
    };
    
    window.setTimeout = function(...args) {
        if (args[1] > 2000) { // Timeouts longs
            console.warn('⚠️ setTimeout long détecté:', args[1], 'ms');
        }
        return originalSetTimeout.apply(this, args);
    };
    
    console.log('👁️ Surveillance des processus activée');
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚨 Initialisation du système d\'arrêt d\'urgence...');
    
    // Créer le bouton d'arrêt d'urgence
    createEmergencyStopButton();
    
    // Démarrer la surveillance
    startProcessMonitoring();
    
    // Arrêt automatique après 10 secondes si trop de processus
    setTimeout(() => {
        const intervalCount = setInterval(() => {}, 1);
        if (intervalCount > 50) {
            console.error('🚨 TROP DE PROCESSUS - Arrêt automatique en cours...');
            emergencyStopAllScripts();
        }
        clearInterval(intervalCount);
    }, 10000);
    
    console.log('✅ Système d\'arrêt d\'urgence prêt');
});

// Raccourci clavier pour arrêt d'urgence (Ctrl+Shift+S)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'S') {
        e.preventDefault();
        console.log('🚨 Raccourci d\'arrêt d\'urgence activé');
        emergencyStopAllScripts();
    }
});

// Export global
window.EmergencyStop = {
    stopAll: emergencyStopAllScripts,
    createButton: createEmergencyStopButton,
    showNotification: showEmergencyNotification
};
