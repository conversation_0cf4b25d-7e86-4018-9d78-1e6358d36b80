<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Product;

class ResetProductStockCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:reset-stock {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remet toutes les quantités de stock des produits à zéro';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== REMISE À ZÉRO DES STOCKS PRODUITS ===');

        // Compter les produits avec stock > 0
        $productsWithStock = Product::where('stock_quantity', '>', 0)->count();
        $totalProducts = Product::count();
        $totalStock = Product::sum('stock_quantity');

        if ($productsWithStock === 0) {
            $this->info('✅ Tous les produits ont déjà un stock de 0.');
            return 0;
        }

        $this->info("Nombre total de produits : {$totalProducts}");
        $this->info("Produits avec stock > 0 : {$productsWithStock}");
        $this->info("Stock total actuel : {$totalStock} unités");

        // Afficher les produits avec stock
        $this->info("\n📦 Produits avec stock actuel :");
        $productsWithStockData = Product::where('stock_quantity', '>', 0)
            ->select('name', 'stock_quantity', 'unit')
            ->get();
            
        foreach ($productsWithStockData as $product) {
            $this->info("   - {$product->name}: {$product->stock_quantity} {$product->unit}");
        }

        // Demander confirmation si --force n'est pas utilisé
        if (!$this->option('force')) {
            $this->warn("\n⚠️  ATTENTION : Cette action va :");
            $this->warn("   - Remettre le stock de {$productsWithStock} produits à ZÉRO");
            $this->warn("   - Supprimer {$totalStock} unités de stock au total");
            $this->warn("   - Cette action est IRRÉVERSIBLE");
            
            if (!$this->confirm('Êtes-vous sûr de vouloir continuer ?')) {
                $this->info('❌ Opération annulée.');
                return 0;
            }
        }

        try {
            $this->resetProductStocks($productsWithStock, $totalStock);
            $this->info('✅ Opération terminée avec succès !');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la remise à zéro : ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function resetProductStocks($productsWithStock, $totalStock)
    {
        $this->info('🔄 Remise à zéro des stocks...');
        
        try {
            // Mettre à jour tous les produits avec stock_quantity = 0
            $updatedCount = Product::where('stock_quantity', '>', 0)
                ->update(['stock_quantity' => 0]);

            $this->info("✅ {$updatedCount} produits mis à jour");
            $this->info("✅ {$totalStock} unités de stock supprimées");
            
            // Vérification finale
            $remainingStock = Product::sum('stock_quantity');
            $productsWithStockAfter = Product::where('stock_quantity', '>', 0)->count();
            
            $this->info("\n📊 Résultats finaux :");
            $this->info("   - Produits avec stock > 0 : {$productsWithStockAfter}");
            $this->info("   - Stock total restant : {$remainingStock} unités");
            
            if ($remainingStock == 0 && $productsWithStockAfter == 0) {
                $this->info("🎉 Tous les stocks ont été remis à zéro avec succès !");
            } else {
                $this->warn("⚠️  Attention : Il reste encore du stock après l'opération");
            }
            
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
