// Suppression des erreurs d'extensions
(function() {
    "use strict";
    
    // Supprimer les erreurs d'extensions
    const originalError = window.onerror;
    window.onerror = function(message, source, lineno, colno, error) {
        // Supprimer les erreurs connues
        if (message && (
            message.includes("inpage.js") ||
            message.includes("Cannot read properties of null") ||
            message.includes("reading 'type'") ||
            source && source.includes("extension")
        )) {
            return true; // Supprimer l'erreur
        }
        
        // Garder les autres erreurs
        if (originalError) {
            return originalError(message, source, lineno, colno, error);
        }
        return false;
    };
    
    // Supprimer les erreurs de promesses
    window.addEventListener("unhandledrejection", function(event) {
        if (event.reason && event.reason.message && (
            event.reason.message.includes("inpage.js") ||
            event.reason.message.includes("Cannot read properties of null")
        )) {
            event.preventDefault();
        }
    });
    
    console.log("🛡️ Suppression d'erreurs activée");
})();