/**
 * GRADIS Admin - Scripts Ultra-Rapides
 * Version optimisée pour éliminer les lenteurs de chargement
 * Remplace tous les scripts lourds par des versions ultra-légères
 */

console.log('⚡ GRADIS Admin Ultra-Fast - Scripts optimisés chargés');

// Configuration ultra-rapide
const ULTRA_FAST_CONFIG = {
    // Désactiver toutes les animations lourdes
    DISABLE_HEAVY_ANIMATIONS: true,
    
    // Désactiver l'auto-refresh
    DISABLE_AUTO_REFRESH: true,
    
    // Délais minimaux
    MINIMAL_DELAYS: true,
    
    // Mode performance maximale
    PERFORMANCE_MODE: true
};

/**
 * Fonction d'optimisation ultra-rapide au chargement
 */
function initUltraFastMode() {
    console.log('🚀 Initialisation du mode ultra-rapide...');
    
    // 1. Désactiver tous les setInterval existants
    stopAllIntervals();
    
    // 2. Désactiver tous les setTimeout longs
    stopLongTimeouts();
    
    // 3. Optimiser les animations CSS
    disableHeavyAnimations();
    
    // 4. Optimiser les graphiques
    optimizeCharts();
    
    // 5. Désactiver l'auto-refresh des données
    disableAutoRefresh();
    
    console.log('✅ Mode ultra-rapide activé');
}

/**
 * Stopper tous les intervals
 */
function stopAllIntervals() {
    // Méthode brutale mais efficace
    let intervalId = setInterval(() => {}, 1);
    for (let i = 1; i <= intervalId; i++) {
        clearInterval(i);
    }
    console.log('❌ Tous les intervals stoppés');
}

/**
 * Stopper les timeouts longs (> 5 secondes)
 */
function stopLongTimeouts() {
    const originalSetTimeout = window.setTimeout;
    window.setTimeout = function(callback, delay, ...args) {
        // Limiter les timeouts à maximum 5 secondes
        if (delay > 5000) {
            console.warn('⚠️ Timeout long bloqué:', delay, 'ms');
            delay = 5000;
        }
        return originalSetTimeout.call(this, callback, delay, ...args);
    };
    console.log('❌ Timeouts longs optimisés');
}

/**
 * Désactiver les animations lourdes
 */
function disableHeavyAnimations() {
    const style = document.createElement('style');
    style.textContent = `
        /* Désactiver toutes les animations lourdes */
        .animate-on-load,
        .fade-in,
        .slide-in,
        .bounce,
        .pulse,
        .loading-animation {
            animation: none !important;
            transition: none !important;
        }
        
        /* Optimiser les transitions */
        * {
            transition-duration: 0.1s !important;
        }
        
        /* Désactiver les transformations lourdes */
        .transform-heavy {
            transform: none !important;
        }
    `;
    document.head.appendChild(style);
    console.log('❌ Animations lourdes désactivées');
}

/**
 * Optimiser les graphiques
 */
function optimizeCharts() {
    // Désactiver les graphiques ApexCharts si présents
    if (window.ApexCharts) {
        console.log('⚠️ ApexCharts détecté - Optimisation en cours...');
        
        // Remplacer par des graphiques statiques
        const chartElements = document.querySelectorAll('[id*="chart"], .apexcharts-canvas');
        chartElements.forEach(element => {
            if (element) {
                element.innerHTML = '<div class="text-center p-3"><i class="fas fa-chart-line"></i><br>Graphique optimisé</div>';
            }
        });
    }
    
    // Désactiver Chart.js si présent
    if (window.Chart) {
        console.log('⚠️ Chart.js détecté - Optimisation en cours...');
        window.Chart.defaults.animation = false;
    }
    
    console.log('❌ Graphiques optimisés');
}

/**
 * Désactiver l'auto-refresh
 */
function disableAutoRefresh() {
    // Désactiver les fonctions d'auto-refresh communes
    if (window.startStockAutoRefresh) {
        window.startStockAutoRefresh = function() {
            console.log('❌ Auto-refresh des stocks désactivé');
        };
    }
    
    if (window.refreshStockData) {
        const originalRefresh = window.refreshStockData;
        window.refreshStockData = function() {
            console.log('⚡ Refresh manuel des stocks (optimisé)');
            // Appeler la fonction originale mais sans auto-refresh
            if (originalRefresh) {
                originalRefresh();
            }
        };
    }
    
    console.log('❌ Auto-refresh désactivé');
}

/**
 * Optimiser les requêtes AJAX
 */
function optimizeAjaxRequests() {
    // Intercepter et optimiser les requêtes fetch
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        // Ajouter un timeout court pour éviter les requêtes qui traînent
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 secondes max
        
        options.signal = controller.signal;
        
        return originalFetch(url, options)
            .finally(() => clearTimeout(timeoutId));
    };
    
    console.log('❌ Requêtes AJAX optimisées');
}

/**
 * Créer un bouton de contrôle ultra-rapide
 */
function createUltraFastControls() {
    const controlPanel = document.createElement('div');
    controlPanel.id = 'ultra-fast-controls';
    controlPanel.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 10px 15px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        z-index: 9999;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        font-family: 'Poppins', sans-serif;
    `;
    
    controlPanel.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-rocket"></i>
            <span>MODE ULTRA-RAPIDE ACTIVÉ</span>
            <button onclick="location.reload()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 11px;">
                Recharger
            </button>
        </div>
    `;
    
    document.body.appendChild(controlPanel);
    
    // Masquer après 10 secondes
    setTimeout(() => {
        if (controlPanel.parentNode) {
            controlPanel.style.opacity = '0.7';
            controlPanel.style.transform = 'scale(0.9)';
        }
    }, 10000);
}

/**
 * Surveiller les performances
 */
function monitorPerformance() {
    const startTime = performance.now();
    
    // Surveiller le temps de chargement
    window.addEventListener('load', function() {
        const loadTime = performance.now() - startTime;
        console.log(`⚡ Temps de chargement total: ${Math.round(loadTime)}ms`);
        
        if (loadTime < 3000) {
            console.log('🎉 Performance EXCELLENTE!');
        } else if (loadTime < 5000) {
            console.log('✅ Performance BONNE');
        } else {
            console.log('⚠️ Performance à améliorer');
        }
    });
}

/**
 * Initialisation automatique
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initialisation du mode ultra-rapide...');
    
    // Délai minimal pour laisser le DOM se charger
    setTimeout(() => {
        initUltraFastMode();
        optimizeAjaxRequests();
        createUltraFastControls();
        monitorPerformance();
        
        // Notification de succès
        console.log('🎉 Mode ultra-rapide complètement activé!');
        
        // Afficher une notification à l'utilisateur
        if (typeof showNotification === 'function') {
            showNotification('Mode ultra-rapide activé! Performance optimisée.', 'success');
        }
    }, 100);
});

// Export global
window.UltraFastMode = {
    init: initUltraFastMode,
    stopIntervals: stopAllIntervals,
    optimizeCharts: optimizeCharts,
    disableAutoRefresh: disableAutoRefresh
};

console.log('⚡ Scripts ultra-rapides prêts');
