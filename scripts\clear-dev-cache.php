<?php

/**
 * Script pour vider tous les caches en développement
 * Utile pour résoudre les problèmes de performance en local
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;

class ClearDevCache
{
    private $app;
    
    public function __construct()
    {
        $this->initializeLaravel();
    }
    
    /**
     * Initialise l'application Laravel
     */
    private function initializeLaravel()
    {
        $this->app = require_once __DIR__ . '/../bootstrap/app.php';
        $this->app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    }
    
    /**
     * Vide tous les caches
     */
    public function run()
    {
        $this->log("🧹 Nettoyage des caches de développement...");
        
        try {
            // Vider le cache de l'application
            Cache::flush();
            $this->log("✅ Cache de l'application vidé");
            
            // Vider les caches Laravel
            Artisan::call('cache:clear');
            $this->log("✅ Cache Laravel vidé");
            
            Artisan::call('config:clear');
            $this->log("✅ Cache de configuration vidé");
            
            Artisan::call('route:clear');
            $this->log("✅ Cache des routes vidé");
            
            Artisan::call('view:clear');
            $this->log("✅ Cache des vues vidé");
            
            // Vider le cache des événements si il existe
            try {
                Artisan::call('event:clear');
                $this->log("✅ Cache des événements vidé");
            } catch (Exception $e) {
                $this->log("⚠️  Cache des événements non disponible");
            }
            
            // Vider les caches spécifiques de l'application
            $this->clearApplicationCaches();
            
            // Optimiser l'autoloader pour le développement
            $this->optimizeAutoloader();
            
            $this->log("🎉 Tous les caches ont été vidés avec succès!");
            $this->log("💡 Conseil: Redémarrez votre serveur de développement pour de meilleures performances");
            
        } catch (Exception $e) {
            $this->error("❌ Erreur lors du nettoyage: " . $e->getMessage());
        }
    }
    
    /**
     * Vide les caches spécifiques de l'application
     */
    private function clearApplicationCaches()
    {
        $this->log("🔄 Nettoyage des caches spécifiques...");
        
        // Vider les caches du dashboard admin
        $adminCacheKeys = [
            'admin_dashboard_stats',
            'admin_sales_stats_*'
        ];
        
        foreach ($adminCacheKeys as $pattern) {
            if (strpos($pattern, '*') !== false) {
                // Pour les patterns avec wildcard, on doit itérer
                $prefix = str_replace('*', '', $pattern);
                $this->clearCacheByPattern($prefix);
            } else {
                Cache::forget($pattern);
            }
        }
        
        $this->log("✅ Caches spécifiques vidés");
    }
    
    /**
     * Vide les caches par pattern
     */
    private function clearCacheByPattern($prefix)
    {
        try {
            // Pour Redis
            if (config('cache.default') === 'redis') {
                $redis = Cache::store('redis')->getRedis();
                $keys = $redis->keys($prefix . '*');
                if (!empty($keys)) {
                    $redis->del($keys);
                }
            }
            // Pour les autres drivers, on ne peut pas facilement faire de pattern matching
        } catch (Exception $e) {
            $this->log("⚠️  Impossible de vider les caches par pattern: " . $e->getMessage());
        }
    }
    
    /**
     * Optimise l'autoloader pour le développement
     */
    private function optimizeAutoloader()
    {
        $this->log("⚡ Optimisation de l'autoloader...");
        
        try {
            // Régénérer l'autoloader sans optimisation pour le développement
            $output = [];
            $returnCode = 0;
            exec('composer dump-autoload', $output, $returnCode);
            
            if ($returnCode === 0) {
                $this->log("✅ Autoloader régénéré");
            } else {
                $this->log("⚠️  Erreur lors de la régénération de l'autoloader");
            }
        } catch (Exception $e) {
            $this->log("⚠️  Impossible d'optimiser l'autoloader: " . $e->getMessage());
        }
    }
    
    /**
     * Affiche les informations de cache
     */
    public function info()
    {
        $this->log("📊 Informations sur les caches:");
        
        // Driver de cache
        $cacheDriver = config('cache.default');
        $this->log("  Cache driver: {$cacheDriver}");
        
        // Driver de session
        $sessionDriver = config('session.driver');
        $this->log("  Session driver: {$sessionDriver}");
        
        // Environnement
        $environment = config('app.env');
        $this->log("  Environnement: {$environment}");
        
        // Debug mode
        $debug = config('app.debug') ? 'activé' : 'désactivé';
        $this->log("  Mode debug: {$debug}");
        
        // Test de connexion Redis si applicable
        if ($cacheDriver === 'redis') {
            try {
                Cache::store('redis')->put('test_connection', 'ok', 10);
                $value = Cache::store('redis')->get('test_connection');
                if ($value === 'ok') {
                    $this->log("  ✅ Connexion Redis: OK");
                    Cache::store('redis')->forget('test_connection');
                } else {
                    $this->log("  ❌ Connexion Redis: Échec");
                }
            } catch (Exception $e) {
                $this->log("  ❌ Connexion Redis: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Log un message
     */
    private function log($message)
    {
        echo $message . "\n";
    }
    
    /**
     * Log une erreur
     */
    private function error($message)
    {
        echo $message . "\n";
    }
}

// Exécuter le script
if (php_sapi_name() === 'cli') {
    $action = $argv[1] ?? 'clear';
    
    $cleaner = new ClearDevCache();
    
    switch ($action) {
        case 'clear':
            $cleaner->run();
            break;
            
        case 'info':
            $cleaner->info();
            break;
            
        default:
            echo "Usage: php clear-dev-cache.php [clear|info]\n";
            echo "  clear - Vide tous les caches (défaut)\n";
            echo "  info  - Affiche les informations sur les caches\n";
            exit(1);
    }
} else {
    echo "Ce script doit être exécuté en ligne de commande.\n";
    exit(1);
}
