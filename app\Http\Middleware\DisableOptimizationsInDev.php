<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DisableOptimizationsInDev
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // En développement, désactiver certaines optimisations qui peuvent causer des problèmes
        if (config('app.env') !== 'production') {
            // Headers pour désactiver le cache en développement
            $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');
            
            // Désactiver la compression pour éviter les erreurs de décodage
            $response->headers->remove('Content-Encoding');
            
            // Headers pour le debugging
            $response->headers->set('X-Debug-Mode', 'enabled');
        }

        return $response;
    }
}
