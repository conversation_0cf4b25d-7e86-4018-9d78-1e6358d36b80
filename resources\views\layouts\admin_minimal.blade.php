<!DOCTYPE html>
<html lang="fr" class="h-100">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title') - GRADIS</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    
    <!-- OPTIMISATION: Preconnect pour accélérer les CDN -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    
    <!-- Google Fonts - Optimisé avec display=swap -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome - Chargement normal -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS - Priorité haute -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 CSS - Chargement normal -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --sidebar-width: 260px;
            --sidebar-width-collapsed: 70px;
            --top-navbar-height: 60px;
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
        }

        body {
            font-family: 'Poppins', sans-serif;
            min-height: 100vh;
            background-color: white;
            background: white;
            background-image: none;
            overflow-x: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: #111827;
            box-shadow: 0 0 15px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            z-index: 1040;
            overflow-y: auto;
            scrollbar-width: thin;
        }

        .sidebar.collapsed {
            width: var(--sidebar-width-collapsed);
        }

        .sidebar-header {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header img {
            max-height: 40px;
            transition: all 0.3s ease;
        }

        .nav-heading {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            color: #9ca3af;
            padding: 1rem 1.5rem 0.5rem;
            letter-spacing: 0.05em;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: #d1d5db;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
            margin: 0.2rem 0.7rem;
        }

        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: #fff;
        }

        .nav-link i {
            width: 20px;
            margin-right: 10px;
            font-size: 1.1rem;
        }

        /* Top Navbar Styles */
        .top-navbar {
            position: fixed;
            top: 0;
            right: 0;
            left: var(--sidebar-width);
            height: var(--top-navbar-height);
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.02);
            transition: all 0.3s ease;
            z-index: 1030;
        }

        .sidebar.collapsed ~ .top-navbar {
            left: var(--sidebar-width-collapsed);
        }

        .navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100%;
            padding: 0 1.5rem;
        }

        .navbar-left button {
            background: none;
            border: none;
            font-size: 1.25rem;
            color: #4b5563;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .navbar-left button:hover {
            background-color: #f3f4f6;
            color: var(--primary-color);
        }

        .profile-menu {
            position: relative;
        }

        .profile-toggle {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .profile-toggle:hover {
            background-color: #f3f4f6;
        }

        /* Avatar styles */
        .profile-avatar {
            position: relative;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 0.75rem;
            background-color: var(--primary-color);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .profile-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .profile-initial {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .profile-info {
            display: flex;
            flex-direction: column;
        }

        .profile-info .name {
            font-size: 0.875rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        /* Dropdown styles */
        .profile-dropdown {
            width: 280px;
            padding: 0;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .dropdown-user-details {
            padding: 1.25rem;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            gap: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .dropdown-user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }
        
        .dropdown-user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .dropdown-user-initial {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.75rem;
            font-weight: 600;
        }
        
        .dropdown-user-info {
            flex: 1;
        }
        
        .dropdown-item {
            padding: 0.75rem 1.25rem;
            transition: all 0.2s ease;
        }
        
        .dropdown-item:hover {
            background-color: #f1f5f9;
        }

        /* Main Content Styles */
        .main-content {
            margin-left: var(--sidebar-width);
            padding-top: var(--top-navbar-height);
            min-height: 100vh;
            transition: all 0.3s ease;
            background-color: white;
            background: white;
            background-image: none;
        }

        .sidebar.collapsed ~ .main-content {
            margin-left: var(--sidebar-width-collapsed);
        }

        /* Responsive Styles Améliorés */
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
                width: var(--sidebar-width) !important;
                z-index: 1040;
                box-shadow: 4px 0 30px rgba(0, 0, 0, 0.3);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content, .top-navbar {
                margin-left: 0 !important;
                left: 0;
                width: 100%;
                padding: 1rem;
            }

            .nav-heading, .nav-link span {
                opacity: 1 !important;
                display: block !important;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(4px);
                z-index: 1035;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .sidebar-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            /* Amélioration du bouton toggle */
            .top-navbar button {
                padding: 0.5rem;
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: #ffffff;
                transition: all 0.3s ease;
            }

            .top-navbar button:hover {
                background: rgba(255, 255, 255, 0.2);
                transform: scale(1.05);
            }
        }

        @media (max-width: 767.98px) {
            .sidebar {
                width: 100% !important;
                max-width: 320px;
            }

            .profile-info .name {
                display: none;
            }

            .profile-image {
                margin-right: 0;
            }

            .container-fluid {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }

            .main-content {
                padding: 0.75rem;
            }

            /* Amélioration de la navbar mobile */
            .top-navbar {
                padding: 0.75rem 1rem;
                background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }

            .navbar-brand {
                font-size: 1.1rem;
                font-weight: 600;
            }
        }

        @media (max-width: 575.98px) {
            .sidebar {
                width: 100% !important;
                max-width: none;
            }

            .container-fluid {
                padding: 0.5rem;
            }

            .main-content {
                padding: 0.5rem;
            }

            .top-navbar {
                padding: 0.5rem 0.75rem;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        /* Collapsed Sidebar Styles */
        @media (min-width: 992px) {
            .sidebar.collapsed .nav-heading {
                opacity: 0;
            }

            .sidebar.collapsed .nav-link span {
                opacity: 0;
                display: none;
            }

            .sidebar.collapsed .nav-link {
                padding: 0.75rem;
                justify-content: center;
            }

            .sidebar.collapsed .nav-link i {
                margin: 0;
                font-size: 1.25rem;
            }

            .sidebar.collapsed .profile-info {
                display: none;
            }
        }

        /* Scrollbar Styles */
        .sidebar::-webkit-scrollbar {
            width: 5px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: #1f2937;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: #4b5563;
            border-radius: 5px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }

        /* ===== STYLES MODERNES POUR LA SIDEBAR ===== */

        /* Sidebar moderne avec dégradé sombre */
        .modern-sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%) !important;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
        }

        /* Header de la sidebar moderne */
        .modern-sidebar .sidebar-header {
            padding: 1.5rem 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.05);
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .sidebar-logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .brand-text {
            flex: 1;
        }

        .brand-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #ffffff;
            margin: 0;
            letter-spacing: 0.5px;
        }

        .brand-subtitle {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Indicateur de statut système */
        .system-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 6px;
            font-size: 0.75rem;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
            animation: pulse 2s infinite;
        }

        .status-text {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Navigation sections */
        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-heading {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.6);
            letter-spacing: 0.05em;
            position: relative;
        }

        .nav-heading-icon {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.5);
        }

        .nav-heading-line {
            flex: 1;
            height: 1px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
            margin-left: 0.5rem;
        }

        /* Liens de navigation modernes */
        .modern-sidebar .nav-link {
            position: relative;
            margin: 0.25rem 0.75rem;
            border-radius: 10px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .nav-link-content {
            display: flex;
            align-items: center;
            padding: 0.875rem 1rem;
            position: relative;
            z-index: 2;
        }

        .nav-icon-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            margin-right: 0.75rem;
            transition: all 0.3s ease;
        }

        .nav-icon {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }

        .nav-text {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .nav-indicator {
            position: absolute;
            right: 1rem;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: transparent;
            transition: all 0.3s ease;
        }

        /* États hover et active */
        .modern-sidebar .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }

        .modern-sidebar .nav-link:hover .nav-icon-wrapper {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .modern-sidebar .nav-link:hover .nav-icon {
            color: #ffffff;
            transform: scale(1.1);
        }

        .modern-sidebar .nav-link:hover .nav-text {
            color: #ffffff;
        }

        .modern-sidebar .nav-link.active {
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.8) 0%, rgba(124, 58, 237, 0.8) 100%);
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            transform: translateX(4px);
        }

        .modern-sidebar .nav-link.active .nav-icon-wrapper {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .modern-sidebar .nav-link.active .nav-icon {
            color: #ffffff;
        }

        .modern-sidebar .nav-link.active .nav-text {
            color: #ffffff;
            font-weight: 600;
        }

        .modern-sidebar .nav-link.active .nav-indicator {
            background: #ffffff;
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
        }

        /* FORCE LE FOND BLANC SEULEMENT POUR LE CONTENU PRINCIPAL (PAS LA SIDEBAR) */
        html, body {
            background: white !important;
            background-color: white !important;
            background-image: none !important;
        }

        .main-content,
        .main-content .container,
        .main-content .container-fluid,
        .main-content .content-wrapper,
        .main-content .page-wrapper {
            background: white !important;
            background-color: white !important;
            background-image: none !important;
        }

        /* Suppression spécifique des gradients violets/bleus SEULEMENT dans le contenu principal */
        .main-content [style*="linear-gradient"],
        .main-content [style*="gradient"],
        .main-content [style*="#667eea"],
        .main-content [style*="#764ba2"] {
            background: white !important;
            background-color: white !important;
            background-image: none !important;
        }

        /* PRÉSERVATION EXPLICITE DU DESIGN DE LA SIDEBAR */
        .sidebar,
        .sidebar *,
        .modern-sidebar,
        .modern-sidebar * {
            /* Annuler les modifications de fond pour la sidebar */
            background: unset !important;
            background-color: unset !important;
            background-image: unset !important;
        }

        /* Restaurer le design original de la sidebar */
        .sidebar {
            background: #111827 !important;
        }

        .modern-sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%) !important;
        }
    </style>

    @stack('styles')

    <!-- Script pour forcer le fond blanc SEULEMENT sur le contenu principal -->
    <script>
    // Force le fond blanc dès le chargement (en préservant la sidebar)
    (function() {
        function forceWhiteBackground() {
            try {
                // Vérifier que les éléments existent avant de les modifier
                if (document.documentElement) {
                    document.documentElement.style.setProperty('background', 'white', 'important');
                    document.documentElement.style.setProperty('background-color', 'white', 'important');
                    document.documentElement.style.setProperty('background-image', 'none', 'important');
                }

                if (document.body) {
                    document.body.style.setProperty('background', 'white', 'important');
                    document.body.style.setProperty('background-color', 'white', 'important');
                    document.body.style.setProperty('background-image', 'none', 'important');
                }

                // Force sur les conteneurs principaux SEULEMENT (pas la sidebar)
                const mainElements = document.querySelectorAll('.main-content, .main-content .container, .main-content .container-fluid, .main-content .content-wrapper, .main-content .page-wrapper');
                if (mainElements) {
                    mainElements.forEach(el => {
                        if (el && el.style) {
                            el.style.setProperty('background', 'white', 'important');
                            el.style.setProperty('background-color', 'white', 'important');
                            el.style.setProperty('background-image', 'none', 'important');
                        }
                    });
                }

                // PRÉSERVER EXPLICITEMENT LE DESIGN DE LA SIDEBAR
                const sidebarElements = document.querySelectorAll('.sidebar, .sidebar *, .modern-sidebar, .modern-sidebar *');
                if (sidebarElements) {
                    sidebarElements.forEach(el => {
                        if (el && el.style) {
                            // Supprimer les styles de fond blanc forcés
                            el.style.removeProperty('background');
                            el.style.removeProperty('background-color');
                            el.style.removeProperty('background-image');
                        }
                    });
                }

                // Restaurer le design original de la sidebar
                const sidebar = document.querySelector('.sidebar');
                if (sidebar && sidebar.style) {
                    sidebar.style.setProperty('background', '#111827', 'important');
                }

                const modernSidebar = document.querySelector('.modern-sidebar');
                if (modernSidebar && modernSidebar.style) {
                    modernSidebar.style.setProperty('background', 'linear-gradient(180deg, #1e293b 0%, #0f172a 100%)', 'important');
                }
            } catch (error) {
                // Ignorer silencieusement les erreurs pour éviter de casser la page
                console.warn('Erreur dans forceWhiteBackground:', error);
            }
        }

        // Exécuter immédiatement
        forceWhiteBackground();

        // Exécuter quand le DOM est prêt
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', forceWhiteBackground);
        }

        // Exécuter après un délai
        setTimeout(forceWhiteBackground, 100);
    })();
    </script>
</head>
<body class="d-flex flex-column h-100">
    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    @include('layouts.partials.admin-sidebar')

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Navbar -->
        <nav class="top-navbar">
            <div class="navbar-content">
                <div class="navbar-left">
                    <button id="sidebarToggle" aria-label="Toggle Sidebar">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
                
                <div class="navbar-right">
                    <div class="profile-menu">
                        <div class="profile-toggle" data-bs-toggle="dropdown">
                            <div class="profile-avatar">
                                @if(Auth::user()->avatar && file_exists(public_path(Auth::user()->avatar)))
                                    <img src="{{ asset(Auth::user()->avatar) }}" alt="Profile" class="profile-image">
                                @else
                                    <div class="profile-initial">
                                        {{ substr(Auth::user()->name, 0, 1) }}
                                    </div>
                                @endif
                            </div>
                            <div class="profile-info">
                                <span class="name">{{ Auth::user()->name }}</span>
                            </div>
                        </div>
                        <ul class="dropdown-menu dropdown-menu-end profile-dropdown">
                            <li>
                                <div class="dropdown-user-details">
                                    <div class="dropdown-user-avatar">
                                        @if(Auth::user()->avatar && file_exists(public_path(Auth::user()->avatar)))
                                            <img src="{{ asset(Auth::user()->avatar) }}" alt="Profile">
                                        @else
                                            <div class="dropdown-user-initial">
                                                {{ substr(Auth::user()->name, 0, 1) }}
                                            </div>
                                        @endif
                                    </div>
                                    <div class="dropdown-user-info">
                                        <h6 class="mb-0">{{ Auth::user()->name }}</h6>
                                        <p class="small text-muted mb-1">{{ Auth::user()->email }}</p>
                                        <span class="badge bg-primary">Administrateur</span>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.profile.show') }}">
                                    <i class="fas fa-user me-2"></i>
                                    Voir mon profil
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.profile.edit') }}">
                                    <i class="fas fa-user-edit me-2"></i>
                                    Modifier mon profil
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('admin.profile.password') }}">
                                    <i class="fas fa-key me-2"></i>
                                    Changer mot de passe
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        Déconnexion
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="container-fluid py-4">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @if(is_array(session('success')))
                        @foreach(session('success') as $message)
                            <div>{{ $message }}</div>
                        @endforeach
                    @else
                        {{ session('success') }}
                    @endif
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @if(is_array(session('error')))
                        @foreach(session('error') as $message)
                            <div>{{ $message }}</div>
                        @endforeach
                    @else
                        {{ session('error') }}
                    @endif
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @yield('content')
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const mainContent = document.getElementById('main-content');

            // Fonction améliorée pour gérer l'état du menu
            function toggleSidebar() {
                if (window.innerWidth < 992) {
                    // Mode mobile/tablette
                    const isShowing = sidebar.classList.contains('show');

                    if (isShowing) {
                        // Fermer avec animation
                        sidebar.classList.remove('show');
                        sidebarOverlay.classList.remove('show');
                        document.body.style.overflow = '';
                    } else {
                        // Ouvrir avec animation
                        sidebar.classList.add('show');
                        sidebarOverlay.classList.add('show');
                        document.body.style.overflow = 'hidden'; // Empêcher le scroll du body
                    }
                } else {
                    // Mode desktop
                    sidebar.classList.toggle('collapsed');
                    localStorage.setItem('sidebarState', sidebar.classList.contains('collapsed') ? 'collapsed' : 'expanded');
                }
            }

            // Fonction pour fermer la sidebar
            function closeSidebar() {
                if (window.innerWidth < 992) {
                    sidebar.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                    document.body.style.overflow = '';
                }
            }

            // Gestionnaire de clic pour le bouton toggle
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleSidebar();
                });
            }

            // Gestionnaire de clic pour l'overlay
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function(e) {
                    e.preventDefault();
                    closeSidebar();
                });
            }

            // Fermer avec la touche Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && window.innerWidth < 992) {
                    closeSidebar();
                }
            });

            // Restaurer l'état du menu au chargement
            if (window.innerWidth >= 992) {
                const sidebarState = localStorage.getItem('sidebarState');
                if (sidebarState === 'collapsed') {
                    sidebar.classList.add('collapsed');
                }
            }

            // Gestionnaire de redimensionnement amélioré
            let resizeTimer;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(function() {
                    if (window.innerWidth >= 992) {
                        // Passage en mode desktop
                        sidebar.classList.remove('show');
                        sidebarOverlay.classList.remove('show');
                        document.body.style.overflow = '';

                        // Restaurer l'état collapsed si nécessaire
                        const sidebarState = localStorage.getItem('sidebarState');
                        if (sidebarState === 'collapsed') {
                            sidebar.classList.add('collapsed');
                        } else {
                            sidebar.classList.remove('collapsed');
                        }
                    } else {
                        // Passage en mode mobile
                        sidebar.classList.remove('collapsed');
                    }
                }, 100);
            });

            // Fermer la sidebar lors du clic sur un lien (mobile uniquement)
            const sidebarLinks = document.querySelectorAll('.nav-link');
            sidebarLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (window.innerWidth < 992 && sidebar.classList.contains('show')) {
                        // Petit délai pour permettre la navigation
                        setTimeout(() => {
                            closeSidebar();
                        }, 150);
                    }
                });
            });

            // Amélioration de l'accessibilité
            if (sidebar) {
                sidebar.setAttribute('aria-hidden', 'true');

                // Observer les changements de classe pour l'accessibilité
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            const isVisible = sidebar.classList.contains('show') || window.innerWidth >= 992;
                            sidebar.setAttribute('aria-hidden', !isVisible);
                        }
                    });
                });

                observer.observe(sidebar, { attributes: true });
            }

            // Gestion du focus pour l'accessibilité
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    if (window.innerWidth < 992 && sidebar.classList.contains('show')) {
                        // Focus sur le premier lien de navigation
                        const firstNavLink = sidebar.querySelector('.nav-link');
                        if (firstNavLink) {
                            setTimeout(() => firstNavLink.focus(), 100);
                        }
                    }
                });
            }

            // OPTIMISATION: Initialisation différée des tooltips
            setTimeout(() => {
                const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
                tooltips.forEach(el => new bootstrap.Tooltip(el));
            }, 100);

            // OPTIMISATION: Suppression des logs pour améliorer les performances
        });
    </script>

    <!-- OPTIMISATION ULTRA-RAPIDE: Script unique optimisé pour performance maximale -->
    <script src="{{ asset('js/admin-ultra-fast.js') }}" defer></script>

    <!-- Scripts lourds désactivés pour performance -->
    <!-- <script src="{{ asset('js/admin-optimized.js') }}" defer></script> -->
    <!-- <script src="{{ asset('js/charts-optimized.js') }}" defer></script> -->
    <!-- <script src="{{ asset('js/emergency-stop.js') }}" defer></script> -->
    
    @stack('scripts')
</body>
</html>
