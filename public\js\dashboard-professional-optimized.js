/**
 * Dashboard Professionnel Optimisé
 * Consolidation de tous les scripts pour améliorer les performances
 */

// Variables globales
let charts = {};
let dashboardInitialized = false;

/**
 * Initialisation principale du dashboard
 */
function initDashboard() {
    if (dashboardInitialized) return;

    console.log('🚀 Initialisation du dashboard professionnel optimisé avec lazy loading');

    // Initialiser les composants de base immédiatement
    initBasicComponents();

    // Initialiser le lazy loading pour les sections non critiques
    initLazyLoading();

    // Initialiser les graphiques seulement si Chart.js est disponible
    if (typeof Chart !== 'undefined') {
        initCharts();
    }

    // Initialiser les animations
    initAnimations();

    // Initialiser les événements
    initEventListeners();

    dashboardInitialized = true;
    console.log('✅ Dashboard initialisé avec succès et lazy loading activé');
}

/**
 * Initialisation des composants de base
 */
function initBasicComponents() {
    // Initialiser les tooltips
    initTooltips();
    
    // Initialiser les filtres
    initFilters();
    
    // Initialiser les widgets
    initWidgets();
}

/**
 * Initialisation des graphiques
 */
function initCharts() {
    try {
        // Graphique des statuts de factures
        createInvoiceStatusChart();
        
        // Graphiques des ventes mensuelles
        if (window.dashboardData.monthlySales) {
            createMonthlySalesChart();
        }
        
        // Graphiques des approvisionnements
        if (window.dashboardData.supplyChartData) {
            createSupplyCharts();
        }
        
    } catch (error) {
        console.error('Erreur lors de l\'initialisation des graphiques:', error);
    }
}

/**
 * Création du graphique circulaire des statuts de factures
 */
function createInvoiceStatusChart() {
    const canvas = document.getElementById('invoiceStatusChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const data = window.dashboardData;
    
    charts.invoiceStatus = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Payées', 'Partielles', 'Impayées'],
            datasets: [{
                data: [
                    data.paidInvoices || 0,
                    data.partialInvoices || 0,
                    data.unpaidInvoices || 0
                ],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

/**
 * Création du graphique des ventes mensuelles
 */
function createMonthlySalesChart() {
    const canvas = document.getElementById('monthlySalesChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const salesData = window.dashboardData.monthlySales;
    
    charts.monthlySales = new Chart(ctx, {
        type: 'line',
        data: {
            labels: salesData.labels || [],
            datasets: [{
                label: 'Ventes mensuelles',
                data: salesData.data || [],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('fr-FR').format(value) + ' F';
                        }
                    }
                }
            }
        }
    });
}

/**
 * Initialisation des animations
 */
function initAnimations() {
    // Observer pour les animations au scroll
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    });
    
    // Observer tous les éléments avec la classe fade-in
    document.querySelectorAll('.fade-in, .slide-in-up').forEach(el => {
        observer.observe(el);
    });
}

/**
 * Initialisation des événements
 */
function initEventListeners() {
    // Événements pour les boutons d'export
    document.addEventListener('click', function(e) {
        if (e.target.matches('[data-export]')) {
            handleExport(e.target.dataset.export);
        }
        
        if (e.target.matches('[data-action]')) {
            handleAction(e.target.dataset.action);
        }
    });
    
    // Événements pour les filtres
    document.querySelectorAll('.filter-input').forEach(input => {
        input.addEventListener('change', updateFilters);
    });
}

/**
 * Gestion des exports
 */
function handleExport(type) {
    console.log(`🔄 Export ${type} demandé`);

    // Afficher un indicateur de chargement
    const exportBtn = document.querySelector(`[data-export="${type}"], [onclick*="${type}"]`);
    let originalText = '';

    if (exportBtn) {
        originalText = exportBtn.innerHTML;
        exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Chargement...';
        exportBtn.disabled = true;
    }

    // Fonction pour restaurer le bouton
    const restoreButton = () => {
        if (exportBtn) {
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
        }
    };

    // Charger les bibliothèques d'export si nécessaire
    if (typeof window.loadExportLibraries === 'function') {
        showNotification('Chargement des bibliothèques d\'export...', 'info');

        window.loadExportLibraries()
            .then(() => {
                showNotification('Préparation de l\'export...', 'info');

                setTimeout(() => {
                    switch(type) {
                        case 'excel':
                            exportToExcel();
                            break;
                        case 'pdf':
                            exportToPDF();
                            break;
                        default:
                            console.warn('Type d\'export non reconnu:', type);
                    }
                    restoreButton();
                }, 500);
            })
            .catch(error => {
                console.error('❌ Erreur lors du chargement des bibliothèques:', error);
                showNotification('Erreur lors du chargement des bibliothèques d\'export', 'error');
                restoreButton();
            });
    } else {
        showNotification('Fonction d\'export non disponible', 'error');
        restoreButton();
    }
}

/**
 * Export Excel
 */
function exportToExcel() {
    if (typeof XLSX === 'undefined') {
        showNotification('Bibliothèque Excel non chargée', 'error');
        return;
    }
    
    const data = [
        ['Indicateur', 'Valeur'],
        ['Total Ventes', window.dashboardData.totalSales || 0],
        ['Factures Payées', window.dashboardData.paidInvoices || 0],
        ['Factures Impayées', window.dashboardData.unpaidInvoices || 0]
    ];
    
    const ws = XLSX.utils.aoa_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Dashboard');
    XLSX.writeFile(wb, 'dashboard-' + new Date().toISOString().split('T')[0] + '.xlsx');
    
    showNotification('Export Excel terminé', 'success');
}

/**
 * Affichage des notifications
 */
function showNotification(message, type = 'info') {
    // Créer ou réutiliser le conteneur de notifications
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        `;
        document.body.appendChild(container);
    }
    
    // Créer la notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    container.appendChild(notification);
    
    // Supprimer automatiquement après 3 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

/**
 * Initialisation des tooltips
 */
function initTooltips() {
    // Initialiser les tooltips Bootstrap si disponible
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * Initialisation des filtres
 */
function initFilters() {
    // Logique des filtres
    const filterForm = document.querySelector('.filters-form');
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            updateDashboardData();
        });
    }
}

/**
 * Initialisation des widgets
 */
function initWidgets() {
    // Animer les compteurs
    animateCounters();
}

/**
 * Animation des compteurs
 */
function animateCounters() {
    document.querySelectorAll('.counter').forEach(counter => {
        const target = parseInt(counter.textContent.replace(/\D/g, ''));
        const duration = 1000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current).toLocaleString('fr-FR');
        }, 16);
    });
}

// Initialisation automatique quand le DOM est prêt
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initDashboard);
} else {
    initDashboard();
}

/**
 * Système de lazy loading pour les sections
 */
function initLazyLoading() {
    // Observer pour le lazy loading des sections
    const lazyObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const section = entry.target;
                const sectionType = section.dataset.lazySection;

                if (sectionType && !section.dataset.loaded) {
                    loadSectionContent(section, sectionType);
                    section.dataset.loaded = 'true';
                    lazyObserver.unobserve(section);
                }
            }
        });
    }, {
        rootMargin: '100px' // Charger 100px avant que la section soit visible
    });

    // Observer toutes les sections avec lazy loading
    document.querySelectorAll('[data-lazy-section]').forEach(section => {
        lazyObserver.observe(section);
    });
}

/**
 * Charger le contenu d'une section de manière asynchrone
 */
function loadSectionContent(section, sectionType) {
    console.log(`🔄 Chargement lazy de la section: ${sectionType}`);

    // Afficher un indicateur de chargement
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'lazy-loading-indicator';
    loadingIndicator.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2 text-muted">Chargement de la section...</p>
        </div>
    `;

    section.appendChild(loadingIndicator);

    // Simuler le chargement asynchrone du contenu
    setTimeout(() => {
        switch(sectionType) {
            case 'charts':
                loadChartsSection(section);
                break;
            case 'reports':
                loadReportsSection(section);
                break;
            case 'tables':
                loadTablesSection(section);
                break;
            default:
                console.warn(`Type de section non reconnu: ${sectionType}`);
        }

        // Supprimer l'indicateur de chargement
        loadingIndicator.remove();

        // Ajouter une animation d'apparition
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        section.style.transition = 'all 0.3s ease';

        setTimeout(() => {
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, 50);

        console.log(`✅ Section ${sectionType} chargée`);
    }, 300); // Délai simulé pour le chargement
}

/**
 * Charger la section des graphiques
 */
function loadChartsSection(section) {
    // Initialiser les graphiques si Chart.js est disponible
    if (window.Chart && typeof initCharts === 'function') {
        initCharts();
    }
}

/**
 * Charger la section des rapports
 */
function loadReportsSection(section) {
    // Initialiser les événements pour les boutons de rapport
    section.querySelectorAll('.generate-report').forEach(btn => {
        btn.addEventListener('click', function() {
            const reportType = this.dataset.report;
            generateReport(reportType);
        });
    });
}

/**
 * Charger la section des tableaux
 */
function loadTablesSection(section) {
    // Initialiser les tableaux avec tri et pagination
    section.querySelectorAll('table').forEach(table => {
        // Ajouter des fonctionnalités de tri si nécessaire
        initTableSorting(table);
    });
}

/**
 * Initialiser le tri des tableaux
 */
function initTableSorting(table) {
    const headers = table.querySelectorAll('th[data-sortable]');
    headers.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            const column = this.dataset.sortable;
            sortTable(table, column);
        });
    });
}

/**
 * Trier un tableau
 */
function sortTable(table, column) {
    // Implémentation basique du tri
    console.log(`Tri du tableau par colonne: ${column}`);
    showNotification(`Tableau trié par ${column}`, 'info');
}

/**
 * Générer un rapport
 */
function generateReport(reportType) {
    console.log(`Génération du rapport: ${reportType}`);
    showNotification(`Génération du rapport ${reportType}...`, 'info');

    // Simuler la génération du rapport
    setTimeout(() => {
        showNotification(`Rapport ${reportType} généré avec succès`, 'success');
    }, 2000);
}

// Export des fonctions pour usage global
window.initDashboard = initDashboard;
window.showNotification = showNotification;
window.handleExport = handleExport;
window.initLazyLoading = initLazyLoading;
