<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use App\Models\User;

class ClearUsersTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:clear {--force : Force the operation without confirmation} {--seed : Run UserSeeder after clearing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vide complètement la table users et exécute optionnellement le seeder (suppression définitive)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== VIDAGE DE LA TABLE USERS ===');

        // Compter les utilisateurs (incluant soft deleted)
        $usersCount = DB::table('users')->count();

        if ($usersCount === 0) {
            $this->info('✅ La table users est déjà vide.');
            
            if ($this->option('seed')) {
                $this->runSeeder();
            }
            
            return 0;
        }

        $this->info("Nombre total d'utilisateurs trouvés : {$usersCount}");

        // Demander confirmation si --force n'est pas utilisé
        if (!$this->option('force')) {
            $this->warn('⚠️  ATTENTION : Cette action va supprimer DÉFINITIVEMENT :');
            $this->warn("   - {$usersCount} utilisateurs");
            $this->warn('⚠️  Cela affectera TOUTES les tables liées (sales, payments, stock_histories, etc.) !');
            $this->warn('⚠️  Les relations avec les rôles et permissions seront aussi supprimées !');
            
            if (!$this->confirm('Êtes-vous sûr de vouloir continuer ?')) {
                $this->info('❌ Opération annulée.');
                return 0;
            }
        }

        try {
            $this->clearUsers($usersCount);
            
            if ($this->option('seed')) {
                $this->runSeeder();
            }
            
            $this->info('✅ Opération terminée avec succès !');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de la suppression : ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function clearUsers($usersCount)
    {
        $this->info('🔄 Vidage de la table users et tables liées...');
        
        try {
            // Étape 1: Supprimer les sessions utilisateurs
            $this->info('1. Suppression des sessions...');
            $sessionsCount = DB::table('sessions')->whereNotNull('user_id')->count();
            if ($sessionsCount > 0) {
                DB::table('sessions')->whereNotNull('user_id')->delete();
                $this->info("   → {$sessionsCount} sessions supprimées");
            }

            // Étape 2: Supprimer les relations model_has_roles (Spatie)
            $this->info('2. Suppression des relations rôles...');
            $roleRelationsCount = DB::table('model_has_roles')->where('model_type', 'App\\Models\\User')->count();
            if ($roleRelationsCount > 0) {
                DB::table('model_has_roles')->where('model_type', 'App\\Models\\User')->delete();
                $this->info("   → {$roleRelationsCount} relations rôles supprimées");
            }

            // Étape 3: Supprimer les relations model_has_permissions (Spatie)
            $this->info('3. Suppression des relations permissions...');
            $permissionRelationsCount = DB::table('model_has_permissions')->where('model_type', 'App\\Models\\User')->count();
            if ($permissionRelationsCount > 0) {
                DB::table('model_has_permissions')->where('model_type', 'App\\Models\\User')->delete();
                $this->info("   → {$permissionRelationsCount} relations permissions supprimées");
            }

            // Étape 4: Mettre à jour les tables avec des clés étrangères vers users
            $this->info('4. Mise à jour des tables liées...');
            
            // Stock histories - set user_id to null ou supprimer
            $stockHistoriesCount = DB::table('stock_histories')->whereNotNull('user_id')->count();
            if ($stockHistoriesCount > 0) {
                DB::table('stock_histories')->delete(); // Supprimer complètement car user_id est required
                $this->info("   → {$stockHistoriesCount} stock_histories supprimées");
            }

            // Expenses - set user_id and updated_by to null
            if (DB::getSchemaBuilder()->hasTable('expenses')) {
                $expensesCount = DB::table('expenses')->count();
                if ($expensesCount > 0) {
                    DB::table('expenses')->delete(); // Supprimer car user_id est required
                    $this->info("   → {$expensesCount} expenses supprimées");
                }
            }

            // Supplies notifications
            if (DB::getSchemaBuilder()->hasTable('supplies_notifications')) {
                $notificationsCount = DB::table('supplies_notifications')->count();
                if ($notificationsCount > 0) {
                    DB::table('supplies_notifications')->delete();
                    $this->info("   → {$notificationsCount} notifications supprimées");
                }
            }

            // Destinations - set user_id to null
            if (DB::getSchemaBuilder()->hasTable('destinations')) {
                $destinationsUpdated = DB::table('destinations')->whereNotNull('user_id')->update(['user_id' => null]);
                if ($destinationsUpdated > 0) {
                    $this->info("   → {$destinationsUpdated} destinations mises à jour");
                }
            }

            // Étape 5: Supprimer les utilisateurs
            $this->info('5. Suppression des utilisateurs...');
            DB::table('users')->delete(); // Supprimer tous les utilisateurs

            // Étape 6: Reset auto-increment
            $this->info('6. Reset de l\'auto-increment...');
            DB::statement('ALTER TABLE users AUTO_INCREMENT = 1');

            $this->info("✅ {$usersCount} utilisateurs supprimés définitivement");
            $this->info('✅ Toutes les relations ont été nettoyées');
            $this->info('✅ Auto-increment remis à 1');
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    private function runSeeder()
    {
        $this->info('🌱 Exécution du UserSeeder...');
        
        try {
            Artisan::call('db:seed', ['--class' => 'UserSeeder']);
            $this->info('✅ UserSeeder exécuté avec succès !');
            
            // Afficher les utilisateurs créés
            $users = User::all();
            $this->info("📊 Utilisateurs créés :");
            foreach ($users as $user) {
                $roles = $user->getRoleNames()->implode(', ');
                $this->info("   - {$user->name} ({$user->email}) - Rôles: {$roles}");
            }
            
        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de l\'exécution du seeder : ' . $e->getMessage());
        }
    }
}
