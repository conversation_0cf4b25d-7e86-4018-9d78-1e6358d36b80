/**
 * Dashboard Comptable - Styles Optimisés
 * Version allégée pour améliorer les performances de chargement
 */

/* Optimisations de base pour les performances */
* {
    box-sizing: border-box;
}

/* Désactiver les animations pendant le chargement initial */
.loading-mode * {
    animation-duration: 0.01ms !important;
    animation-delay: 0.01ms !important;
    transition-duration: 0.01ms !important;
    transition-delay: 0.01ms !important;
}

/* Styles pour le lazy loading */
[data-lazy-section] {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

[data-lazy-section].lazy-loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Optimisations pour les graphiques */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

.chart-loading::before {
    content: "📊";
    margin-right: 10px;
    font-size: 24px;
}

/* Styles pour les boutons d'export optimisés */
.export-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
}

.export-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.export-btn.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Optimisations pour les tableaux */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-optimized {
    table-layout: fixed;
    width: 100%;
}

.table-optimized th,
.table-optimized td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Styles pour les cartes de statistiques */
.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 15px;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-card-label {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Optimisations pour les images */
img[data-src] {
    background: #f8f9fa;
    min-height: 100px;
}

img.lazy-loaded {
    transition: opacity 0.3s ease;
}

/* Styles pour les alertes et notifications */
.alert-optimized {
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border-left: 4px solid;
}

.alert-success {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.alert-warning {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.alert-danger {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.alert-info {
    background: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* Optimisations pour les formulaires */
.form-optimized .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    transition: border-color 0.15s ease;
}

.form-optimized .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    outline: none;
}

/* Styles pour les badges et étiquettes */
.badge-optimized {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.badge-primary { background-color: #007bff; color: white; }
.badge-success { background-color: #28a745; color: white; }
.badge-warning { background-color: #ffc107; color: #212529; }
.badge-danger { background-color: #dc3545; color: white; }
.badge-info { background-color: #17a2b8; color: white; }
.badge-light { background-color: #f8f9fa; color: #212529; }
.badge-dark { background-color: #343a40; color: white; }

/* Optimisations pour les barres de progression */
.progress-optimized {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-optimized {
    height: 100%;
    background-color: #007bff;
    transition: width 0.3s ease;
}

/* Styles pour les modales optimisées */
.modal-optimized {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-optimized.show {
    opacity: 1;
    visibility: visible;
}

.modal-content-optimized {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-optimized.show .modal-content-optimized {
    transform: scale(1);
}

/* Optimisations pour les écrans mobiles */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 15px;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .export-btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* Optimisations pour l'impression */
@media print {
    .export-btn,
    .btn,
    .sidebar,
    .navbar {
        display: none !important;
    }
    
    .chart-container {
        break-inside: avoid;
    }
    
    .stat-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Styles pour les indicateurs de chargement */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Optimisations pour réduire le reflow */
.no-reflow {
    contain: layout style paint;
}

/* Styles pour améliorer la lisibilité */
.text-optimized {
    line-height: 1.5;
    color: #333;
}

.text-muted-optimized {
    color: #6c757d;
}

/* Optimisations pour les transitions */
.smooth-transition {
    transition: all 0.2s ease;
}

.fast-transition {
    transition: all 0.1s ease;
}

/* Styles pour les éléments critiques */
.critical-content {
    will-change: auto;
    transform: translateZ(0);
}
