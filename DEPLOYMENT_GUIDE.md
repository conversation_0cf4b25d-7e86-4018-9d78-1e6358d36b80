# Guide de Déploiement Gradis sur Namecheap

## Prérequis

### Serveur Namecheap
- **Hébergement recommandé**: Shared Hosting ou VPS
- **PHP**: Version 8.1 ou supérieure
- **MySQL**: Version 5.7 ou supérieure
- **Extensions PHP requises**:
  - BCMath
  - Ctype
  - Fileinfo
  - JSON
  - Mbstring
  - OpenSSL
  - PDO
  - Tokenizer
  - XML
  - GD (pour l'optimisation d'images)
  - Redis (recommandé pour le cache)

### Outils locaux
- Git
- Composer
- Node.js et NPM
- PHP CLI

## Étapes de Déploiement

### 1. Préparation du Serveur

#### Configuration de la Base de Données
1. Connectez-vous au cPanel de Namecheap
2. Créez une nouvelle base de données MySQL
3. Créez un utilisateur avec tous les privilèges sur cette base
4. Notez les informations de connexion

#### Configuration du Domaine
1. Pointez votre domaine vers le dossier `public` de Laravel
2. Configurez les sous-domaines si nécessaire
3. Activez SSL/TLS (Let's Encrypt disponible gratuitement)

### 2. Upload des Fichiers

#### Méthode 1: Git (Recommandée)
```bash
# Sur le serveur (via SSH si disponible)
git clone https://github.com/votre-repo/gradis.git
cd gradis
```

#### Méthode 2: FTP/SFTP
1. Compressez le projet localement (excluez node_modules, .git, storage/logs)
2. Uploadez via FileZilla ou le gestionnaire de fichiers cPanel
3. Décompressez sur le serveur

### 3. Configuration de l'Environnement

#### Fichier .env
```bash
# Copiez le fichier de production
cp .env.production .env

# Modifiez les variables selon votre configuration Namecheap
nano .env
```

#### Variables importantes à configurer:
```env
APP_NAME=Gradis
APP_ENV=production
APP_DEBUG=false
APP_URL=https://votre-domaine.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=votre_db_name
DB_USERNAME=votre_db_user
DB_PASSWORD=votre_db_password

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

MAIL_MAILER=smtp
MAIL_HOST=mail.votre-domaine.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=votre_mail_password
MAIL_ENCRYPTION=tls
```

### 4. Installation et Configuration

#### Installation des dépendances
```bash
# Dépendances PHP
composer install --no-dev --optimize-autoloader

# Dépendances Node.js
npm ci --production

# Génération de la clé d'application
php artisan key:generate
```

#### Migration de la base de données
```bash
# Exécuter les migrations
php artisan migrate --force

# Seeder de production (optionnel)
php artisan db:seed --class=ProductionSeeder
```

#### Compilation des assets
```bash
# Build de production
npm run build

# Optimisation Laravel
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 5. Configuration des Permissions

```bash
# Permissions pour les dossiers de stockage
chmod -R 775 storage
chmod -R 775 bootstrap/cache

# Propriétaire des fichiers (si vous avez accès SSH)
chown -R www-data:www-data storage bootstrap/cache
```

### 6. Configuration du Serveur Web

#### .htaccess (Apache - généralement inclus)
Le fichier `public/.htaccess` devrait être automatiquement configuré.

#### Configuration Nginx (si applicable)
```nginx
server {
    listen 80;
    server_name votre-domaine.com;
    root /path/to/gradis/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";

    index index.html index.htm index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

### 7. Configuration SSL

#### Let's Encrypt (Gratuit)
1. Activez SSL dans cPanel
2. Sélectionnez "Let's Encrypt"
3. Configurez le renouvellement automatique

#### SSL Payant
1. Achetez un certificat SSL
2. Installez-le via cPanel
3. Forcez HTTPS dans .env: `FORCE_HTTPS=true`

### 8. Optimisations de Performance

#### Redis (Recommandé)
```bash
# Installation Redis (si pas déjà installé)
# Contactez le support Namecheap pour l'activation

# Configuration dans .env
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
```

#### Optimisations PHP
Ajoutez dans `.htaccess` ou demandez au support:
```apache
# Compression GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache des fichiers statiques
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

### 9. Sécurité

#### Fichiers à protéger
Assurez-vous que ces fichiers ne sont pas accessibles publiquement:
- `.env`
- `composer.json`
- `package.json`
- Dossier `storage/`
- Dossier `bootstrap/cache/`

#### Configuration de sécurité
```bash
# Générer une nouvelle clé d'application
php artisan key:generate

# Configurer les permissions restrictives
chmod 644 .env
chmod -R 755 public
```

### 10. Monitoring et Maintenance

#### Logs
- Logs Laravel: `storage/logs/`
- Logs serveur: Accessible via cPanel

#### Sauvegardes
```bash
# Script de sauvegarde automatique
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u username -p database_name > backup_$DATE.sql
tar -czf gradis_backup_$DATE.tar.gz . --exclude=node_modules --exclude=.git
```

#### Mises à jour
```bash
# Processus de mise à jour
git pull origin main
composer install --no-dev --optimize-autoloader
npm run build
php artisan migrate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Dépannage

### Erreurs Communes

#### Erreur 500
1. Vérifiez les logs: `storage/logs/laravel.log`
2. Vérifiez les permissions des dossiers
3. Vérifiez la configuration .env

#### Base de données inaccessible
1. Vérifiez les informations de connexion dans .env
2. Testez la connexion depuis cPanel
3. Vérifiez que l'utilisateur a les bonnes permissions

#### Assets non chargés
1. Vérifiez que `npm run build` a été exécuté
2. Vérifiez les permissions du dossier public
3. Vérifiez la configuration APP_URL

### Support

#### Namecheap
- Support technique 24/7
- Documentation: https://www.namecheap.com/support/
- Chat en direct disponible

#### Laravel
- Documentation: https://laravel.com/docs
- Communauté: https://laracasts.com/

## Checklist de Déploiement

- [ ] Serveur configuré avec PHP 8.1+
- [ ] Base de données MySQL créée
- [ ] Domaine pointé vers le dossier public
- [ ] SSL activé
- [ ] Fichiers uploadés
- [ ] .env configuré
- [ ] Dépendances installées
- [ ] Migrations exécutées
- [ ] Assets compilés
- [ ] Permissions configurées
- [ ] Cache optimisé
- [ ] Tests de fonctionnement
- [ ] Monitoring configuré
- [ ] Sauvegardes planifiées

## Contact

Pour toute question concernant le déploiement, contactez l'équipe de développement.
