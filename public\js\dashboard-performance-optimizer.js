/**
 * Optimiseur de performance pour le tableau de bord GRADIS
 * Script pour améliorer les performances et diagnostiquer les problèmes
 */

class DashboardPerformanceOptimizer {
    constructor() {
        this.isOptimized = false;
        this.originalIntervals = [];
        this.init();
    }

    init() {
        console.log('🚀 Optimiseur de performance du dashboard initialisé');
        this.addOptimizationControls();
        this.monitorPerformance();
        
        // Auto-optimisation si la page met plus de 3 secondes à charger
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            if (loadTime > 3000) {
                console.warn('⚠️ Chargement lent détecté (' + loadTime.toFixed(0) + 'ms), activation de l\'optimisation automatique');
                this.enableOptimizations();
            }
        });
    }

    addOptimizationControls() {
        // Créer un panneau de contrôle d'optimisation
        const controlPanel = document.createElement('div');
        controlPanel.id = 'performance-optimizer-panel';
        controlPanel.style.cssText = `
            position: fixed;
            top: 50px;
            right: 10px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Segoe UI', sans-serif;
            font-size: 12px;
            z-index: 10000;
            min-width: 250px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            display: none;
        `;

        controlPanel.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <h4 style="margin: 0; font-size: 14px;">⚡ Optimiseur Performance</h4>
                <button onclick="this.parentElement.parentElement.style.display='none'" 
                        style="background: none; border: none; color: white; font-size: 16px; cursor: pointer;">×</button>
            </div>
            
            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px;">
                    <input type="checkbox" id="disable-animations" style="margin-right: 5px;">
                    Désactiver les animations
                </label>
                <label style="display: block; margin-bottom: 5px;">
                    <input type="checkbox" id="disable-auto-refresh" checked style="margin-right: 5px;">
                    Désactiver l'auto-refresh
                </label>
                <label style="display: block; margin-bottom: 5px;">
                    <input type="checkbox" id="reduce-polling" checked style="margin-right: 5px;">
                    Réduire les mises à jour
                </label>
            </div>
            
            <div style="margin-bottom: 10px;">
                <button onclick="window.performanceOptimizer.clearAllCaches()" 
                        style="background: #2ecc71; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px; font-size: 11px;">
                    🗑️ Vider Cache
                </button>
                <button onclick="window.performanceOptimizer.enableOptimizations()" 
                        style="background: #3498db; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px; font-size: 11px;">
                    ⚡ Optimiser
                </button>
            </div>
            
            <div id="optimization-status" style="font-size: 11px; color: #ecf0f1;">
                Status: En attente
            </div>
        `;

        document.body.appendChild(controlPanel);

        // Bouton pour afficher/masquer le panneau
        const toggleButton = document.createElement('button');
        toggleButton.innerHTML = '⚡';
        toggleButton.title = 'Optimiseur de performance';
        toggleButton.style.cssText = `
            position: fixed;
            top: 50px;
            right: 10px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            z-index: 10001;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        `;

        toggleButton.addEventListener('click', () => {
            const isVisible = controlPanel.style.display !== 'none';
            controlPanel.style.display = isVisible ? 'none' : 'block';
            toggleButton.style.right = isVisible ? '10px' : '270px';
        });

        document.body.appendChild(toggleButton);

        // Ajouter les event listeners pour les checkboxes
        this.setupControlListeners();
    }

    setupControlListeners() {
        document.addEventListener('change', (e) => {
            if (e.target.id === 'disable-animations') {
                this.toggleAnimations(!e.target.checked);
            } else if (e.target.id === 'disable-auto-refresh') {
                this.toggleAutoRefresh(!e.target.checked);
            } else if (e.target.id === 'reduce-polling') {
                this.togglePolling(!e.target.checked);
            }
        });
    }

    enableOptimizations() {
        console.log('🔧 Activation des optimisations...');
        
        // Marquer les checkboxes appropriées
        document.getElementById('disable-animations').checked = true;
        document.getElementById('disable-auto-refresh').checked = true;
        document.getElementById('reduce-polling').checked = true;
        
        // Appliquer les optimisations
        this.toggleAnimations(false);
        this.toggleAutoRefresh(false);
        this.togglePolling(false);
        this.clearAllCaches();
        
        this.isOptimized = true;
        this.updateStatus('✅ Optimisé - Performance améliorée');
        
        console.log('✅ Optimisations activées');
    }

    toggleAnimations(enable) {
        if (!enable) {
            // Désactiver toutes les animations
            const style = document.createElement('style');
            style.id = 'disable-animations-style';
            style.textContent = `
                *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-delay: 0.01ms !important;
                    transition-duration: 0.01ms !important;
                    transition-delay: 0.01ms !important;
                }
            `;
            document.head.appendChild(style);
        } else {
            // Réactiver les animations
            const style = document.getElementById('disable-animations-style');
            if (style) style.remove();
        }
    }

    toggleAutoRefresh(enable) {
        if (!enable) {
            // Désactiver l'auto-refresh
            if (typeof autoRefreshEnabled !== 'undefined') {
                window.autoRefreshEnabled = false;
            }
            
            const autoRefreshToggle = document.getElementById('autoRefreshToggle');
            if (autoRefreshToggle) {
                autoRefreshToggle.checked = false;
            }
            
            // Arrêter tous les intervalles de refresh
            if (typeof countdownInterval !== 'undefined' && countdownInterval) {
                clearInterval(countdownInterval);
            }
            if (typeof refreshInterval !== 'undefined' && refreshInterval) {
                clearInterval(refreshInterval);
            }
        }
    }

    togglePolling(enable) {
        if (!enable) {
            // Réduire drastiquement la fréquence des mises à jour
            const intervals = ['countdownInterval', 'refreshInterval', 'updateInterval'];
            intervals.forEach(intervalName => {
                if (typeof window[intervalName] !== 'undefined' && window[intervalName]) {
                    clearInterval(window[intervalName]);
                    window[intervalName] = null;
                }
            });
        }
    }

    clearAllCaches() {
        console.log('🗑️ Vidage de tous les caches...');
        
        // Vider le cache du navigateur
        if ('caches' in window) {
            caches.keys().then(names => {
                names.forEach(name => caches.delete(name));
            });
        }
        
        // Vider le localStorage et sessionStorage
        localStorage.clear();
        sessionStorage.clear();
        
        // Faire une requête pour vider le cache côté serveur
        fetch('/accountant/clear-cache', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                'Content-Type': 'application/json'
            }
        }).then(response => {
            if (response.ok) {
                console.log('✅ Cache serveur vidé');
                this.updateStatus('✅ Cache vidé - Rechargement recommandé');
            }
        }).catch(error => {
            console.log('❌ Erreur lors du vidage du cache serveur:', error);
        });
    }

    monitorPerformance() {
        // Surveiller les performances en continu
        setInterval(() => {
            if (performance.memory) {
                const memory = performance.memory;
                const usedMB = memory.usedJSHeapSize / 1024 / 1024;
                
                if (usedMB > 100 && !this.isOptimized) {
                    console.warn('⚠️ Utilisation mémoire élevée (' + usedMB.toFixed(1) + 'MB)');
                    this.updateStatus('⚠️ Mémoire élevée - Optimisation recommandée');
                }
            }
        }, 30000); // Vérifier toutes les 30 secondes
    }

    updateStatus(message) {
        const statusElement = document.getElementById('optimization-status');
        if (statusElement) {
            statusElement.textContent = 'Status: ' + message;
        }
    }
}

// Initialiser l'optimiseur
window.performanceOptimizer = new DashboardPerformanceOptimizer();

// Fonctions utilitaires globales
window.optimizeDashboard = () => window.performanceOptimizer.enableOptimizations();
window.clearDashboardCache = () => window.performanceOptimizer.clearAllCaches();

console.log('⚡ Optimiseur de performance du dashboard chargé');
console.log('💡 Utilisez optimizeDashboard() pour activer toutes les optimisations');
console.log('💡 Utilisez clearDashboardCache() pour vider tous les caches');
