/* Dashboard Professional - Styles Optimisés */

/* Styles pour le lazy loading optimisé */
.lazy-loading-indicator {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 2rem;
    margin: 1rem 0;
    border: 1px solid rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
    text-align: center;
}

.lazy-loading-indicator .spinner-border {
    width: 2rem;
    height: 2rem;
}

[data-lazy-section] {
    min-height: 100px;
    transition: all 0.3s ease;
}

[data-lazy-section][data-loaded="true"] {
    opacity: 1;
    transform: translateY(0);
}

/* Optimisation pour réduire les reflows */
[data-lazy-section]:not([data-loaded]) {
    contain: layout style paint;
}

/* Styles optimisés pour les cartes de la bannière */
.dashboard-header-data {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15);
    position: relative;
    overflow: hidden;
}

.dashboard-header-data::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.header-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.header-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.2);
}

.header-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.header-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-card-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-card-trend {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    color: white;
    font-weight: 600;
}

/* Styles pour les sections animées */
.animated-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent, #667eea, transparent);
    margin: 2rem 0;
    border-radius: 1px;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

.section-title {
    color: #2d3748;
    font-weight: 600;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    padding-left: 1rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 2px;
}

/* Styles pour les graphiques avancés */
.advanced-chart-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.advanced-chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.advanced-chart-header {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.advanced-chart-title {
    font-weight: 600;
    color: #2d3748;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.advanced-chart-area {
    padding: 1.5rem;
    position: relative;
}

/* Styles pour les boutons d'export */
.export-btn {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: #4a5568;
    width: 100%;
    margin-bottom: 0.75rem;
}

.export-btn:hover {
    border-color: #667eea;
    background: #f7fafc;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
    color: #667eea;
}

.export-btn i {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

.export-btn span {
    font-weight: 600;
    font-size: 0.9rem;
}

.export-btn small {
    color: #718096;
    font-size: 0.75rem;
}

/* Styles pour les rapports prédéfinis */
.report-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    background: white;
}

.report-item:hover {
    border-color: #667eea;
    background: #f7fafc;
    transform: translateX(5px);
}

.report-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    flex-shrink: 0;
}

.report-info {
    flex: 1;
}

.report-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.report-description {
    color: #718096;
    font-size: 0.85rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .header-card {
        margin-bottom: 1rem;
    }
    
    .header-card-value {
        font-size: 1.5rem;
    }
    
    .advanced-chart-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .export-btn {
        padding: 0.75rem;
    }
    
    .report-item {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .report-icon {
        margin-right: 0;
    }
}
