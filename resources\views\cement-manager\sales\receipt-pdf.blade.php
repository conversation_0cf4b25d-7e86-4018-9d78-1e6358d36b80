<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reçu de Vente #{{ $sale->id }}</title>
    <style>
        @page {
            size: A5 portrait;
            margin: 10mm;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .receipt-container {
            width: 100%;
            max-width: 128mm; /* A5 width minus margins */
            margin: 0 auto;
            padding: 0;
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 25px 15px;
            margin-bottom: 20px;
            border-radius: 0;
        }

        .company-logo {
            width: 70px;
            height: 70px;
            margin: 0 auto 12px;
            background: rgba(255,255,255,0.15);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 22px;
            color: white;
            font-weight: bold;
        }

        .receipt-title {
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin: 10px 0 5px;
            letter-spacing: 1px;
        }

        .receipt-subtitle {
            font-size: 11px;
            color: rgba(255,255,255,0.9);
            margin-bottom: 15px;
        }

        .receipt-number {
            background: rgba(255,255,255,0.2);
            padding: 6px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            display: inline-block;
            margin-bottom: 8px;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .receipt-date {
            font-size: 10px;
            color: rgba(255,255,255,0.8);
        }
        
        .receipt-section {
            margin: 15px 0;
            padding: 0;
        }

        .section-card {
            background: white;
            border-radius: 8px;
            padding: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .section-title {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 11px;
            display: flex;
            align-items: center;
        }

        .section-icon {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 8px;
            color: white;
            font-weight: bold;
        }

        .product-icon {
            background: #667eea;
        }

        .customer-icon {
            background: #f093fb;
        }

        .payment-icon {
            background: #4facfe;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 10px;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
            flex: 1;
        }
        
        .info-value {
            color: #333;
            text-align: right;
            flex: 1;
            font-weight: 500;
        }
        
        .product-details {
            background: #e3f2fd;
            border-left-color: #2196f3;
        }
        
        .customer-details {
            background: #f3e5f5;
            border-left-color: #9c27b0;
        }
        
        .payment-details {
            background: #e8f5e8;
            border-left-color: #4caf50;
        }
        
        .total-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: white;
        }

        .total-breakdown {
            margin-bottom: 10px;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 10px;
            color: rgba(255,255,255,0.9);
        }

        .total-amount {
            font-size: 18px;
            font-weight: bold;
            color: white;
            text-align: center;
            margin-top: 10px;
        }
        
        .subtotal-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 10px;
        }
        
        .receipt-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
            font-size: 9px;
            color: #666;
        }
        
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 25px;
            padding-top: 15px;
        }
        
        .signature-box {
            text-align: center;
            width: 45%;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 30px;
            padding-top: 3px;
            font-size: 9px;
            color: #666;
        }
        
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 8px;
            font-weight: bold;
            text-transform: uppercase;
            color: white;
        }

        .status-completed {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .status-pending {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .status-cancelled {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .highlight-value {
            background: #fef5e7;
            padding: 2px 6px;
            border-radius: 4px;
            color: #c05621;
            font-weight: bold;
        }

        /* Styles pour le tableau des produits PDF */
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            background: white;
            border: 1px solid #e2e8f0;
        }

        .products-table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .products-table thead th {
            padding: 8px 6px;
            text-align: left;
            font-weight: bold;
            font-size: 9px;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .products-table tbody tr {
            border-bottom: 1px solid #f0f0f0;
        }

        .products-table tbody tr:last-child {
            border-bottom: none;
        }

        .products-table tbody td {
            padding: 8px 6px;
            font-size: 9px;
            color: #2d3748;
            border: 1px solid #e2e8f0;
            vertical-align: middle;
        }

        .product-name {
            font-weight: bold;
            color: #4a5568;
            font-size: 10px;
        }

        .product-reference {
            font-size: 8px;
            color: #718096;
            font-style: italic;
            margin-top: 2px;
        }

        .quantity-badge {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 3px 6px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 8px;
            display: inline-block;
            text-align: center;
            min-width: 30px;
        }

        .price-cell {
            text-align: right;
            font-weight: bold;
            color: #2d3748;
        }

        .discount-cell {
            text-align: right;
            color: #e53e3e;
            font-weight: bold;
        }

        .total-cell {
            text-align: right;
            font-weight: bold;
            color: #667eea;
            font-size: 10px;
        }

        .table-summary {
            background: #f7fafc;
            border-top: 2px solid #e2e8f0;
        }

        .table-summary td {
            font-weight: bold;
            color: #4a5568;
            padding: 10px 6px !important;
            font-size: 10px;
        }

        .no-products {
            text-align: center;
            padding: 20px 10px;
            color: #718096;
            font-style: italic;
            font-size: 10px;
        }
        
        .qr-code {
            text-align: center;
            margin: 15px 0;
        }
        
        .company-info {
            font-size: 8px;
            color: #888;
            text-align: center;
            margin-top: 10px;
        }
        
        .divider {
            border-top: 1px solid #ddd;
            margin: 10px 0;
        }
        
        .highlight {
            background: #ffffcc;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- En-tête -->
        <div class="receipt-header">
            <div class="company-logo">CT</div>
            <h1 class="receipt-title">CIMTOGO</h1>
            <div class="receipt-subtitle">Ciment de Qualité Supérieure</div>
            <div class="receipt-number">REÇU #{{ str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}</div>
            <div class="receipt-date">{{ $sale->created_at->format('d/m/Y à H:i') }}</div>
        </div>

        <!-- Tableau des produits -->
        <div class="receipt-section">
            <div class="section-card">
                <div class="section-title">
                    <div class="section-icon product-icon">📦</div>
                    DÉTAILS DES PRODUITS
                </div>

                @if($sale->supply && $sale->supply->details && $sale->supply->details->count() > 0)
                    <table class="products-table">
                        <thead>
                            <tr>
                                <th style="width: 40%;">Produit</th>
                                <th style="width: 15%; text-align: center;">Qté</th>
                                <th style="width: 15%; text-align: right;">P.U.</th>
                                @if($sale->discount_per_ton > 0)
                                <th style="width: 15%; text-align: right;">Remise</th>
                                @endif
                                <th style="width: 15%; text-align: right;">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $subtotal = $sale->quantity * $sale->unit_price;
                                $discount_total = $sale->discount_per_ton * $sale->quantity;
                                $line_total = $subtotal - $discount_total;
                            @endphp
                            <tr>
                                <td>
                                    <div class="product-name">
                                        {{ $sale->supply->details->first()->product->name ?? 'Produit non spécifié' }}
                                    </div>
                                    @if($sale->supply->reference)
                                    <div class="product-reference">
                                        Réf: {{ $sale->supply->reference }}
                                    </div>
                                    @endif
                                </td>
                                <td style="text-align: center;">
                                    <span class="quantity-badge">
                                        {{ number_format($sale->quantity, 2, ',', ' ') }} T
                                    </span>
                                </td>
                                <td class="price-cell">
                                    {{ number_format($sale->unit_price, 0, ',', ' ') }}
                                </td>
                                @if($sale->discount_per_ton > 0)
                                <td class="discount-cell">
                                    -{{ number_format($sale->discount_per_ton, 0, ',', ' ') }}
                                </td>
                                @endif
                                <td class="total-cell">
                                    {{ number_format($line_total, 0, ',', ' ') }}
                                </td>
                            </tr>
                        </tbody>
                        <tfoot class="table-summary">
                            <tr>
                                <td colspan="{{ $sale->discount_per_ton > 0 ? '4' : '3' }}" style="text-align: right; font-weight: bold;">
                                    TOTAL GÉNÉRAL:
                                </td>
                                <td class="total-cell" style="font-size: 11px; color: #667eea;">
                                    {{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                @else
                    <div class="no-products">
                        <p>Aucun produit trouvé pour cette vente</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Informations du client -->
        <div class="receipt-section">
            <div class="section-card">
                <div class="section-title">
                    <div class="section-icon customer-icon">👤</div>
                    INFORMATIONS CLIENT
                </div>
                <div class="info-row">
                    <span class="info-label">Nom:</span>
                    <span class="info-value"><strong>{{ $sale->customer_name }}</strong></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Téléphone:</span>
                    <span class="info-value">{{ $sale->customer_phone }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Adresse:</span>
                    <span class="info-value">{{ $sale->customer_address }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Ville:</span>
                    <span class="info-value">{{ $sale->city->name ?? 'N/A' }}</span>
                </div>
            </div>
        </div>

        <!-- Informations de paiement -->
        <div class="receipt-section">
            <div class="section-card">
                <div class="section-title">
                    <div class="section-icon payment-icon">💳</div>
                    DÉTAILS DU PAIEMENT
                </div>
                <div class="info-row">
                    <span class="info-label">Mode de paiement:</span>
                    <span class="info-value">
                        @if($sale->payment_method === 'cash')
                            <span class="status-badge status-completed">COMPTANT</span>
                        @else
                            <span class="status-badge status-pending">CRÉDIT</span>
                        @endif
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Statut:</span>
                    <span class="info-value">
                        @if($sale->status === 'completed')
                            <span class="status-badge status-completed">TERMINÉ</span>
                        @elseif($sale->status === 'pending')
                            <span class="status-badge status-pending">EN ATTENTE</span>
                        @else
                            <span class="status-badge status-cancelled">ANNULÉ</span>
                        @endif
                    </span>
                </div>
                @if($sale->trips)
                <div class="info-row">
                    <span class="info-label">Nombre de voyages:</span>
                    <span class="info-value"><strong>{{ $sale->trips }}</strong></span>
                </div>
                @endif
            </div>
        </div>



        <!-- Code QR (si disponible) -->
        @if($sale->qr_code)
        <div class="qr-code">
            <img src="data:image/png;base64,{{ $sale->qr_code }}" alt="QR Code" style="width: 60px; height: 60px;">
        </div>
        @endif

        <!-- Signatures -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line">Signature du Client</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">Signature du Vendeur</div>
            </div>
        </div>

        <!-- Pied de page -->
        <div class="receipt-footer">
            <p><strong>CIMTOGO - Ciment de qualité pour vos constructions</strong></p>
            <p>Merci pour votre confiance !</p>
            <p>Ce reçu fait foi de votre achat - Conservez-le précieusement</p>
            <div class="company-info">
                <p>Généré le {{ now()->format('d/m/Y à H:i:s') }} par {{ auth()->user()->name ?? 'Système' }}</p>
                <p>Contact: <EMAIL> | Tél: +228 XX XX XX XX</p>
            </div>
        </div>
    </div>
</body>
</html>
