<?php

echo "🔍 DIAGNOSTIC COMPLET DES GRAPHIQUES\n";
echo "===================================\n\n";

$viewPath = 'resources/views/admin/dashboard.blade.php';

if (!file_exists($viewPath)) {
    echo "❌ Fichier vue non trouvé: $viewPath\n";
    exit(1);
}

$content = file_get_contents($viewPath);

echo "📊 VÉRIFICATION 1: CONTENEURS HTML\n";
echo "----------------------------------\n";

// Vérifier les conteneurs de graphiques
$containers = [
    'revenueChart',
    'resourcesChart', 
    'categoryRevenueChart',
    'cementOrdersChart'
];

foreach ($containers as $container) {
    if (strpos($content, "id=\"$container\"") !== false) {
        echo "✅ Conteneur #$container trouvé\n";
    } else {
        echo "❌ Conteneur #$container MANQUANT\n";
    }
}

echo "\n🔧 VÉRIFICATION 2: JAVASCRIPT\n";
echo "-----------------------------\n";

// Vérifier ApexCharts
if (strpos($content, 'apexcharts') !== false) {
    echo "✅ ApexCharts CDN trouvé\n";
} else {
    echo "❌ ApexCharts CDN MANQUANT\n";
}

// Vérifier les données JSON
$jsonChecks = [
    '@json($monthlyOrders' => 'Données commandes',
    '@json($monthlyCementOrders' => 'Données ciment', 
    '@json($revenueByCategory' => 'Données catégories'
];

foreach ($jsonChecks as $pattern => $description) {
    if (strpos($content, $pattern) !== false) {
        echo "✅ $description trouvées\n";
    } else {
        echo "❌ $description MANQUANTES\n";
    }
}

echo "\n📋 VÉRIFICATION 3: DONNÉES CONTRÔLEUR\n";
echo "------------------------------------\n";

// Vérifier le contrôleur
$controllerPath = 'app/Http/Controllers/Admin/DashboardController.php';
if (file_exists($controllerPath)) {
    $controllerContent = file_get_contents($controllerPath);
    
    $dataChecks = [
        'monthlyOrders' => strpos($controllerContent, 'monthlyOrders') !== false,
        'monthlyCementOrders' => strpos($controllerContent, 'monthlyCementOrders') !== false,
        'revenueByCategory' => strpos($controllerContent, 'revenueByCategory') !== false,
        'chartData' => strpos($controllerContent, 'chartData') !== false
    ];
    
    foreach ($dataChecks as $data => $found) {
        if ($found) {
            echo "✅ Variable $data dans contrôleur\n";
        } else {
            echo "❌ Variable $data MANQUANTE dans contrôleur\n";
        }
    }
} else {
    echo "❌ Contrôleur non trouvé\n";
}

echo "\n🌐 CRÉATION D'UNE PAGE DE TEST SIMPLE\n";
echo "====================================\n";

// Créer une page de test simple pour les graphiques
$testPageContent = '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Graphiques GRADIS</title>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart-container { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🧪 Test des Graphiques GRADIS</h1>
    
    <div id="status" class="status">⏳ Chargement...</div>
    
    <div class="chart-container">
        <h3>📊 Graphique Test 1</h3>
        <div id="testChart1" style="height: 300px;"></div>
    </div>
    
    <div class="chart-container">
        <h3>📈 Graphique Test 2</h3>
        <div id="testChart2" style="height: 300px;"></div>
    </div>

    <script>
        console.log("🚀 Début du test des graphiques");
        
        // Vérifier que ApexCharts est chargé
        if (typeof ApexCharts === "undefined") {
            document.getElementById("status").innerHTML = "❌ ApexCharts non chargé";
            document.getElementById("status").className = "status error";
            console.error("❌ ApexCharts non disponible");
        } else {
            console.log("✅ ApexCharts chargé:", ApexCharts.version);
            document.getElementById("status").innerHTML = "✅ ApexCharts chargé (v" + ApexCharts.version + ")";
            document.getElementById("status").className = "status success";
            
            // Test graphique 1 - Simple
            try {
                const options1 = {
                    series: [{
                        name: "Test",
                        data: [10, 20, 15, 25, 30, 20]
                    }],
                    chart: {
                        type: "line",
                        height: 300
                    },
                    xaxis: {
                        categories: ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"]
                    }
                };
                
                const chart1 = new ApexCharts(document.querySelector("#testChart1"), options1);
                chart1.render();
                console.log("✅ Graphique 1 créé");
                
            } catch (error) {
                console.error("❌ Erreur graphique 1:", error);
            }
            
            // Test graphique 2 - Donut
            try {
                const options2 = {
                    series: [44, 55, 13, 43],
                    chart: {
                        type: "donut",
                        height: 300
                    },
                    labels: ["Ciment", "Fer", "Sable", "Gravier"]
                };
                
                const chart2 = new ApexCharts(document.querySelector("#testChart2"), options2);
                chart2.render();
                console.log("✅ Graphique 2 créé");
                
            } catch (error) {
                console.error("❌ Erreur graphique 2:", error);
            }
        }
    </script>
</body>
</html>';

// Sauvegarder la page de test
$testPagePath = 'public/test-charts.html';
if (file_put_contents($testPagePath, $testPageContent)) {
    echo "✅ Page de test créée: $testPagePath\n";
    echo "🌐 Accédez à: http://127.0.0.1:8000/test-charts.html\n";
} else {
    echo "❌ Erreur création page de test\n";
}

echo "\n🔧 CRÉATION D'UN SCRIPT DE RÉPARATION\n";
echo "=====================================\n";

// Créer un script de réparation simple
$repairScript = '<?php
// Script de réparation des graphiques

echo "🔧 RÉPARATION DES GRAPHIQUES\\n";
echo "============================\\n\\n";

$viewPath = "resources/views/admin/dashboard.blade.php";
$content = file_get_contents($viewPath);

// Backup
$backupPath = $viewPath . ".repair-backup." . date("Y-m-d-H-i-s");
copy($viewPath, $backupPath);
echo "📋 Backup: $backupPath\\n";

// Supprimer tout le JavaScript et le remplacer par une version ultra-simple
$scriptStart = strpos($content, "@push(\'scripts\')");
$scriptEnd = strpos($content, "@endpush", $scriptStart);

if ($scriptStart !== false && $scriptEnd !== false) {
    $beforeScripts = substr($content, 0, $scriptStart);
    $afterScripts = substr($content, $scriptEnd);
    
    $simpleScript = \'@push("scripts")
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>
<script>
document.addEventListener("DOMContentLoaded", function() {
    console.log("🚀 Initialisation graphiques GRADIS");
    
    if (typeof ApexCharts === "undefined") {
        console.error("❌ ApexCharts non chargé");
        return;
    }
    
    // Données de test
    const testData = [15000, 22000, 18000, 25000, 30000, 28000];
    const testCategories = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"];
    
    // Fonction de création sécurisée
    function createSafeChart(selector, options, name) {
        const container = document.querySelector(selector);
        if (!container) {
            console.warn("⚠️ Conteneur " + selector + " non trouvé");
            return;
        }
        
        try {
            const chart = new ApexCharts(container, options);
            chart.render();
            console.log("✅ Graphique " + name + " créé");
        } catch (error) {
            console.error("❌ Erreur " + name + ":", error);
        }
    }
    
    // Graphique 1 - Revenus
    setTimeout(function() {
        createSafeChart("#revenueChart", {
            series: [{ name: "Revenus", data: testData }],
            chart: { type: "area", height: 300, toolbar: { show: false } },
            xaxis: { categories: testCategories },
            colors: ["#007bff"]
        }, "Revenus");
    }, 100);
    
    // Graphique 2 - Ressources  
    setTimeout(function() {
        createSafeChart("#resourcesChart", {
            series: [{ name: "Ressources", data: testData }],
            chart: { type: "bar", height: 300, toolbar: { show: false } },
            xaxis: { categories: testCategories },
            colors: ["#28a745"]
        }, "Ressources");
    }, 200);
    
    // Graphique 3 - Catégories
    setTimeout(function() {
        createSafeChart("#categoryRevenueChart", {
            series: [45000, 32000, 28000, 23000],
            chart: { type: "donut", height: 300 },
            labels: ["Ciment", "Fer", "Sable", "Gravier"],
            colors: ["#007bff", "#28a745", "#ffc107", "#dc3545"]
        }, "Catégories");
    }, 300);
    
    // Graphique 4 - Ciment
    setTimeout(function() {
        createSafeChart("#cementOrdersChart", {
            series: [{ name: "Tonnage", data: [100, 150, 120, 180, 200, 180] }],
            chart: { type: "bar", height: 300, toolbar: { show: false } },
            xaxis: { categories: testCategories },
            colors: ["#17a2b8"]
        }, "Ciment");
    }, 400);
});
</script>
@endpush\';
    
    $content = $beforeScripts . $simpleScript . $afterScripts;
    
    if (file_put_contents($viewPath, $content)) {
        echo "✅ Script JavaScript ultra-simple installé\\n";
        echo "📊 Les graphiques devraient maintenant fonctionner\\n";
    } else {
        echo "❌ Erreur sauvegarde\\n";
    }
} else {
    echo "❌ Section scripts non trouvée\\n";
}

echo "\\n🎯 RÉPARATION TERMINÉE\\n";
?>';

$repairScriptPath = 'repair_charts_simple.php';
if (file_put_contents($repairScriptPath, $repairScript)) {
    echo "✅ Script de réparation créé: $repairScriptPath\n";
} else {
    echo "❌ Erreur création script de réparation\n";
}

echo "\n🎯 DIAGNOSTIC TERMINÉ\n";
echo "====================\n";
echo "1. 🌐 Testez d'abord: http://127.0.0.1:8000/test-charts.html\n";
echo "2. 🔧 Si ça marche, exécutez: php repair_charts_simple.php\n";
echo "3. 🧹 Puis videz le cache: php artisan view:clear\n";
echo "4. 📊 Testez le dashboard: http://127.0.0.1:8000/admin/dashboard\n";

echo "\n💡 ÉTAPES DE DÉBOGAGE:\n";
echo "======================\n";
echo "1. Ouvrez la console du navigateur (F12)\n";
echo "2. Regardez les erreurs JavaScript\n";
echo "3. Vérifiez si ApexCharts se charge\n";
echo "4. Vérifiez si les conteneurs existent\n";
