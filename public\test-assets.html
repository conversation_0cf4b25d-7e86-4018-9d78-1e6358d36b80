<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="test-token">
    <meta name="app-url" content="http://127.0.0.1:8000">
    
    <!-- Permissions Policy pour éviter les erreurs en développement -->
    <meta http-equiv="Permissions-Policy" content="unload=*, payment=*, geolocation=*">
    
    <title>Test Assets - Gradis</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>
        .payment-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }
        
        .payment-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
        }
        
        .payment-card.selected {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #f8fff9 100%);
        }
        
        .payment-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">🧪 Test des Assets - Page de Paiement Comptable</h1>
        
        <!-- Section de diagnostic -->
        <div class="test-section">
            <h3>📊 Diagnostic des Assets</h3>
            <div id="diagnostic-results">
                <p><span class="status-indicator status-warning"></span>Diagnostic en cours...</p>
            </div>
        </div>
        
        <!-- Section de test des cartes de paiement -->
        <div class="test-section">
            <h3>💳 Test des Cartes de Paiement</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="payment-card" data-method="cash">
                        <div class="text-center">
                            <div class="payment-icon text-success">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <h5>Espèces</h5>
                            <p class="text-muted">Paiement en liquide</p>
                        </div>
                        <input type="radio" name="payment_method" id="method_cash" value="cash" class="d-none">
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="payment-card" data-method="bank_transfer">
                        <div class="text-center">
                            <div class="payment-icon text-primary">
                                <i class="fas fa-university"></i>
                            </div>
                            <h5>Virement Bancaire</h5>
                            <p class="text-muted">Transfert électronique</p>
                        </div>
                        <input type="radio" name="payment_method" id="method_bank_transfer" value="bank_transfer" class="d-none">
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="payment-card" data-method="check">
                        <div class="text-center">
                            <div class="payment-icon text-info">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <h5>Chèque</h5>
                            <p class="text-muted">Paiement par chèque</p>
                        </div>
                        <input type="radio" name="payment_method" id="method_check" value="check" class="d-none">
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <p><strong>Méthode sélectionnée:</strong> <span id="selected-method">Aucune</span></p>
            </div>
        </div>
        
        <!-- Section de test des boutons -->
        <div class="test-section">
            <h3>🔘 Test des Boutons et Interactions</h3>
            <div class="row">
                <div class="col-md-3">
                    <button class="btn btn-primary w-100" onclick="testJQuery()">Test jQuery</button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success w-100" onclick="testSweetAlert()">Test SweetAlert</button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info w-100" onclick="testAjax()">Test AJAX</button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning w-100" onclick="testBootstrap()">Test Bootstrap</button>
                </div>
            </div>
        </div>
        
        <!-- Section des logs -->
        <div class="test-section">
            <h3>📝 Console de Test</h3>
            <div id="test-console" style="background: #000; color: #00ff00; padding: 15px; border-radius: 5px; font-family: monospace; height: 200px; overflow-y: auto;">
                <div>🚀 Console de test initialisée...</div>
            </div>
        </div>
    </div>

    <!-- jQuery avec fallback -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" 
            integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" 
            crossorigin="anonymous"></script>
    <script>
        // Fallback si jQuery ne se charge pas depuis le CDN
        if (typeof jQuery === 'undefined') {
            console.warn('jQuery CDN failed, loading local fallback...');
            document.write('<script src="js/jquery-3.7.1.min.js"><\/script>');
        }
    </script>
    
    <!-- Bootstrap 5 Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    
    <!-- Script de diagnostic -->
    <script src="js/diagnostic.js"></script>
    
    <!-- Scripts de test -->
    <script>
        let testConsole = document.getElementById('test-console');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#00ff00',
                'success': '#00ff00',
                'error': '#ff0000',
                'warning': '#ffff00'
            };
            
            const div = document.createElement('div');
            div.style.color = colors[type] || '#00ff00';
            div.innerHTML = `[${timestamp}] ${message}`;
            testConsole.appendChild(div);
            testConsole.scrollTop = testConsole.scrollHeight;
        }
        
        function testJQuery() {
            log('🔄 Test jQuery...', 'info');
            
            if (typeof $ !== 'undefined') {
                log('✅ jQuery disponible - Version: ' + $.fn.jquery, 'success');
                
                // Test de manipulation DOM
                try {
                    $('body').addClass('jquery-test');
                    $('body').removeClass('jquery-test');
                    log('✅ Manipulation DOM jQuery fonctionne', 'success');
                } catch (e) {
                    log('❌ Erreur manipulation DOM: ' + e.message, 'error');
                }
            } else {
                log('❌ jQuery non disponible', 'error');
            }
        }
        
        function testSweetAlert() {
            log('🔄 Test SweetAlert2...', 'info');
            
            if (typeof Swal !== 'undefined') {
                log('✅ SweetAlert2 disponible', 'success');
                
                Swal.fire({
                    title: '🎉 Test Réussi!',
                    text: 'SweetAlert2 fonctionne correctement',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
                
                log('✅ SweetAlert2 affiché avec succès', 'success');
            } else {
                log('❌ SweetAlert2 non disponible', 'error');
            }
        }
        
        function testAjax() {
            log('🔄 Test AJAX...', 'info');
            
            if (typeof $ !== 'undefined') {
                $.ajax({
                    url: '/test-server',
                    method: 'GET',
                    timeout: 5000,
                    success: function(data) {
                        log('✅ AJAX réussi: ' + data.message, 'success');
                    },
                    error: function(xhr, status, error) {
                        log('❌ AJAX échoué: ' + status + ' - ' + error, 'error');
                    }
                });
            } else {
                log('❌ jQuery non disponible pour AJAX', 'error');
            }
        }
        
        function testBootstrap() {
            log('🔄 Test Bootstrap...', 'info');
            
            if (typeof bootstrap !== 'undefined') {
                log('✅ Bootstrap disponible', 'success');
                
                // Test modal
                try {
                    const modalElement = document.createElement('div');
                    modalElement.className = 'modal';
                    modalElement.innerHTML = `
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-body">Test Bootstrap Modal</div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(modalElement);
                    
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                    
                    setTimeout(() => {
                        modal.hide();
                        document.body.removeChild(modalElement);
                    }, 1500);
                    
                    log('✅ Bootstrap Modal fonctionne', 'success');
                } catch (e) {
                    log('❌ Erreur Bootstrap Modal: ' + e.message, 'error');
                }
            } else {
                log('❌ Bootstrap non disponible', 'error');
            }
        }
        
        // Initialisation au chargement
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Page chargée, initialisation...', 'info');
            
            // Test automatique des assets
            setTimeout(() => {
                testJQuery();
                
                setTimeout(() => {
                    if (typeof Swal !== 'undefined') {
                        log('✅ SweetAlert2 chargé', 'success');
                    } else {
                        log('❌ SweetAlert2 non chargé', 'error');
                    }
                }, 100);
                
                setTimeout(() => {
                    if (typeof bootstrap !== 'undefined') {
                        log('✅ Bootstrap chargé', 'success');
                    } else {
                        log('❌ Bootstrap non chargé', 'error');
                    }
                }, 200);
            }, 500);
            
            // Configuration des cartes de paiement
            if (typeof $ !== 'undefined') {
                $('.payment-card').on('click', function() {
                    log('🔄 Carte de paiement cliquée', 'info');
                    
                    // Retirer la sélection des autres cartes
                    $('.payment-card').removeClass('selected');
                    $('input[name="payment_method"]').prop('checked', false);
                    
                    // Sélectionner cette carte
                    $(this).addClass('selected');
                    const method = $(this).data('method');
                    $('#method_' + method).prop('checked', true);
                    $('#selected-method').text(method);
                    
                    log('✅ Méthode sélectionnée: ' + method, 'success');
                });
                
                log('✅ Événements jQuery configurés', 'success');
            } else {
                log('❌ Impossible de configurer les événements jQuery', 'error');
            }
        });
    </script>
</body>
</html>
