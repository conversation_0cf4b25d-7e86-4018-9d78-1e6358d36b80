<?php

namespace App\Http\Controllers\IronManager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;

class OrderController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:iron_manager']);
    }

    public function index()
    {
        $orders = Order::with(['customer', 'products'])
            ->where('type', 'iron') // Filtrer les commandes de fer
            ->latest()
            ->paginate(15);

        return view('iron-manager.orders.index', compact('orders'));
    }

    public function show(Order $order)
    {
        $order->load(['customer', 'products', 'payments']);
        
        return view('iron-manager.orders.show', compact('order'));
    }

    public function create()
    {
        $customers = User::role('customer')->get();
        $products = Product::where('category', 'iron')
            ->where('is_active', true)
            ->get();
        
        return view('iron-manager.orders.create', compact('customers', 'products'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:users,id',
            'products' => 'required|array',
            'products.*.id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|numeric|min:1',
            'delivery_date' => 'required|date|after:today',
            'delivery_address' => 'required|string|max:500',
            'notes' => 'nullable|string|max:1000',
        ]);

        $order = Order::create([
            'customer_id' => $validated['customer_id'],
            'type' => 'iron',
            'status' => 'pending',
            'delivery_date' => $validated['delivery_date'],
            'delivery_address' => $validated['delivery_address'],
            'notes' => $validated['notes'] ?? null,
            'created_by' => auth()->id(),
        ]);

        // Attacher les produits à la commande
        foreach ($validated['products'] as $productData) {
            $product = Product::find($productData['id']);
            $order->products()->attach($product->id, [
                'quantity' => $productData['quantity'],
                'unit_price' => $product->price,
                'total_price' => $product->price * $productData['quantity'],
            ]);
        }

        return redirect()->route('iron-manager.orders.show', $order)
            ->with('success', 'Commande créée avec succès.');
    }

    public function update(Request $request, Order $order)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled',
            'notes' => 'nullable|string|max:1000',
        ]);

        $order->update($validated);

        return redirect()->route('iron-manager.orders.show', $order)
            ->with('success', 'Commande mise à jour avec succès.');
    }
}
