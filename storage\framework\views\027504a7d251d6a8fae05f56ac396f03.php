<?php $__env->startSection('title', 'Liste des approvisionnements'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <!-- Header Section avec gradient et icônes -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="header-card">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <div class="header-icon">
                            <i class="fas fa-truck-loading"></i>
                        </div>
                        <div class="ms-3">
                            <h1 class="header-title mb-1">Gestion des Approvisionnements</h1>
                            <p class="header-subtitle mb-0">Supervision et validation des demandes d'approvisionnement</p>
                        </div>
                    </div>
                    <div class="header-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo e($supplies->total()); ?></span>
                            <span class="stat-label">Total</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card pending">
                <div class="stats-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-content">
                    <h3><?php echo e($supplies->where('status', 'pending')->count()); ?></h3>
                    <p>En attente</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card validated">
                <div class="stats-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-content">
                    <h3><?php echo e($supplies->where('status', 'validated')->count()); ?></h3>
                    <p>Validés</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card rejected">
                <div class="stats-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stats-content">
                    <h3><?php echo e($supplies->where('status', 'rejected')->count()); ?></h3>
                    <p>Rejetés</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card total">
                <div class="stats-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stats-content">
                    <h3><?php echo e(number_format($supplies->sum('real_total_amount'), 0, ',', ' ')); ?></h3>
                    <p>FCFA Total</p>
                </div>
            </div>
        </div>
    </div>

    <?php if(isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show modern-alert" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo e($error); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if($supplies->isEmpty()): ?>
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-inbox"></i>
            </div>
            <h3>Aucun approvisionnement</h3>
            <p>Il n'y a actuellement aucun approvisionnement à afficher.</p>
        </div>
    <?php else: ?>
        <div class="modern-card">
            <div class="card-header-modern">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Liste des approvisionnements
                </h5>
                <div class="header-actions">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshTable()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Actualiser
                    </button>
                </div>
            </div>
            <div class="card-body-modern p-0">
                <div class="table-responsive">
                    <table class="table modern-table mb-0">
                        <thead>
                            <tr>
                                <th class="ps-4">
                                    <i class="fas fa-hashtag me-2 text-muted"></i>
                                    Référence
                                </th>
                                <th>
                                    <i class="fas fa-building me-2 text-muted"></i>
                                    Fournisseur
                                </th>
                                <th>
                                    <i class="fas fa-user me-2 text-muted"></i>
                                    Créé par
                                </th>
                                <th>
                                    <i class="fas fa-money-bill-wave me-2 text-muted"></i>
                                    Montant
                                </th>
                                <th>
                                    <i class="fas fa-flag me-2 text-muted"></i>
                                    Statut
                                </th>
                                <th>
                                    <i class="fas fa-calendar me-2 text-muted"></i>
                                    Date
                                </th>
                                <th class="text-end pe-4">
                                    <i class="fas fa-cogs me-2 text-muted"></i>
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $supplies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="table-row-hover">
                                <td class="ps-4">
                                    <div class="reference-cell">
                                        <span class="reference-code"><?php echo e($supply->reference); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="supplier-cell">
                                        <div class="supplier-avatar">
                                            <?php echo e(substr($supply->supplier->name, 0, 2)); ?>

                                        </div>
                                        <span class="supplier-name"><?php echo e($supply->supplier->name); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="creator-cell">
                                        <span class="creator-name"><?php echo e($supply->createdBy->name ?? 'N/A'); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="amount-cell">
                                        <span class="amount-value"><?php echo e(number_format($supply->real_total_amount, 0, ',', ' ')); ?></span>
                                        <span class="amount-currency">FCFA</span>
                                    </div>
                                </td>
                                <td>
                                    <?php if($supply->status === 'pending'): ?>
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-clock me-1"></i>
                                            En attente
                                        </span>
                                    <?php elseif($supply->status === 'validated'): ?>
                                        <span class="status-badge status-validated">
                                            <i class="fas fa-check me-1"></i>
                                            Validé
                                        </span>
                                    <?php elseif($supply->status === 'rejected'): ?>
                                        <span class="status-badge status-rejected">
                                            <i class="fas fa-times me-1"></i>
                                            Rejeté
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="date-cell">
                                        <span class="date-value"><?php echo e($supply->created_at->format('d/m/Y')); ?></span>
                                        <span class="time-value"><?php echo e($supply->created_at->format('H:i')); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons pe-4">
                                        <a href="<?php echo e(route('admin.supplies.show', $supply)); ?>"
                                           class="action-btn view-btn"
                                           title="Voir les détails"
                                           data-bs-toggle="tooltip">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if($supply->status === 'pending' && $supply->created_by !== auth()->id()): ?>
                                            <button type="button"
                                                    class="action-btn validate-btn"
                                                    onclick="confirmValidation(<?php echo e($supply->id); ?>)"
                                                    title="Valider l'approvisionnement"
                                                    data-bs-toggle="tooltip">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button"
                                                    class="action-btn reject-btn"
                                                    onclick="showRejectModal(<?php echo e($supply->id); ?>)"
                                                    title="Rejeter l'approvisionnement"
                                                    data-bs-toggle="tooltip">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <div class="pagination-wrapper">
                    <?php echo e($supplies->links()); ?>

                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Modal de rejet moderne -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <form id="rejectForm" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-header modern-modal-header">
                    <div class="modal-icon reject-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="modal-title-wrapper">
                        <h5 class="modal-title" id="rejectModalLabel">Rejeter l'approvisionnement</h5>
                        <p class="modal-subtitle">Cette action nécessite une justification</p>
                    </div>
                    <button type="button" class="btn-close modern-btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body modern-modal-body">
                    <div class="form-group-modern">
                        <label for="rejection_reason" class="form-label-modern">
                            <i class="fas fa-comment-alt me-2"></i>
                            Raison du rejet
                        </label>
                        <textarea class="form-control-modern"
                                  id="rejection_reason"
                                  name="rejection_reason"
                                  rows="4"
                                  placeholder="Veuillez expliquer les raisons du rejet de cet approvisionnement..."
                                  required></textarea>
                        <div class="form-help-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Cette raison sera communiquée au demandeur
                        </div>
                    </div>
                </div>
                <div class="modal-footer modern-modal-footer">
                    <button type="button" class="btn btn-secondary-modern" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-danger-modern">
                        <i class="fas fa-ban me-2"></i>
                        Rejeter l'approvisionnement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Styles pour les modales modernes -->
<style>
.modern-modal {
    border: none;
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    overflow: hidden;
}

.modern-modal-header {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border: none;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modal-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.reject-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.modal-title-wrapper {
    flex: 1;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #7f1d1d;
    margin: 0;
}

.modal-subtitle {
    font-size: 0.9rem;
    color: #991b1b;
    margin: 0.25rem 0 0 0;
    opacity: 0.8;
}

.modern-btn-close {
    background: rgba(127, 29, 29, 0.1);
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.modern-modal-body {
    padding: 2rem;
    background: white;
}

.form-group-modern {
    margin-bottom: 1.5rem;
}

.form-label-modern {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.form-control-modern {
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    resize: vertical;
    min-height: 100px;
}

.form-control-modern:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-help-text {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
}

.modern-modal-footer {
    background: #f9fafb;
    border: none;
    padding: 1.5rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-secondary-modern {
    background: #6b7280;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.btn-secondary-modern:hover {
    background: #4b5563;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(107, 114, 128, 0.3);
}

.btn-danger-modern {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.btn-danger-modern:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

/* Styles pour SweetAlert moderne */
.modern-swal-popup {
    border-radius: 16px !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25) !important;
    padding: 0 !important;
    overflow: hidden !important;
}

.modern-swal-title {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: #1f2937 !important;
    margin-bottom: 1rem !important;
}

.modern-swal-content {
    padding: 0 1rem !important;
    color: #4b5563 !important;
}

.modern-swal-input {
    border: 2px solid #e5e7eb !important;
    border-radius: 10px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    margin: 1rem 0 !important;
}

.modern-swal-input:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

.modern-swal-confirm {
    background: linear-gradient(135deg, #059669, #047857) !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.modern-swal-confirm:hover {
    background: linear-gradient(135deg, #047857, #065f46) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(5, 150, 105, 0.3) !important;
}

.modern-swal-cancel {
    background: #6b7280 !important;
    border: none !important;
    border-radius: 10px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.modern-swal-cancel:hover {
    background: #4b5563 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(107, 114, 128, 0.3) !important;
}

.success-popup {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7) !important;
}

.error-popup {
    background: linear-gradient(135deg, #fef2f2, #fee2e2) !important;
}

/* Animation pour les éléments qui apparaissent */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.header-card,
.stats-card,
.modern-card {
    animation: slideInFromTop 0.6s ease-out;
}

/* Effet de pulsation pour les boutons d'action */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

.action-btn:focus {
    animation: pulse 1.5s infinite;
}
</style>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> <?php echo e(date('Y')); ?> GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Variables CSS pour la cohérence des couleurs */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --info-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Header Card */
.header-card {
    background: var(--primary-gradient);
    border-radius: var(--border-radius);
    padding: 2rem;
    color: white;
    box-shadow: var(--card-shadow);
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.header-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.header-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
}

.header-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.header-subtitle {
    opacity: 0.9;
    font-size: 1rem;
}

.header-stats {
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Stats Cards */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: none;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stats-card.pending::before {
    background: var(--warning-gradient);
}

.stats-card.validated::before {
    background: var(--success-gradient);
}

.stats-card.rejected::before {
    background: var(--danger-gradient);
}

.stats-card.total::before {
    background: var(--info-gradient);
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: white;
}

.stats-card.pending .stats-icon {
    background: var(--warning-gradient);
}

.stats-card.validated .stats-icon {
    background: var(--success-gradient);
}

.stats-card.rejected .stats-icon {
    background: var(--danger-gradient);
}

.stats-card.total .stats-icon {
    background: var(--info-gradient);
}

.stats-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    color: #2d3748;
}

.stats-content p {
    margin: 0;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Modern Card */
.modern-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    border: none;
}

.card-header-modern {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-header-modern h5 {
    color: #2d3748;
    font-weight: 600;
    margin: 0;
}

.header-actions .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: var(--transition);
}

/* Modern Table */
.modern-table {
    margin: 0;
}

.modern-table thead th {
    background: #f7fafc;
    border: none;
    padding: 1.2rem 1rem;
    font-weight: 600;
    color: #4a5568;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #e2e8f0;
}

.modern-table tbody td {
    padding: 1.2rem 1rem;
    border: none;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.table-row-hover {
    transition: var(--transition);
}

.table-row-hover:hover {
    background: #f8fafc;
    transform: scale(1.01);
}

/* Cell Styles */
.reference-cell .reference-code {
    font-family: 'Monaco', 'Menlo', monospace;
    background: #e2e8f0;
    padding: 0.3rem 0.6rem;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    color: #4a5568;
}

.supplier-cell {
    display: flex;
    align-items: center;
}

.supplier-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
    margin-right: 0.8rem;
    text-transform: uppercase;
}

.supplier-name {
    font-weight: 500;
    color: #2d3748;
}

.creator-name {
    color: #4a5568;
    font-weight: 500;
}

.amount-cell {
    display: flex;
    flex-direction: column;
}

.amount-value {
    font-weight: 700;
    color: #2d3748;
    font-size: 1rem;
}

.amount-currency {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.status-pending {
    background: linear-gradient(135deg, #fef3c7, #fbbf24);
    color: #92400e;
}

.status-validated {
    background: linear-gradient(135deg, #d1fae5, #10b981);
    color: #065f46;
}

.status-rejected {
    background: linear-gradient(135deg, #fee2e2, #ef4444);
    color: #991b1b;
}

.date-cell {
    display: flex;
    flex-direction: column;
}

.date-value {
    font-weight: 600;
    color: #2d3748;
}

.time-value {
    font-size: 0.8rem;
    color: #718096;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.action-btn {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    cursor: pointer;
    font-size: 0.9rem;
}

.view-btn {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.view-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(116, 185, 255, 0.4);
}

.validate-btn {
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
}

.validate-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 184, 148, 0.4);
}

.reject-btn {
    background: linear-gradient(135deg, #e17055, #d63031);
    color: white;
}

.reject-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(225, 112, 85, 0.4);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: #718096;
}

.empty-state h3 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #718096;
    margin: 0;
}

/* Pagination */
.pagination-wrapper {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: center;
}

/* Modern Alert */
.modern-alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border-left: 4px solid #e74c3c;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-card {
        padding: 1.5rem;
    }

    .header-title {
        font-size: 1.4rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .card-header-modern {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .modern-table thead th {
        padding: 0.8rem 0.5rem;
        font-size: 0.75rem;
    }

    .modern-table tbody td {
        padding: 0.8rem 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.3rem;
    }

    .action-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}

/* Animation pour le chargement */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card,
.modern-card {
    animation: fadeInUp 0.6s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Fonction pour actualiser le tableau
    window.refreshTable = function() {
        const refreshBtn = document.querySelector('[onclick="refreshTable()"]');
        const icon = refreshBtn.querySelector('i');

        // Animation de rotation
        icon.classList.add('fa-spin');
        refreshBtn.disabled = true;

        // OPTIMISATION: Rechargement immédiat
        window.location.reload();
    };

    // Animation d'apparition des lignes du tableau
    const tableRows = document.querySelectorAll('.table-row-hover');
    tableRows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';

        // OPTIMISATION: Animation CSS immédiate
        row.style.transition = 'all 0.3s ease';
        row.style.opacity = '1';
        row.style.transform = 'translateY(0)';
    });
    window.confirmValidation = function(id) {
        Swal.fire({
            title: '✅ Validation d\'approvisionnement',
            html: `
                <div style="text-align: left; margin: 1rem 0;">
                    <p style="color: #6b7280; margin-bottom: 1rem;">Pour confirmer la validation de cet approvisionnement, veuillez saisir <strong style="color: #059669;">VALIDER</strong> dans le champ ci-dessous :</p>
                </div>
            `,
            input: 'text',
            inputPlaceholder: 'Tapez VALIDER pour confirmer',
            icon: 'question',
            iconColor: '#059669',
            showCancelButton: true,
            confirmButtonColor: '#059669',
            cancelButtonColor: '#6b7280',
            confirmButtonText: '<i class="fas fa-check me-2"></i>Valider',
            cancelButtonText: '<i class="fas fa-times me-2"></i>Annuler',
            buttonsStyling: true,
            customClass: {
                popup: 'modern-swal-popup',
                title: 'modern-swal-title',
                htmlContainer: 'modern-swal-content',
                input: 'modern-swal-input',
                confirmButton: 'modern-swal-confirm',
                cancelButton: 'modern-swal-cancel'
            },
            inputValidator: (value) => {
                if (!value) {
                    return '<i class="fas fa-exclamation-triangle"></i> Vous devez saisir quelque chose!';
                }
                if (value.toUpperCase() !== 'VALIDER') {
                    return '<i class="fas fa-times-circle"></i> Veuillez saisir exactement "VALIDER"';
                }
            },
            preConfirm: () => {
                return new Promise((resolve) => {
                    Swal.showLoading();
                    setTimeout(() => {
                        resolve();
                    }, 1000);
                });
            }
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/admin/supplies/${id}/validateSupply`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            title: '🎉 Validation réussie !',
                            html: `
                                <div style="text-align: center; margin: 1rem 0;">
                                    <div style="background: linear-gradient(135deg, #d1fae5, #a7f3d0); padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                                        <i class="fas fa-check-circle" style="font-size: 2rem; color: #059669; margin-bottom: 0.5rem;"></i>
                                        <p style="color: #065f46; margin: 0; font-weight: 600;">${data.message}</p>
                                    </div>
                                </div>
                            `,
                            icon: 'success',
                            iconColor: '#059669',
                            confirmButtonColor: '#059669',
                            confirmButtonText: '<i class="fas fa-check me-2"></i>Parfait !',
                            customClass: {
                                popup: 'modern-swal-popup success-popup',
                                confirmButton: 'modern-swal-confirm'
                            },
                            timer: 3000,
                            timerProgressBar: true
                        }).then(() => {
                            window.location.reload();
                        });
                    } else {
                        throw new Error(data.message || 'Une erreur est survenue');
                    }
                })
                .catch(error => {
                    Swal.fire({
                        title: '❌ Erreur de validation',
                        html: `
                            <div style="text-align: center; margin: 1rem 0;">
                                <div style="background: linear-gradient(135deg, #fee2e2, #fecaca); padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #dc2626; margin-bottom: 0.5rem;"></i>
                                    <p style="color: #7f1d1d; margin: 0; font-weight: 600;">${error.message || "Une erreur s'est produite lors de la validation."}</p>
                                </div>
                            </div>
                        `,
                        icon: 'error',
                        iconColor: '#dc2626',
                        confirmButtonColor: '#dc2626',
                        confirmButtonText: '<i class="fas fa-times me-2"></i>Compris',
                        customClass: {
                            popup: 'modern-swal-popup error-popup',
                            confirmButton: 'modern-swal-confirm'
                        }
                    });
                });
            }
        });
    };

    window.showRejectModal = function(id) {
        const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
        const form = document.getElementById('rejectForm');
        
        // Remove any existing event listeners
        const newForm = form.cloneNode(true);
        form.parentNode.replaceChild(newForm, form);
        
        newForm.action = `<?php echo e(route('admin.supplies.rejectSupply', ['supply' => ':id'])); ?>`.replace(':id', id);
        
        newForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            fetch(this.action, {
                method: 'POST',
                body: new FormData(this),
                headers: {
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => Promise.reject(err));
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Hide modal
                    modal.hide();
                    // Show success message
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success alert-dismissible fade show';
                    alert.innerHTML = `
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.querySelector('.container-fluid').insertBefore(alert, document.querySelector('.container-fluid').firstChild);
                    // Reload after a short delay
                    setTimeout(() => window.location.reload(), 1000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const message = error.message || 'Une erreur est survenue lors du rejet de l\'approvisionnement.';
                const alert = document.createElement('div');
                alert.className = 'alert alert-danger alert-dismissible fade show';
                alert.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                document.querySelector('.modal-body').insertBefore(alert, document.querySelector('.modal-body').firstChild);
            });
        });
        
        modal.show();
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/supplies/index.blade.php ENDPATH**/ ?>