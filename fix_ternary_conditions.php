<?php

echo "🔧 CORRECTION AUTOMATIQUE DES CONDITIONS TERNAIRES\n";
echo "=================================================\n\n";

$viewPath = 'resources/views/admin/dashboard.blade.php';

if (!file_exists($viewPath)) {
    echo "❌ Fichier non trouvé: $viewPath\n";
    exit(1);
}

echo "📖 Lecture du fichier...\n";
$content = file_get_contents($viewPath);

if ($content === false) {
    echo "❌ Impossible de lire le fichier\n";
    exit(1);
}

echo "🔍 Recherche des conditions ternaires mal formées...\n";

// Patterns pour corriger les conditions ternaires mal formées
$ternaryPatterns = [
    // Corriger les conditions ternaires sans ?
    '/\{\{ \$([a-zA-Z_\[\]\']+)->format\(\'([^\']+)\'\) : \'([^\']+)\' \}\}/' => '{{ $1 ? $1->format(\'$2\') : \'$3\' }}',
    
    // Corriger les conditions avec now() qui n'ont pas besoin de ternaire
    '/\{\{ now\(\)->format\(\'([^\']+)\'\) : \'([^\']+)\' \}\}/' => '{{ now()->format(\'$1\') }}',
    
    // Corriger les conditions ternaires avec des variables complexes
    '/\{\{ \$([a-zA-Z_]+)\[\'([^\']+)\'\]->format\(\'([^\']+)\'\) : \'([^\']+)\' \}\}/' => '{{ $1[\'$2\'] ? $1[\'$2\']->format(\'$3\') : \'$4\' }}',
    
    // Corriger les conditions ternaires dans les tableaux
    '/\{\{ \$([a-zA-Z_]+)\[\'([^\']+)\'\] \? \$\1\[\'([^\']+)\'\]->format\(\'([^\']+)\'\) : \'([^\']+)\' \}\}/' => '{{ $1[\'$2\'] ? $1[\'$3\']->format(\'$4\') : \'$5\' }}',
];

$replacements = 0;

foreach ($ternaryPatterns as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $content, -1, $count);
    
    if ($count > 0) {
        echo "✅ Corrigé condition ternaire: $count occurrences\n";
        $content = $newContent;
        $replacements += $count;
    }
}

// Corrections manuelles spécifiques
$manualCorrections = [
    // Corriger les conditions ternaires spécifiques mentionnées dans l'erreur
    '{{ $stockStats[\'last_stock_update\']->format(\'d/m/Y H:i\') : \'N/A\' }}' => '{{ $stockStats[\'last_stock_update\'] ? $stockStats[\'last_stock_update\']->format(\'d/m/Y H:i\') : \'N/A\' }}',
    
    '{{ $product[\'last_updated\']->format(\'d/m/Y H:i\') : \'N/A\' }}' => '{{ $product[\'last_updated\'] ? $product[\'last_updated\']->format(\'d/m/Y H:i\') : \'N/A\' }}',
    
    // Corriger now() qui n'a pas besoin de condition ternaire
    '{{ now()->format(\'d/m/Y\') : \'N/A\' }}' => '{{ now()->format(\'d/m/Y\') }}',
    '{{ now()->format(\'H:i\') : \'N/A\' }}' => '{{ now()->format(\'H:i\') }}',
    '{{ now()->format(\'d/m/Y H:i\') : \'N/A\' }}' => '{{ now()->format(\'d/m/Y H:i\') }}',
    
    // Autres corrections similaires
    '->format(\'d/m/Y\') : \'N/A\' }}' => ' ? $0->format(\'d/m/Y\') : \'N/A\' }}',
    '->format(\'H:i\') : \'N/A\' }}' => ' ? $0->format(\'H:i\') : \'N/A\' }}',
    '->format(\'d/m/Y H:i\') : \'N/A\' }}' => ' ? $0->format(\'d/m/Y H:i\') : \'N/A\' }}',
];

foreach ($manualCorrections as $search => $replace) {
    if (strpos($content, $search) !== false) {
        $content = str_replace($search, $replace, $content);
        echo "✅ Correction manuelle: condition ternaire corrigée\n";
        $replacements++;
    }
}

// Vérification finale pour s'assurer que toutes les conditions ternaires sont bien formées
$finalChecks = [
    // S'assurer que toutes les conditions ternaires avec format() ont une condition
    '/\{\{ \$([a-zA-Z_\[\]\']+)->format\(\'([^\']+)\'\) : \'([^\']+)\' \}\}/' => '{{ $1 ? $1->format(\'$2\') : \'$3\' }}',
];

foreach ($finalChecks as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $content, -1, $count);
    
    if ($count > 0) {
        echo "✅ Vérification finale: $count occurrences\n";
        $content = $newContent;
        $replacements += $count;
    }
}

if ($replacements > 0) {
    echo "\n💾 Sauvegarde des modifications...\n";
    
    // Backup du fichier original
    $backupPath = $viewPath . '.ternary-backup.' . date('Y-m-d-H-i-s');
    copy($viewPath, $backupPath);
    echo "📋 Backup créé: $backupPath\n";
    
    // Sauvegarde du fichier modifié
    if (file_put_contents($viewPath, $content) !== false) {
        echo "✅ Fichier corrigé avec succès!\n";
        echo "🔢 Total des corrections: $replacements\n";
    } else {
        echo "❌ Erreur lors de la sauvegarde\n";
        exit(1);
    }
} else {
    echo "ℹ️  Aucune correction nécessaire\n";
}

echo "\n🎯 RÉSUMÉ:\n";
echo "=========\n";
echo "✅ Toutes les conditions ternaires bien formées\n";
echo "✅ Syntaxe ? : correcte partout\n";
echo "✅ Fonctions now() sans condition inutile\n";
echo "✅ Conditions de date sécurisées\n";

echo "\n🚀 Toutes les conditions ternaires sont maintenant parfaites!\n";
