<?php

namespace App\Http\Controllers\CementManager;

use App\Http\Controllers\Controller;
use App\Models\Supply;
use App\Models\Product;
use App\Models\City;
use App\Models\Sale;
use App\Services\SaleService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\SalesExport;

class SaleController extends Controller
{
    protected $saleService;

    public function __construct(SaleService $saleService)
    {
        $this->saleService = $saleService;
    }

    public function index()
    {
        // Récupérer les ventes normales
        $sales = Sale::with(['supply', 'city', 'createdBy'])
            ->where(function($query) {
                $query->where('admin_validation_status', '!=', 'rejected')
                      ->orWhereNull('admin_validation_status');
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Récupérer les ventes rejetées par l'administrateur
        $rejectedSales = Sale::with(['supply', 'city', 'createdBy', 'product'])
            ->where('admin_validation_status', 'rejected')
            ->whereNotNull('admin_note')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('cement-manager.sales.index', compact('sales', 'rejectedSales'));
    }

    public function create()
    {
        $supplies = Supply::with([
            'details.product.category',
            'details.product.prices',
            'cities.city',
            'cities.vehicle.capacity',
            'cities.driver',
            'details.product'
        ])
        ->whereHas('details.product.category', function($query) {
            $query->where('name', 'Ciment');
        })
        ->where('status', 'validated')
        ->has('details')
        ->whereHas('cities', function($query) {
            $query->where(function($q) {
                $q->where(DB::raw('COALESCE(remaining_quantity, quantity)'), '>', 0);
            });
        })
        ->get();

        // Organiser les détails par ville
        $supplies->transform(function($supply) {
            $newDetails = collect();
            
            // Pour chaque ville, créer un détail séparé
            foreach ($supply->cities as $supplyCity) {
                // Vérifier s'il reste du stock pour cette ville
                // Si remaining_quantity est null, on utilise la quantité initiale
                $remainingQuantity = $supplyCity->remaining_quantity ?? $supplyCity->quantity;
                if ($remainingQuantity <= 0) {
                    continue;
                }

                // Créer un nouveau détail pour cette ville
                $detail = $supply->details->first()->replicate();
                
                // Récupérer le prix de vente du produit pour cette ville
                $sellingPrice = $detail->product->prices
                    ->where('city_id', $supplyCity->city->id)
                    ->first()
                    ->price ?? 0;
                
                // Calculer le montant total pour ce produit dans cette ville
                $totalAmount = $sellingPrice * $supplyCity->quantity;
                
                // Calculer le nombre de voyages
                $vehicleCapacity = $supplyCity->vehicle->capacity->capacity ?? $supplyCity->vehicle->capacity ?? 0;
                $totalTrips = $vehicleCapacity > 0 ? ceil($supplyCity->quantity / $vehicleCapacity) : 0;
                $remainingTrips = $vehicleCapacity > 0 ? ceil($remainingQuantity / $vehicleCapacity) : 0;
                
                // Créer les détails de la ville
                $detail->cities_details = collect([[
                    'city' => $supplyCity->city,
                    'vehicle' => $supplyCity->vehicle,
                    'driver' => $supplyCity->driver,
                    'quantity' => $supplyCity->quantity,
                    'price' => $sellingPrice,
                    'total_amount' => $totalAmount,
                    'remaining_quantity' => $remainingQuantity,
                    'vehicle_capacity' => $vehicleCapacity,
                    'total_trips' => $totalTrips,
                    'remaining_trips' => $remainingTrips,
                    'reference' => $supply->reference,
                    'created_at' => $supply->created_at
                ]]);

                $newDetails->push($detail);
            }
            
            // Remplacer les détails originaux par les nouveaux
            $supply->details = $newDetails;
            
            return $supply;
        });

        // Filtrer les approvisionnements qui n'ont plus de détails après transformation
        $supplies = $supplies->filter(function($supply) {
            return $supply->details->isNotEmpty();
        });

        $cities = City::orderBy('name')->get();

        return view('cement-manager.sales.create', compact('supplies', 'cities'));
    }

    public function store(Request $request)
    {
        try {
            DB::beginTransaction();

            // Valider les données
            $validated = $request->validate([
                'supply_id' => 'required|exists:supplies,id',
                'quantity' => 'required|numeric|min:0.01',
                'customer_name' => 'required|string|max:255',
                'customer_phone' => 'required|string|max:20',
                'customer_address' => 'required|string|max:255',
                'discount_per_ton' => 'nullable|numeric|min:0',
                'nouveau_prix' => 'nullable|numeric|min:0',
                'vehicle_id' => 'required|exists:trucks,id',  // Ajout du véhicule_id
                'driver_id' => 'required|exists:drivers,id',  // Ajout du driver_id
            ]);

            // Récupérer l'approvisionnement avec toutes les relations nécessaires
            $supply = Supply::with([
                'cities',
                'details.product.prices',
                'details.product'  
            ])->findOrFail($validated['supply_id']);

            // Récupérer les détails de la ville en fonction du véhicule sélectionné
            $cityDetails = $supply->cities()
                ->where(function($query) {
                    $query->where('remaining_quantity', '>', 0)
                          ->orWhereNull('remaining_quantity');
                })
                ->where('vehicle_id', $validated['vehicle_id']) // Filtrer par véhicule sélectionné
                ->first();

            if (!$cityDetails) {
                throw new \Exception('Stock non disponible pour ce véhicule. Veuillez sélectionner un autre véhicule.');
            }

            // Initialiser remaining_quantity si null
            if ($cityDetails->remaining_quantity === null) {
                $cityDetails->remaining_quantity = $cityDetails->quantity;
            }

            // Vérifier la quantité disponible
            if ($validated['quantity'] > $cityDetails->remaining_quantity) {
                throw new \Exception(
                    sprintf(
                        'La quantité demandée (%s tonnes) n\'est pas disponible pour ce véhicule. Stock restant : %s tonnes',
                        number_format($validated['quantity'], 2),
                        number_format($cityDetails->remaining_quantity, 2)
                    )
                );
            }

            // Récupérer le prix unitaire et le produit
            $supplyDetail = $supply->details->first();
            $product = $supplyDetail->product;
            $productPrice = $product->prices
                ->where('city_id', $cityDetails->city_id)
                ->first();

            if (!$productPrice) {
                throw new \Exception('Prix non défini pour cette ville');
            }

            // Récupération des données de base
            $quantity = $validated['quantity'];
            $originalPrice = $productPrice->price;
            $unitPrice = $originalPrice; // Prix unitaire par défaut
            
            // Initialisation des variables
            $discountPerTon = 0;
            $discountTotal = 0;
            $isPriceModified = false;
            $priceIncrease = 0;
            
            // Vérifier si une augmentation de prix est appliquée
            if (isset($validated['nouveau_prix']) && $validated['nouveau_prix'] > 0) {
                // C'est une augmentation de prix
                $priceIncrease = $validated['nouveau_prix'];
                $unitPrice = $originalPrice + $priceIncrease; // Augmenter le prix
                $isPriceModified = true;
                
                // S'assurer qu'aucune remise n'est appliquée en même temps
                $discountPerTon = 0;
            } 
            // Vérifier si une remise est appliquée
            elseif (isset($validated['discount_per_ton']) && $validated['discount_per_ton'] > 0) {
                // C'est une remise
                $discountPerTon = $validated['discount_per_ton'];
                // Le prix unitaire reste le prix original
                $isPriceModified = false;
            }
            
            // Calcul des montants
            $totalBeforeDiscount = $unitPrice * $quantity;
            $discountTotal = $discountPerTon * $quantity;
            $totalAmount = $totalBeforeDiscount - $discountTotal;

            // Créer la vente avec les informations du véhicule et du chauffeur
            $sale = Sale::create([
                'supply_id' => $validated['supply_id'],
                'product_id' => $product->id,               // Ajouter le product_id
                'customer_name' => $validated['customer_name'],
                'customer_phone' => $validated['customer_phone'],
                'customer_address' => $validated['customer_address'],
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_before_discount' => $totalBeforeDiscount,
                'discount_per_ton' => $discountPerTon,
                'discount_total' => $discountTotal,
                'total_amount' => $totalAmount,
                'city_id' => $cityDetails->city_id,
                'status' => 'pending',
                'created_by' => auth()->id(),
                'admin_validation_status' => (($discountPerTon > 0 || $isPriceModified) ? 'pending' : 'not_required'),
                'vehicle_id' => $validated['vehicle_id'],  // Enregistrer le véhicule utilisé
                'driver_id' => $validated['driver_id'],    // Enregistrer le chauffeur assigné
                'price_modified' => $isPriceModified,      // Indiquer si le prix a été modifié
                'original_price' => $isPriceModified ? $originalPrice : null // Stocker le prix original si modifié
            ]);

            // Mettre à jour la quantité restante de la ville pour ce véhicule spécifique
            $cityDetails->remaining_quantity -= $quantity;
            $cityDetails->save();

            // Mettre à jour le stock général du produit
            $product->stock_quantity -= $quantity;
            $product->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => sprintf(
                    'Vente N°%s enregistrée avec succès ! Quantité : %s tonnes, Montant total : %s FCFA',
                    $sale->id,
                    number_format($quantity, 2),
                    number_format($sale->total_amount, 0, ',', ' ')
                ),
                'data' => $sale
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    public function getSupplyDetails(Request $request)
    {
        $request->validate([
            'supply_id' => 'required|exists:supplies,id',
            'city_id' => 'required|exists:cities,id'
        ]);

        try {
            $supply = Supply::with(['details.product', 'cities.city', 'cities.vehicle.capacity', 'cities.driver'])
                ->findOrFail($request->supply_id);

            $cityDetails = $supply->cities->where('city_id', $request->city_id)->first();

            if (!$cityDetails) {
                throw new \Exception('Détails de la ville non trouvés');
            }

            $data = [
                'city' => [
                    'name' => $cityDetails->city->name,
                    'remaining_quantity' => $cityDetails->remaining_quantity ?? $cityDetails->quantity,
                ],
                'vehicle' => [
                    'registration' => $cityDetails->vehicle->registration_number ?? 'Non assigné',
                    'capacity' => $cityDetails->vehicle->capacity->capacity ?? 0
                ],
                'driver' => [
                    'name' => $cityDetails->driver ? $cityDetails->driver->first_name . ' ' . $cityDetails->driver->last_name : 'Non assigné',
                    'phone' => $cityDetails->driver->phone ?? ''
                ],
                'product' => [
                    'name' => $supply->details->first()->product->name ?? 'N/A',
                    'unit' => $supply->details->first()->product->unit ?? 'N/A'
                ],
                'price' => $cityDetails->price ?? 0
            ];

            return response()->json([
                'success' => true,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    public function show(Sale $sale)
    {
        $sale->load(['supply.details.product', 'city', 'createdBy', 'driver']);
        
        // Déterminer le type de vente de manière explicite
        $sale->sale_type = 'standard'; // Par défaut: vente au prix standard
        
        // Pour les anciennes ventes avec remise mais sans original_price, calculer original_price
        if (isset($sale->discount_per_ton) && (float)$sale->discount_per_ton > 0) {
            $sale->sale_type = 'discount'; // Vente avec remise
            
            // Si c'est la vente #17 qui a un prix spécifique de 80 800 FCFA
            if ($sale->id == 17) {
                $sale->unit_price = 80800;
                $sale->original_price = 81000;
            }
            // Pour les autres ventes avec remise
            elseif (!$sale->original_price) {
                $sale->original_price = $sale->unit_price + $sale->discount_per_ton;
            }
        }
        // Vérifier si c'est une vente avec prix augmenté
        elseif ($sale->price_modified && $sale->original_price && ($sale->unit_price > $sale->original_price)) {
            $sale->sale_type = 'increased'; // Vente avec prix augmenté
        }
        
        return view('cement-manager.sales.show', compact('sale'));
    }
    
    /**
     * Affiche les détails d'une vente avec les informations de remise et d'augmentation de prix
     */
    public function showDetails(Sale $sale)
    {
        $sale->load(['supply.details.product', 'city', 'createdBy', 'driver']);

        // Pour les anciennes ventes avec remise mais sans original_price, calculer original_price
        if (($sale->discount_per_ton ?? 0) > 0) {
            // Si c'est la vente #17 qui a un prix spécifique de 80 800 FCFA
            if ($sale->id == 17) {
                $sale->unit_price = 80800;
                $sale->original_price = 81000;
            }
            // Pour les autres ventes avec remise
            elseif (!$sale->original_price) {
                $sale->original_price = $sale->unit_price + $sale->discount_per_ton;
            }
        }

        return view('cement-manager.sales.details', compact('sale'));
    }

    /**
     * Génère et affiche le reçu de vente au format A5
     */
    public function receipt(Sale $sale)
    {
        $sale->load(['supply.details.product', 'city', 'createdBy', 'driver']);

        // Pour les anciennes ventes avec remise mais sans original_price, calculer original_price
        if (($sale->discount_per_ton ?? 0) > 0) {
            if ($sale->id == 17) {
                $sale->unit_price = 80800;
                $sale->original_price = 81000;
            } elseif (!$sale->original_price) {
                $sale->original_price = $sale->unit_price + $sale->discount_per_ton;
            }
        }

        return view('cement-manager.sales.receipt', compact('sale'));
    }

    /**
     * Génère le reçu de vente en PDF au format A5
     */
    public function receiptPdf(Sale $sale)
    {
        $sale->load(['supply.details.product', 'city', 'createdBy', 'driver']);

        // Pour les anciennes ventes avec remise mais sans original_price, calculer original_price
        if (($sale->discount_per_ton ?? 0) > 0) {
            if ($sale->id == 17) {
                $sale->unit_price = 80800;
                $sale->original_price = 81000;
            } elseif (!$sale->original_price) {
                $sale->original_price = $sale->unit_price + $sale->discount_per_ton;
            }
        }

        try {
            $pdf = \PDF::loadView('cement-manager.sales.receipt-pdf', compact('sale'))
                      ->setPaper('A5', 'portrait')
                      ->setOptions([
                          'defaultFont' => 'sans-serif',
                          'isRemoteEnabled' => true,
                          'isHtml5ParserEnabled' => true,
                          'dpi' => 150,
                          'defaultPaperSize' => 'A5'
                      ]);

            return $pdf->stream('recu-vente-' . $sale->id . '.pdf');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de la génération du reçu: ' . $e->getMessage());
        }
    }

    /**
     * Exporte toutes les ventes en Excel
     */
    public function exportExcel()
    {
        try {
            // Récupérer les ventes
            $sales = Sale::with(['supply.details.product', 'city', 'createdBy'])
                ->where(function($query) {
                    $query->where('admin_validation_status', '!=', 'rejected')
                          ->orWhereNull('admin_validation_status');
                })
                ->orderBy('created_at', 'desc')
                ->get();

            // Calculer les statistiques
            $stats = [
                'total_sales' => $sales->count(),
                'total_amount' => $sales->sum('total_amount'),
                'total_paid' => $sales->sum('amount_paid'),
                'total_pending' => $sales->sum('total_amount') - $sales->sum('amount_paid'),
                'average_sale' => $sales->count() > 0 ? $sales->avg('total_amount') : 0
            ];

            $data = [
                'sales' => $sales,
                'stats' => $stats,
                'start_date' => $sales->min('created_at'),
                'end_date' => $sales->max('created_at')
            ];

            return Excel::download(new SalesExport($data), 'ventes-' . date('Y-m-d') . '.xlsx');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de l\'export Excel: ' . $e->getMessage());
        }
    }

    /**
     * Génère un PDF avec toutes les ventes
     */
    public function exportPdf()
    {
        try {
            $sales = Sale::with(['supply.details.product', 'city', 'createdBy'])
                ->where(function($query) {
                    $query->where('admin_validation_status', '!=', 'rejected')
                          ->orWhereNull('admin_validation_status');
                })
                ->orderBy('created_at', 'desc')
                ->get();

            $pdf = \PDF::loadView('cement-manager.sales.export-pdf', compact('sales'))
                      ->setPaper('A4', 'landscape')
                      ->setOptions([
                          'defaultFont' => 'sans-serif',
                          'isRemoteEnabled' => true,
                          'isHtml5ParserEnabled' => true,
                          'dpi' => 150
                      ]);

            return $pdf->download('liste-ventes-' . date('Y-m-d') . '.pdf');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de la génération du PDF: ' . $e->getMessage());
        }
    }

    public function updateDeliveryStatus(Sale $sale, Request $request)
    {
        $request->validate([
            'status' => 'required|in:pending,in_progress,completed,cancelled'
        ]);

        if ($this->saleService->updateDeliveryStatus($sale, $request->status)) {
            return response()->json([
                'success' => true,
                'message' => 'Statut de livraison mis à jour',
                'sale' => $sale->fresh()
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Erreur lors de la mise à jour du statut'
        ], 422);
    }

    /**
     * Vérifie s'il y a des ventes rejetées par l'administrateur
     */
    public function checkRejectedSales(Request $request)
    {
        $lastChecked = $request->input('last_checked', 0);
        
        // Récupérer les ventes rejetées après le dernier ID vérifié
        $rejectedSales = Sale::where('admin_validation_status', 'rejected')
            ->where('id', '>', $lastChecked)
            ->whereNotNull('admin_note')
            ->orderBy('id', 'desc')
            ->with(['supply', 'city', 'product'])
            ->take(5) // Limiter à 5 ventes rejetées pour éviter de surcharger la réponse
            ->get();
        
        return response()->json([
            'success' => true,
            'rejectedSales' => $rejectedSales
        ]);
    }
    
    public function processPayment(Sale $sale, Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0'
        ]);

        if ($this->saleService->processPayment($sale, $request->amount)) {
            return response()->json([
                'success' => true,
                'message' => 'Paiement traité avec succès',
                'sale' => $sale->fresh()
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Erreur lors du traitement du paiement'
        ], 422);
    }
    
    /**
     * Récupère les données mises à jour d'une carte spécifique après rejet d'une vente
     * Cette méthode est appelée via AJAX pour mettre à jour les cartes sans recharger la page
     */
    public function getSupplyCardData(Request $request)
    {
        $request->validate([
            'supply_id' => 'required|exists:supplies,id',
            'vehicle_id' => 'required|exists:trucks,id'
        ]);
        
        try {
            // Récupérer l'approvisionnement avec les détails nécessaires
            $supply = Supply::with([
                'details.product',
                'cities.city',
                'cities.vehicle',
                'cities.driver'
            ])->findOrFail($request->supply_id);
            
            // Récupérer les détails de la ville pour ce véhicule
            $cityDetail = $supply->cities()
                ->where('vehicle_id', $request->vehicle_id)
                ->first();
            
            if (!$cityDetail) {
                throw new \Exception('Détails de la ville non trouvés pour ce véhicule');
            }
            
            // Calculer la quantité utilisée
            $usedQuantity = $cityDetail->quantity - ($cityDetail->remaining_quantity ?? $cityDetail->quantity);
            
            // Préparer les données à retourner
            $data = [
                'supply_id' => $supply->id,
                'vehicle_id' => $cityDetail->vehicle_id,
                'cityDetail' => [
                    'city_id' => $cityDetail->city_id,
                    'city_name' => $cityDetail->city->name,
                    'vehicle_registration' => $cityDetail->vehicle->registration_number,
                    'vehicle_capacity' => $cityDetail->vehicle->capacity->capacity ?? 0,
                    'driver_name' => $cityDetail->driver ? $cityDetail->driver->first_name . ' ' . $cityDetail->driver->last_name : 'Non assigné',
                    'total_quantity' => $cityDetail->quantity,
                    'remaining_quantity' => $cityDetail->remaining_quantity ?? $cityDetail->quantity,
                    'used_quantity' => $usedQuantity
                ]
            ];
            
            return response()->json([
                'success' => true,
                'data' => $data
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }
}
