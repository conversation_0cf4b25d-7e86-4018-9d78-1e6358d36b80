<?php

namespace App\Http\Controllers\IronManager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Supply;
use App\Models\Category;

class InventoryController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:iron_manager']);
    }

    public function index()
    {
        $products = Product::where('category', 'iron')
            ->with(['category'])
            ->latest()
            ->paginate(15);

        $lowStockProducts = Product::where('category', 'iron')
            ->where('stock_quantity', '<', 10)
            ->count();

        $totalProducts = Product::where('category', 'iron')->count();
        $totalValue = Product::where('category', 'iron')
            ->sum(\DB::raw('stock_quantity * price'));

        $stats = [
            'total_products' => $totalProducts,
            'low_stock_products' => $lowStockProducts,
            'total_value' => $totalValue,
        ];

        return view('iron-manager.inventory.index', compact('products', 'stats'));
    }

    public function show(Product $product)
    {
        if ($product->category !== 'iron') {
            abort(404, 'Produit non trouvé.');
        }

        $product->load(['category', 'supplies' => function($query) {
            $query->latest()->take(10);
        }]);

        return view('iron-manager.inventory.show', compact('product'));
    }

    public function create()
    {
        $categories = Category::where('name', 'like', '%fer%')
            ->orWhere('name', 'like', '%iron%')
            ->get();
        
        return view('iron-manager.inventory.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'minimum_stock' => 'required|integer|min:0',
            'category_id' => 'required|exists:categories,id',
            'unit' => 'required|string|max:50',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:255',
        ]);

        $validated['category'] = 'iron';
        $validated['is_active'] = true;

        $product = Product::create($validated);

        return redirect()->route('iron-manager.inventory.show', $product)
            ->with('success', 'Produit créé avec succès.');
    }

    public function edit(Product $product)
    {
        if ($product->category !== 'iron') {
            abort(404, 'Produit non trouvé.');
        }

        $categories = Category::where('name', 'like', '%fer%')
            ->orWhere('name', 'like', '%iron%')
            ->get();
        
        return view('iron-manager.inventory.edit', compact('product', 'categories'));
    }

    public function update(Request $request, Product $product)
    {
        if ($product->category !== 'iron') {
            abort(404, 'Produit non trouvé.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'minimum_stock' => 'required|integer|min:0',
            'category_id' => 'required|exists:categories,id',
            'unit' => 'required|string|max:50',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        $product->update($validated);

        return redirect()->route('iron-manager.inventory.show', $product)
            ->with('success', 'Produit mis à jour avec succès.');
    }
}
