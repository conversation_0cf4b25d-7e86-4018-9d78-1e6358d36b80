<?php
// Script de réparation des graphiques

echo "🔧 RÉPARATION DES GRAPHIQUES\n";
echo "============================\n\n";

$viewPath = "resources/views/admin/dashboard.blade.php";
$content = file_get_contents($viewPath);

// Backup
$backupPath = $viewPath . ".repair-backup." . date("Y-m-d-H-i-s");
copy($viewPath, $backupPath);
echo "📋 Backup: $backupPath\n";

// Supprimer tout le JavaScript et le remplacer par une version ultra-simple
$scriptStart = strpos($content, "@push('scripts')");
$scriptEnd = strpos($content, "@endpush", $scriptStart);

if ($scriptStart !== false && $scriptEnd !== false) {
    $beforeScripts = substr($content, 0, $scriptStart);
    $afterScripts = substr($content, $scriptEnd);
    
    $simpleScript = '@push("scripts")
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>
<script>
document.addEventListener("DOMContentLoaded", function() {
    console.log("🚀 Initialisation graphiques GRADIS");
    
    if (typeof ApexCharts === "undefined") {
        console.error("❌ ApexCharts non chargé");
        return;
    }
    
    // Données de test
    const testData = [15000, 22000, 18000, 25000, 30000, 28000];
    const testCategories = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"];
    
    // Fonction de création sécurisée
    function createSafeChart(selector, options, name) {
        const container = document.querySelector(selector);
        if (!container) {
            console.warn("⚠️ Conteneur " + selector + " non trouvé");
            return;
        }
        
        try {
            const chart = new ApexCharts(container, options);
            chart.render();
            console.log("✅ Graphique " + name + " créé");
        } catch (error) {
            console.error("❌ Erreur " + name + ":", error);
        }
    }
    
    // Graphique 1 - Revenus
    setTimeout(function() {
        createSafeChart("#revenueChart", {
            series: [{ name: "Revenus", data: testData }],
            chart: { type: "area", height: 300, toolbar: { show: false } },
            xaxis: { categories: testCategories },
            colors: ["#007bff"]
        }, "Revenus");
    }, 100);
    
    // Graphique 2 - Ressources  
    setTimeout(function() {
        createSafeChart("#resourcesChart", {
            series: [{ name: "Ressources", data: testData }],
            chart: { type: "bar", height: 300, toolbar: { show: false } },
            xaxis: { categories: testCategories },
            colors: ["#28a745"]
        }, "Ressources");
    }, 200);
    
    // Graphique 3 - Catégories
    setTimeout(function() {
        createSafeChart("#categoryRevenueChart", {
            series: [45000, 32000, 28000, 23000],
            chart: { type: "donut", height: 300 },
            labels: ["Ciment", "Fer", "Sable", "Gravier"],
            colors: ["#007bff", "#28a745", "#ffc107", "#dc3545"]
        }, "Catégories");
    }, 300);
    
    // Graphique 4 - Ciment
    setTimeout(function() {
        createSafeChart("#cementOrdersChart", {
            series: [{ name: "Tonnage", data: [100, 150, 120, 180, 200, 180] }],
            chart: { type: "bar", height: 300, toolbar: { show: false } },
            xaxis: { categories: testCategories },
            colors: ["#17a2b8"]
        }, "Ciment");
    }, 400);
});
</script>
@endpush';
    
    $content = $beforeScripts . $simpleScript . $afterScripts;
    
    if (file_put_contents($viewPath, $content)) {
        echo "✅ Script JavaScript ultra-simple installé\n";
        echo "📊 Les graphiques devraient maintenant fonctionner\n";
    } else {
        echo "❌ Erreur sauvegarde\n";
    }
} else {
    echo "❌ Section scripts non trouvée\n";
}

echo "\n🎯 RÉPARATION TERMINÉE\n";
?>