<?php

echo "🔄 ÉLIMINATION DES BOUCLES INFINIES\n";
echo "==================================\n\n";

$viewPath = 'resources/views/admin/dashboard.blade.php';
$content = file_get_contents($viewPath);

if ($content === false) {
    echo "❌ Impossible de lire le fichier\n";
    exit(1);
}

echo "🔍 Recherche des boucles infinies et code problématique...\n";

// Backup
$backupPath = $viewPath . '.loops-fix-backup.' . date('Y-m-d-H-i-s');
copy($viewPath, $backupPath);
echo "📋 Backup créé: $backupPath\n";

$fixes = 0;

// 1. Supprimer les setInterval qui peuvent causer des boucles
$intervalPatterns = [
    '/setInterval\s*\([^)]+\)\s*;?/i',
    '/window\.setInterval\s*\([^)]+\)\s*;?/i',
];

foreach ($intervalPatterns as $pattern) {
    if (preg_match($pattern, $content)) {
        $content = preg_replace($pattern, '// setInterval supprimé pour éviter les boucles', $content);
        echo "✅ setInterval supprimé\n";
        $fixes++;
    }
}

// 2. Supprimer les setTimeout excessifs (plus de 5000ms)
$content = preg_replace_callback('/setTimeout\s*\([^,]+,\s*([0-9]+)\s*\)/', function($matches) {
    $delay = intval($matches[1]);
    if ($delay > 5000) {
        return '// setTimeout long supprimé (' . $delay . 'ms)';
    }
    return $matches[0];
}, $content);

// 3. Supprimer les boucles while et for infinies potentielles
$loopPatterns = [
    '/while\s*\(\s*true\s*\)/i',
    '/for\s*\(\s*;\s*;\s*\)/i',
    '/while\s*\(\s*1\s*\)/i',
];

foreach ($loopPatterns as $pattern) {
    if (preg_match($pattern, $content)) {
        $content = preg_replace($pattern, '// Boucle infinie supprimée', $content);
        echo "✅ Boucle infinie supprimée\n";
        $fixes++;
    }
}

// 4. Supprimer les appels récursifs problématiques
$recursivePatterns = [
    '/function\s+(\w+)[^}]+\1\s*\(/i', // Fonction qui s'appelle elle-même
];

foreach ($recursivePatterns as $pattern) {
    if (preg_match($pattern, $content)) {
        echo "⚠️ Récursion potentielle détectée\n";
    }
}

// 5. Limiter les addEventListener multiples
$eventListenerCount = substr_count($content, 'addEventListener');
if ($eventListenerCount > 10) {
    echo "⚠️ Trop d'event listeners ($eventListenerCount), risque de performance\n";
}

// 6. Supprimer les auto-refresh automatiques
$autoRefreshPatterns = [
    '/location\.reload\s*\(\s*\)/i',
    '/window\.location\.reload\s*\(\s*\)/i',
    '/document\.location\.reload\s*\(\s*\)/i',
];

foreach ($autoRefreshPatterns as $pattern) {
    if (preg_match($pattern, $content)) {
        $content = preg_replace($pattern, '// Auto-reload supprimé', $content);
        echo "✅ Auto-reload supprimé\n";
        $fixes++;
    }
}

// 7. Optimiser les requêtes AJAX répétitives
if (strpos($content, '$.ajax') !== false || strpos($content, 'fetch(') !== false) {
    echo "ℹ️ Requêtes AJAX détectées - vérifiez qu'elles ne sont pas en boucle\n";
}

// 8. Ajouter des garde-fous contre les boucles
$guardCode = '
// Garde-fou contre les boucles infinies
let chartInitialized = false;
let initializationInProgress = false;

function safeInitializeCharts() {
    if (chartInitialized || initializationInProgress) {
        console.log("🛡️ Initialisation déjà en cours ou terminée");
        return;
    }
    
    initializationInProgress = true;
    console.log("🚀 Début initialisation sécurisée des graphiques");
    
    try {
        // Code d\'initialisation ici
        chartInitialized = true;
        console.log("✅ Graphiques initialisés avec succès");
    } catch (error) {
        console.error("❌ Erreur initialisation:", error);
    } finally {
        initializationInProgress = false;
    }
}
';

// Insérer le garde-fou au début du script
$scriptPos = strpos($content, 'document.addEventListener(\'DOMContentLoaded\'');
if ($scriptPos !== false) {
    $content = substr_replace($content, $guardCode . "\n", $scriptPos, 0);
    echo "✅ Garde-fou contre les boucles ajouté\n";
    $fixes++;
}

// 9. Supprimer les console.log excessifs qui peuvent ralentir
$consoleLogCount = substr_count($content, 'console.log');
if ($consoleLogCount > 20) {
    // Garder seulement les console.log importants
    $content = preg_replace('/console\.log\([^)]*\);?\s*\n?/', '', $content);
    // Remettre quelques console.log importants
    $content = str_replace('🚀 Initialisation des graphiques GRADIS', '🚀 Initialisation des graphiques GRADIS\');
    console.log(\'', $content);
    echo "✅ Console.log excessifs supprimés\n";
    $fixes++;
}

// 10. Vérifier les timeouts en cascade
$timeoutCount = substr_count($content, 'setTimeout');
if ($timeoutCount > 10) {
    echo "⚠️ Trop de setTimeout ($timeoutCount) - risque de ralentissement\n";
}

// Sauvegarder les corrections
if ($fixes > 0) {
    if (file_put_contents($viewPath, $content) !== false) {
        echo "\n✅ Fichier corrigé avec $fixes modifications!\n";
    } else {
        echo "\n❌ Erreur lors de la sauvegarde\n";
        exit(1);
    }
} else {
    echo "\nℹ️ Aucune boucle infinie détectée\n";
}

echo "\n🎯 VÉRIFICATIONS EFFECTUÉES:\n";
echo "============================\n";
echo "✅ setInterval supprimés\n";
echo "✅ setTimeout longs supprimés\n";
echo "✅ Boucles infinites supprimées\n";
echo "✅ Auto-reload supprimés\n";
echo "✅ Garde-fou ajouté\n";
echo "✅ Console.log optimisés\n";

echo "\n🚀 Plus de risque de boucles infinies!\n";
echo "📊 Les graphiques devraient se charger rapidement!\n";
