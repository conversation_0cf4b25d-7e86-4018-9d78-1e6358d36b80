<?php

echo "🚨 CORRECTION DES CONFLITS JAVASCRIPT\n";
echo "====================================\n\n";

$viewPath = 'resources/views/admin/dashboard.blade.php';
$content = file_get_contents($viewPath);

// Backup
$backupPath = $viewPath . '.conflict-fix.' . date('Y-m-d-H-i-s');
copy($viewPath, $backupPath);
echo "📋 Backup: $backupPath\n";

// Supprimer complètement tous les scripts et recommencer
$scriptStart = strpos($content, '@push(\'scripts\')');
$scriptEnd = strpos($content, '@endpush');

if ($scriptStart !== false && $scriptEnd !== false) {
    $beforeScripts = substr($content, 0, $scriptStart);
    $afterScripts = substr($content, $scriptEnd + strlen('@endpush'));
    
    // Script SANS CONFLITS - Version finale
    $cleanScript = '@push(\'scripts\')
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>

<script>
// GRADIS Charts - Version sans conflits
(function() {
    "use strict";
    
    console.log("🎯 GRADIS Charts v3.0 - Démarrage");
    
    // Désactiver les scripts qui interfèrent
    if (window.adminUltraFast) {
        window.adminUltraFast = null;
        console.log("🛡️ admin-ultra-fast désactivé");
    }
    
    // Attendre le chargement complet
    function initCharts() {
        console.log("📊 Initialisation des graphiques...");
        
        if (typeof ApexCharts === "undefined") {
            console.error("❌ ApexCharts non disponible");
            return;
        }
        
        console.log("✅ ApexCharts OK");
        
        // Données fixes pour éviter les erreurs
        const monthlyData = [15000, 22000, 18000, 25000, 30000, 28000];
        const months = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"];
        const categoryData = [45, 30, 15, 10];
        const categoryLabels = ["Ciment", "Fer", "Sable", "Gravier"];
        
        // Fonction de création robuste
        function createChart(containerId, config, name) {
            const container = document.getElementById(containerId);
            if (!container) {
                console.warn(`⚠️ Conteneur ${containerId} introuvable`);
                return;
            }
            
            try {
                // Nettoyer le conteneur
                container.innerHTML = "";
                
                const chart = new ApexCharts(container, config);
                chart.render().then(() => {
                    console.log(`✅ ${name} créé avec succès`);
                }).catch(error => {
                    console.error(`❌ Erreur ${name}:`, error);
                });
                
            } catch (error) {
                console.error(`❌ Exception ${name}:`, error);
            }
        }
        
        // Créer les graphiques un par un
        setTimeout(() => {
            createChart("revenueChart", {
                series: [{ name: "Revenus", data: monthlyData }],
                chart: { 
                    type: "area", 
                    height: 350, 
                    toolbar: { show: false },
                    animations: { enabled: false }
                },
                xaxis: { categories: months },
                colors: ["#007bff"],
                fill: {
                    type: "gradient",
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.3
                    }
                }
            }, "Graphique Revenus");
        }, 1000);
        
        setTimeout(() => {
            createChart("resourcesChart", {
                series: [{ name: "Ressources", data: monthlyData }],
                chart: { 
                    type: "bar", 
                    height: 350, 
                    toolbar: { show: false },
                    animations: { enabled: false }
                },
                xaxis: { categories: months },
                colors: ["#28a745"]
            }, "Graphique Ressources");
        }, 1500);
        
        setTimeout(() => {
            createChart("categoryRevenueChart", {
                series: categoryData,
                chart: { 
                    type: "donut", 
                    height: 300,
                    animations: { enabled: false }
                },
                labels: categoryLabels,
                colors: ["#007bff", "#28a745", "#ffc107", "#dc3545"],
                legend: { position: "bottom" }
            }, "Graphique Catégories");
        }, 2000);
        
        setTimeout(() => {
            createChart("cementOrdersChart", {
                series: [{ name: "Tonnage", data: [100, 150, 120, 180, 200, 160] }],
                chart: { 
                    type: "bar", 
                    height: 300, 
                    toolbar: { show: false },
                    animations: { enabled: false }
                },
                xaxis: { categories: months },
                colors: ["#17a2b8"]
            }, "Graphique Ciment");
        }, 2500);
        
        console.log("⏰ Tous les graphiques programmés");
    }
    
    // Démarrer quand tout est prêt
    if (document.readyState === "complete") {
        initCharts();
    } else {
        window.addEventListener("load", initCharts);
    }
    
})();
</script>
@endpush';
    
    $newContent = $beforeScripts . $cleanScript . $afterScripts;
    
    if (file_put_contents($viewPath, $newContent)) {
        echo "✅ Script sans conflits installé\n";
    } else {
        echo "❌ Erreur sauvegarde\n";
        exit(1);
    }
} else {
    echo "❌ Section scripts non trouvée\n";
    exit(1);
}

// Désactiver temporairement admin-ultra-fast.js
$adminFastPath = 'public/js/admin-ultra-fast.js';
if (file_exists($adminFastPath)) {
    $adminFastBackup = $adminFastPath . '.disabled.' . date('Y-m-d-H-i-s');
    if (rename($adminFastPath, $adminFastBackup)) {
        echo "🛡️ admin-ultra-fast.js désactivé temporairement\n";
        echo "📋 Backup: $adminFastBackup\n";
    }
}

// Vider tous les caches
echo "\n🧹 VIDAGE COMPLET DES CACHES\n";
echo "---------------------------\n";

$commands = [
    'php artisan view:clear',
    'php artisan cache:clear',
    'php artisan config:clear',
    'php artisan route:clear'
];

foreach ($commands as $cmd) {
    echo "⚡ $cmd\n";
    exec($cmd . ' 2>&1', $output, $return);
    if ($return === 0) {
        echo "✅ OK\n";
    }
}

echo "\n🎯 CORRECTION DES CONFLITS TERMINÉE\n";
echo "==================================\n";
echo "✅ Script sans conflits installé\n";
echo "✅ admin-ultra-fast.js désactivé temporairement\n";
echo "✅ Tous les caches vidés\n";
echo "✅ Délais augmentés pour éviter les conflits\n";
echo "✅ Fonction IIFE pour isoler le code\n";

echo "\n🚀 TESTEZ MAINTENANT:\n";
echo "====================\n";
echo "1. 🌐 Allez sur: http://127.0.0.1:8000/admin/dashboard\n";
echo "2. 🔄 Rafraîchissez complètement (Ctrl+Shift+R)\n";
echo "3. 🔍 Ouvrez la console (F12)\n";
echo "4. 📊 Attendez 3-4 secondes pour voir les graphiques\n";

echo "\n💡 DANS LA CONSOLE VOUS DEVRIEZ VOIR:\n";
echo "====================================\n";
echo "🎯 GRADIS Charts v3.0 - Démarrage\n";
echo "🛡️ admin-ultra-fast désactivé\n";
echo "📊 Initialisation des graphiques...\n";
echo "✅ ApexCharts OK\n";
echo "✅ Graphique Revenus créé avec succès\n";
echo "✅ Graphique Ressources créé avec succès\n";
echo "✅ Graphique Catégories créé avec succès\n";
echo "✅ Graphique Ciment créé avec succès\n";

echo "\n🎉 SOLUTION ANTI-CONFLITS APPLIQUÉE!\n";
