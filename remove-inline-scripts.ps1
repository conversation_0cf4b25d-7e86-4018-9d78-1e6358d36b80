# Script PowerShell pour supprimer les scripts inline du fichier dashboard-professional.blade.php
# et les remplacer par des commentaires

$filePath = "resources\views\accountant\dashboard-professional.blade.php"

Write-Host "🔧 Suppression des scripts inline de $filePath..." -ForegroundColor Yellow

# Lire le contenu du fichier
$content = Get-Content $filePath -Raw

# Compter les scripts avant
$scriptsBefore = ([regex]::Matches($content, '<script[^>]*>.*?</script>', [System.Text.RegularExpressions.RegexOptions]::Singleline)).Count
Write-Host "📊 Scripts trouvés: $scriptsBefore" -ForegroundColor Cyan

# Remplacer tous les blocs <script>...</script> par des commentaires
$content = [regex]::Replace($content, '<script[^>]*>.*?</script>', '<!-- Script inline supprimé - géré dans dashboard-professional-optimized.js -->', [System.Text.RegularExpressions.RegexOptions]::Singleline)

# Compter les scripts après
$scriptsAfter = ([regex]::Matches($content, '<script[^>]*>.*?</script>', [System.Text.RegularExpressions.RegexOptions]::Singleline)).Count
Write-Host "📊 Scripts restants: $scriptsAfter" -ForegroundColor Cyan

# Supprimer les lignes vides multiples
$content = [regex]::Replace($content, '\n\s*\n\s*\n', "`n`n", [System.Text.RegularExpressions.RegexOptions]::Multiline)

# Écrire le contenu modifié
Set-Content $filePath $content -Encoding UTF8

# Afficher les statistiques
$lines = (Get-Content $filePath | Measure-Object -Line).Lines
$size = (Get-Item $filePath).Length

Write-Host "✅ Optimisation terminée!" -ForegroundColor Green
Write-Host "📈 Scripts supprimés: $($scriptsBefore - $scriptsAfter)" -ForegroundColor Green
Write-Host "📄 Nouvelles lignes: $lines" -ForegroundColor Green
Write-Host "💾 Nouvelle taille: $([math]::Round($size/1KB, 2)) KB" -ForegroundColor Green
