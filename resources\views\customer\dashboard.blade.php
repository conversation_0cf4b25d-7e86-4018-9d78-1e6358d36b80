@extends('layouts.app')

@section('title', 'Tableau de bord Client')

@push('styles')
<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }
    .dashboard-card {
        border: none;
        border-radius: 20px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        overflow: hidden;
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }
    .dashboard-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    }
    .stat-icon {
        width: 70px;
        height: 70px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .welcome-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 25px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 50px rgba(102, 126, 234, 0.4);
        position: relative;
        overflow: hidden;
    }
    .welcome-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
    .quick-action-btn {
        border-radius: 15px;
        padding: 15px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 2px solid rgba(255,255,255,0.3);
        margin: 5px;
        backdrop-filter: blur(10px);
    }
    .quick-action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        border-color: rgba(255,255,255,0.6);
    }
    .order-timeline {
        position: relative;
        padding-left: 40px;
    }
    .order-timeline::before {
        content: '';
        position: absolute;
        left: 20px;
        top: 0;
        bottom: 0;
        width: 3px;
        background: linear-gradient(to bottom, #667eea, #764ba2);
        border-radius: 2px;
    }
    .timeline-item {
        position: relative;
        margin-bottom: 30px;
        padding: 20px;
        background: rgba(255,255,255,0.7);
        border-radius: 15px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }
    .timeline-item:hover {
        transform: translateX(10px);
        background: rgba(255,255,255,0.9);
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -30px;
        top: 25px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: 4px solid white;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
    }
    .progress-ring {
        width: 100px;
        height: 100px;
    }
    .progress-ring circle {
        fill: transparent;
        stroke-width: 6;
        stroke-linecap: round;
        transform: rotate(-90deg);
        transform-origin: 50% 50%;
        filter: drop-shadow(0 0 10px rgba(255,255,255,0.5));
    }
    .badge {
        font-size: 0.75rem;
        padding: 8px 12px;
        font-weight: 600;
    }
    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8, #6a4190);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    }
    .btn-outline-primary {
        border-color: #667eea;
        color: #667eea;
    }
    .btn-outline-primary:hover {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-color: #667eea;
    }

    /* Styles pour la section feedback */
    .btn-gradient-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        transition: all 0.3s ease;
    }
    .btn-gradient-primary:hover {
        background: linear-gradient(135deg, #5a6fd8, #6a4190);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        transform: translateY(-2px);
        color: white;
    }

    .rating-stars .rating-star {
        transition: all 0.2s ease;
        margin: 0 1px;
    }
    .rating-stars .rating-star:hover {
        color: #ffc107 !important;
        transform: scale(1.2);
    }
    .rating-stars .rating-star.active {
        color: #ffc107 !important;
    }

    /* Animation pour les étoiles */
    @keyframes starPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.3); }
        100% { transform: scale(1); }
    }
    .rating-star.pulse {
        animation: starPulse 0.3s ease;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .welcome-card {
            padding: 1.5rem;
            text-align: center;
        }
        .stat-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }
        .order-timeline {
            padding-left: 20px;
        }
        .timeline-item {
            margin-left: -10px;
        }
        .dashboard-card:hover {
            transform: none;
        }
        .rating-stars {
            text-align: center;
            margin-top: 5px;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid px-4">
    <!-- En-tête de bienvenue -->
    <div class="welcome-card">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">👋 Bonjour, {{ Auth::user()->name }} !</h2>
                <p class="mb-3 opacity-90">Bienvenue sur votre tableau de bord. Gérez vos commandes et suivez vos livraisons en temps réel.</p>
                <div class="d-flex flex-wrap">
                    <button class="quick-action-btn btn btn-light me-2 mb-2">
                        <i class="fas fa-plus me-2"></i>Nouvelle commande
                    </button>
                    <button class="quick-action-btn btn btn-outline-light me-2 mb-2">
                        <i class="fas fa-file-invoice me-2"></i>Mes factures
                    </button>
                    <button class="quick-action-btn btn btn-outline-light mb-2">
                        <i class="fas fa-headset me-2"></i>Support
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="position-relative">
                    <svg class="progress-ring" viewBox="0 0 80 80">
                        <circle cx="40" cy="40" r="32" stroke="#ffffff40" />
                        <circle cx="40" cy="40" r="32" stroke="#ffffff"
                                stroke-dasharray="201" stroke-dashoffset="50" />
                    </svg>
                    <div class="position-absolute top-50 start-50 translate-middle text-center">
                        <div class="h4 mb-0">75%</div>
                        <small>Satisfaction</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques modernes -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card card h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon me-3" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">Total Commandes</h6>
                            <h2 class="mb-0 fw-bold">{{ $stats['total_orders'] }}</h2>
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>+12% ce mois
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card card h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon me-3" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">En Attente</h6>
                            <h2 class="mb-0 fw-bold">{{ $stats['pending_orders'] }}</h2>
                            <small class="text-warning">
                                <i class="fas fa-hourglass-half me-1"></i>En traitement
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card card h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon me-3" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">Terminées</h6>
                            <h2 class="mb-0 fw-bold">{{ $stats['completed_orders'] }}</h2>
                            <small class="text-success">
                                <i class="fas fa-check me-1"></i>Livrées
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card card h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon me-3" style="background: linear-gradient(135deg, #fa709a, #fee140);">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="text-muted mb-1">Ce Mois</h6>
                            <h2 class="mb-0 fw-bold">{{ $stats['monthly_orders'] }}</h2>
                            <small class="text-info">
                                <i class="fas fa-calendar me-1"></i>{{ date('F Y') }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Commandes récentes avec design moderne -->
        <div class="col-xl-8">
            <div class="dashboard-card card">
                <div class="card-header bg-transparent border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="card-title mb-1">📦 Commandes Récentes</h4>
                            <p class="text-muted mb-0">Suivez l'état de vos dernières commandes</p>
                        </div>
                        <a href="{{ route('customer.orders.index') }}" class="btn btn-primary btn-sm rounded-pill">
                            <i class="fas fa-eye me-1"></i>Voir tout
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($recent_orders->count() > 0)
                        <div class="order-timeline">
                            @foreach($recent_orders as $order)
                            <div class="timeline-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="mb-0 me-3">Commande #{{ $order->id }}</h6>
                                            @if($order->status == 'pending')
                                                <span class="badge bg-warning-subtle text-warning rounded-pill">
                                                    <i class="fas fa-clock me-1"></i>En attente
                                                </span>
                                            @elseif($order->status == 'processing')
                                                <span class="badge bg-info-subtle text-info rounded-pill">
                                                    <i class="fas fa-cog me-1"></i>En cours
                                                </span>
                                            @elseif($order->status == 'completed')
                                                <span class="badge bg-success-subtle text-success rounded-pill">
                                                    <i class="fas fa-check me-1"></i>Terminée
                                                </span>
                                            @else
                                                <span class="badge bg-secondary-subtle text-secondary rounded-pill">{{ $order->status }}</span>
                                            @endif
                                        </div>
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-cube me-2"></i>{{ $order->cementOrder->product->name ?? 'N/A' }}
                                            <span class="mx-2">•</span>
                                            <i class="fas fa-weight me-1"></i>{{ $order->total_tonnage }} {{ $order->cementOrder->product->unit ?? 'T' }}
                                        </p>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>{{ $order->created_at->format('d/m/Y à H:i') }}
                                        </small>
                                    </div>
                                    <div class="ms-3">
                                        <a href="{{ route('customer.orders.show', $order) }}" class="btn btn-outline-primary btn-sm rounded-pill">
                                            <i class="fas fa-eye me-1"></i>Détails
                                        </a>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-shopping-cart text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
                            </div>
                            <h4 class="text-muted mb-3">Aucune commande pour le moment</h4>
                            <p class="text-muted mb-4">Commencez par passer votre première commande pour voir l'historique ici.</p>
                            <a href="{{ route('customer.orders.create') }}" class="btn btn-primary rounded-pill px-4">
                                <i class="fas fa-plus me-2"></i>Passer une commande
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Panneau latéral avec informations utiles -->
        <div class="col-xl-4">
            <!-- Commandes en cours -->
            <div class="dashboard-card card mb-4">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-1">⏳ Commandes en Cours</h5>
                    <p class="text-muted mb-0 small">Suivi en temps réel</p>
                </div>
                <div class="card-body">
                    @if($pending_orders->count() > 0)
                        @foreach($pending_orders as $order)
                        <div class="d-flex align-items-center p-3 mb-3 rounded-3" style="background: linear-gradient(135deg, #fff5f5, #fff0f0);">
                            <div class="me-3">
                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 40px; height: 40px; background: linear-gradient(135deg, #ff6b6b, #ee5a52);">
                                    <i class="fas fa-truck text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">#{{ $order->id }}</h6>
                                <p class="mb-1 small text-muted">{{ $order->cementOrder->product->name ?? 'N/A' }}</p>
                                <small class="text-muted">{{ $order->created_at->format('d/m/Y') }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-warning-subtle text-warning rounded-pill">En cours</span>
                            </div>
                        </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle text-success mb-3" style="font-size: 2.5rem; opacity: 0.7;"></i>
                            <p class="text-muted mb-0">Toutes vos commandes sont à jour !</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="dashboard-card card mb-4">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-1">🚀 Actions Rapides</h5>
                    <p class="text-muted mb-0 small">Accès direct aux fonctionnalités</p>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('customer.orders.create') }}" class="btn btn-primary rounded-pill">
                            <i class="fas fa-plus me-2"></i>Nouvelle Commande
                        </a>
                        <a href="{{ route('customer.orders.index') }}" class="btn btn-outline-primary rounded-pill">
                            <i class="fas fa-list me-2"></i>Mes Commandes
                        </a>
                        <a href="{{ route('customer.invoices.index') }}" class="btn btn-outline-success rounded-pill">
                            <i class="fas fa-file-invoice me-2"></i>Mes Factures
                        </a>
                        <a href="{{ route('customer.profile') }}" class="btn btn-outline-secondary rounded-pill">
                            <i class="fas fa-user me-2"></i>Mon Profil
                        </a>
                    </div>
                </div>
            </div>

            <!-- Section Feedback et Évaluations -->
            <div class="dashboard-card card mb-4">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-1">⭐ Votre Avis Compte</h5>
                    <p class="text-muted mb-0 small">Aidez-nous à améliorer nos services</p>
                </div>
                <div class="card-body">
                    <!-- Bouton pour donner un avis -->
                    <div class="text-center mb-4">
                        <a href="{{ route('demo.customer.feedback.create') }}" class="btn btn-gradient-primary rounded-pill px-4 py-2">
                            <i class="fas fa-star me-2"></i>Donner mon Avis
                        </a>
                    </div>

                    <!-- Statistiques rapides des avis -->
                    <div class="row g-2 mb-3">
                        <div class="col-6">
                            <div class="text-center p-2 rounded-3" style="background: linear-gradient(135deg, #fff3cd, #ffeaa7);">
                                <div class="h5 mb-1 text-warning">4.8</div>
                                <small class="text-muted">Note Moyenne</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-2 rounded-3" style="background: linear-gradient(135deg, #d1ecf1, #bee5eb);">
                                <div class="h5 mb-1 text-info">{{ $stats['total_orders'] ?? 0 }}</div>
                                <small class="text-muted">Avis Donnés</small>
                            </div>
                        </div>
                    </div>

                    <!-- Évaluation rapide des dernières livraisons -->
                    @if($recent_orders->count() > 0)
                    <div class="mb-3">
                        <h6 class="mb-2">Évaluez vos dernières livraisons :</h6>
                        @foreach($recent_orders->take(2) as $order)
                        <div class="d-flex align-items-center justify-content-between p-2 mb-2 rounded-3" style="background: rgba(0,123,255,0.05);">
                            <div class="flex-grow-1">
                                <small class="fw-bold">#{{ $order->id }}</small>
                                <div class="small text-muted">{{ $order->created_at->format('d/m/Y') }}</div>
                            </div>
                            <div class="rating-stars">
                                @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star text-muted rating-star" data-rating="{{ $i }}" data-order="{{ $order->id }}" style="cursor: pointer; font-size: 14px;"></i>
                                @endfor
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @endif

                    <!-- Lien vers tous les avis -->
                    <div class="text-center">
                        <a href="{{ route('demo.customer.feedback.index') }}" class="btn btn-outline-primary btn-sm rounded-pill">
                            <i class="fas fa-list me-1"></i>Voir tous mes avis
                        </a>
                    </div>
                </div>
            </div>

            <!-- Informations de contact -->
            <div class="dashboard-card card">
                <div class="card-header bg-transparent border-0 pb-0">
                    <h5 class="card-title mb-1">📞 Besoin d'aide ?</h5>
                    <p class="text-muted mb-0 small">Notre équipe est là pour vous</p>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3 p-3 rounded-3" style="background: linear-gradient(135deg, #e8f5e8, #f0f8f0);">
                        <div class="me-3">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 40px; height: 40px; background: linear-gradient(135deg, #28a745, #20c997);">
                                <i class="fas fa-phone text-white"></i>
                            </div>
                        </div>
                        <div>
                            <h6 class="mb-1">Service Client</h6>
                            <p class="mb-0 small text-muted">+237 6XX XXX XXX</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center p-3 rounded-3" style="background: linear-gradient(135deg, #e3f2fd, #f0f8ff);">
                        <div class="me-3">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 40px; height: 40px; background: linear-gradient(135deg, #007bff, #0056b3);">
                                <i class="fas fa-envelope text-white"></i>
                            </div>
                        </div>
                        <div>
                            <h6 class="mb-1">Email Support</h6>
                            <p class="mb-0 small text-muted"><EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Animation pour les cartes au chargement
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.dashboard-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });

    // Animation du cercle de progression
    const progressCircle = document.querySelector('.progress-ring circle:last-child');
    if (progressCircle) {
        const circumference = 2 * Math.PI * 32;
        progressCircle.style.strokeDasharray = circumference;
        progressCircle.style.strokeDashoffset = circumference;

        setTimeout(() => {
            progressCircle.style.transition = 'stroke-dashoffset 2s ease-in-out';
            progressCircle.style.strokeDashoffset = circumference - (75 / 100) * circumference;
        }, 1000);
    }

    // Gestion des étoiles de notation
    const ratingStars = document.querySelectorAll('.rating-star');
    ratingStars.forEach(star => {
        star.addEventListener('mouseenter', function() {
            const rating = parseInt(this.dataset.rating);
            const orderId = this.dataset.order;
            const starsInGroup = document.querySelectorAll(`[data-order="${orderId}"]`);

            starsInGroup.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        });

        star.addEventListener('mouseleave', function() {
            const orderId = this.dataset.order;
            const starsInGroup = document.querySelectorAll(`[data-order="${orderId}"]`);
            starsInGroup.forEach(s => s.classList.remove('active'));
        });

        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            const orderId = this.dataset.order;

            // Animation de pulse
            this.classList.add('pulse');
            setTimeout(() => this.classList.remove('pulse'), 300);

            // Envoyer la notation via AJAX
            fetch(`/customer/feedback/quick-rating`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    order_id: orderId,
                    rating: rating
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Afficher un message de succès
                    const toast = document.createElement('div');
                    toast.className = 'alert alert-success position-fixed';
                    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                    toast.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        Merci pour votre évaluation de ${rating} étoile${rating > 1 ? 's' : ''} !
                        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
                    `;
                    document.body.appendChild(toast);

                    // Supprimer le toast après 3 secondes
                    setTimeout(() => {
                        if (toast.parentElement) {
                            toast.remove();
                        }
                    }, 3000);

                    // Marquer les étoiles comme évaluées
                    const starsInGroup = document.querySelectorAll(`[data-order="${orderId}"]`);
                    starsInGroup.forEach((s, index) => {
                        s.style.pointerEvents = 'none';
                        if (index < rating) {
                            s.classList.add('active');
                            s.style.color = '#ffc107';
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                // Rediriger vers la page de feedback complète
                window.location.href = `/customer/feedback/create?order_id=${orderId}`;
            });
        });
    });
</script>
@endpush
