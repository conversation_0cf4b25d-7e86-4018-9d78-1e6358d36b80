<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Models\Supply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $userId = auth()->id();
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;

        // Statistiques avec cache optimisé (5 minutes)
        $stats = Cache::remember("accountant_dashboard_stats_{$userId}", 300, function () use ($userId, $currentMonth, $currentYear) {
            // Requête optimisée pour les commandes et ventes
            $orderStats = DB::table('orders')
                ->selectRaw('
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN status = "completed" AND MONTH(created_at) = ? AND YEAR(created_at) = ? THEN total_amount ELSE 0 END) as total_sales
                ', [$currentMonth, $currentYear])
                ->first();

            // Requête optimisée pour les approvisionnements de l'utilisateur
            $supplyStats = DB::table('supplies')
                ->selectRaw('
                    COUNT(*) as total_supplies,
                    SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_supplies,
                    SUM(total_amount) as total_supply_amount,
                    SUM(CASE WHEN MONTH(created_at) = ? AND YEAR(created_at) = ? THEN 1 ELSE 0 END) as monthly_supplies
                ', [$currentMonth, $currentYear])
                ->where('created_by', $userId)
                ->first();

            // Requête optimisée pour les clients actifs et produits en stock faible
            $miscStats = DB::selectOne('
                SELECT
                    (SELECT COUNT(DISTINCT users.id) FROM users
                     JOIN model_has_roles ON users.id = model_has_roles.model_id
                     JOIN roles ON model_has_roles.role_id = roles.id
                     WHERE roles.name = "customer" AND users.is_active = 1 AND model_has_roles.model_type = "App\\\\Models\\\\User") as active_customers,
                    (SELECT COUNT(*) FROM products WHERE stock_quantity <= 10 AND deleted_at IS NULL) as low_stock_products
            ');

            return [
                'total_sales' => $orderStats->total_sales ?? 0,
                'total_orders' => $orderStats->total_orders ?? 0,
                'active_customers' => $miscStats->active_customers ?? 0,
                'low_stock_products' => $miscStats->low_stock_products ?? 0,
                'total_supplies' => $supplyStats->total_supplies ?? 0,
                'pending_supplies' => $supplyStats->pending_supplies ?? 0,
                'total_supply_amount' => $supplyStats->total_supply_amount ?? 0,
                'monthly_supplies' => $supplyStats->monthly_supplies ?? 0,
            ];
        });

        // Données récentes avec eager loading optimisé (cache de 3 minutes)
        $recentData = Cache::remember("accountant_dashboard_recent_{$userId}", 180, function () use ($userId) {
            // Dernières commandes avec eager loading optimisé
            $latestOrders = Order::with(['user:id,name,email'])
                ->select('id', 'user_id', 'total_amount', 'status', 'created_at')
                ->latest()
                ->limit(10)
                ->get();

            // Derniers approvisionnements avec eager loading optimisé
            $latestSupplies = Supply::with(['supplier:id,name'])
                ->select('id', 'reference', 'supplier_id', 'total_amount', 'status', 'created_at')
                ->where('created_by', $userId)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            return [
                'latestOrders' => $latestOrders,
                'latestSupplies' => $latestSupplies
            ];
        });

        $latestOrders = $recentData['latestOrders'];
        $latestSupplies = $recentData['latestSupplies'];

        // Données pour les graphiques (cache de 10 minutes)
        $chartData = Cache::remember("accountant_dashboard_charts_{$userId}", 600, function () use ($userId) {
            $startDate = Carbon::now()->subDays(6);
            $endDate = Carbon::now();

            // Données pour le graphique des ventes (optimisé)
            $salesData = DB::table('orders')
                ->selectRaw('DATE(created_at) as date, SUM(total_amount) as total')
                ->where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy(DB::raw('DATE(created_at)'))
                ->orderBy('date')
                ->get();

            // Données pour le graphique des approvisionnements par jour (optimisé)
            $dailySupplies = DB::table('supplies')
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->where('created_by', $userId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy(DB::raw('DATE(created_at)'))
                ->orderBy('date')
                ->get();

            return [
                'salesData' => $salesData,
                'dailySupplies' => $dailySupplies
            ];
        });

        $salesData = $chartData['salesData'];
        $dailySupplies = $chartData['dailySupplies'];

        // Données pour les graphiques de catégories et produits (cache de 15 minutes)
        $categoryProductData = Cache::remember("accountant_dashboard_category_product_{$userId}_{$currentMonth}", 900, function () use ($userId, $currentMonth, $currentYear) {
            // Données pour le graphique des catégories (optimisé avec requête directe)
            $categoryData = DB::table('orders')
                ->join('order_items', 'orders.id', '=', 'order_items.order_id')
                ->join('products', 'order_items.product_id', '=', 'products.id')
                ->join('categories', 'products.category_id', '=', 'categories.id')
                ->select('categories.name as category', DB::raw('SUM(order_items.quantity * order_items.price) as amount'))
                ->where('orders.status', 'completed')
                ->whereMonth('orders.created_at', $currentMonth)
                ->whereYear('orders.created_at', $currentYear)
                ->groupBy('categories.id', 'categories.name')
                ->pluck('amount', 'category');

            // Données pour le graphique des montants par produit (optimisé)
            $amountByProduct = DB::table('supplies')
                ->join('supply_details', 'supplies.id', '=', 'supply_details.supply_id')
                ->join('products', 'products.id', '=', 'supply_details.product_id')
                ->select('products.name', DB::raw('SUM(supply_details.quantity * supply_details.unit_price) as total'))
                ->where('supplies.created_by', $userId)
                ->groupBy('products.id', 'products.name')
                ->orderBy('total', 'desc')
                ->limit(5)
                ->get();

            return [
                'categoryData' => $categoryData,
                'amountByProduct' => $amountByProduct
            ];
        });

        $categoryData = $categoryProductData['categoryData'];
        $amountByProduct = $categoryProductData['amountByProduct'];

        return view('accountant.dashboard', compact(
            'stats',
            'latestOrders',
            'latestSupplies',
            'salesData',
            'dailySupplies',
            'categoryData',
            'amountByProduct'
        ));
    }

    public function generateReport(Request $request)
    {
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        $report = [
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d')
            ],
            'sales' => Order::whereBetween('created_at', [$startDate, $endDate])->sum('total_amount'),
            'orders' => Order::whereBetween('created_at', [$startDate, $endDate])->count(),
            'new_customers' => User::role('customer')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'supplies' => Supply::whereBetween('created_at', [$startDate, $endDate])
                ->where('created_by', auth()->id())
                ->count(),
            'total_supply_amount' => Supply::whereBetween('created_at', [$startDate, $endDate])
                ->where('created_by', auth()->id())
                ->sum('total_amount'),
            'sales_by_category' => Product::select(
                'categories.name',
                DB::raw('SUM(order_items.quantity * products.price) as total_amount'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count')
            )
                ->join('order_items', 'products.id', '=', 'order_items.product_id')
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                ->join('categories', 'products.category_id', '=', 'categories.id')
                ->whereBetween('orders.created_at', [$startDate, $endDate])
                ->groupBy('categories.id', 'categories.name')
                ->get()
        ];

        return response()->json($report);
    }
}
