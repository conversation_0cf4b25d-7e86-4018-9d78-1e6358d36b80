/**
 * Script de test de performance pour le dashboard professionnel optimisé
 */

class DashboardPerformanceTester {
    constructor() {
        this.metrics = {
            domContentLoaded: 0,
            windowLoaded: 0,
            firstPaint: 0,
            firstContentfulPaint: 0,
            largestContentfulPaint: 0,
            cumulativeLayoutShift: 0,
            firstInputDelay: 0,
            totalBlockingTime: 0,
            resourcesLoaded: 0,
            jsExecutionTime: 0,
            cssLoadTime: 0
        };
        
        this.startTime = performance.now();
        this.init();
    }

    init() {
        console.log('🚀 Démarrage du test de performance du dashboard');
        
        // Mesurer le temps de chargement du DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.metrics.domContentLoaded = performance.now() - this.startTime;
                console.log(`📊 DOM Content Loaded: ${this.metrics.domContentLoaded.toFixed(2)}ms`);
            });
        } else {
            this.metrics.domContentLoaded = 0; // Déjà chargé
        }

        // Mesurer le temps de chargement complet de la fenêtre
        if (document.readyState !== 'complete') {
            window.addEventListener('load', () => {
                this.metrics.windowLoaded = performance.now() - this.startTime;
                console.log(`🏁 Window Loaded: ${this.metrics.windowLoaded.toFixed(2)}ms`);
                this.measureWebVitals();
                this.measureResources();
                this.generateReport();
            });
        } else {
            this.metrics.windowLoaded = 0; // Déjà chargé
            setTimeout(() => {
                this.measureWebVitals();
                this.measureResources();
                this.generateReport();
            }, 100);
        }

        // Mesurer les Core Web Vitals
        this.measureCoreWebVitals();
    }

    measureCoreWebVitals() {
        // First Paint et First Contentful Paint
        if ('PerformanceObserver' in window) {
            const paintObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.name === 'first-paint') {
                        this.metrics.firstPaint = entry.startTime;
                        console.log(`🎨 First Paint: ${entry.startTime.toFixed(2)}ms`);
                    }
                    if (entry.name === 'first-contentful-paint') {
                        this.metrics.firstContentfulPaint = entry.startTime;
                        console.log(`🖼️ First Contentful Paint: ${entry.startTime.toFixed(2)}ms`);
                    }
                }
            });
            paintObserver.observe({ entryTypes: ['paint'] });

            // Largest Contentful Paint
            const lcpObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.largestContentfulPaint = lastEntry.startTime;
                console.log(`🖼️ Largest Contentful Paint: ${lastEntry.startTime.toFixed(2)}ms`);
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

            // Cumulative Layout Shift
            const clsObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (!entry.hadRecentInput) {
                        this.metrics.cumulativeLayoutShift += entry.value;
                    }
                }
                console.log(`📐 Cumulative Layout Shift: ${this.metrics.cumulativeLayoutShift.toFixed(4)}`);
            });
            clsObserver.observe({ entryTypes: ['layout-shift'] });

            // First Input Delay
            const fidObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
                    console.log(`⚡ First Input Delay: ${this.metrics.firstInputDelay.toFixed(2)}ms`);
                }
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
        }
    }

    measureWebVitals() {
        // Mesurer les métriques Web Vitals avec Navigation Timing API
        if ('performance' in window && 'getEntriesByType' in performance) {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                console.log('📈 Navigation Timing:');
                console.log(`  - DNS Lookup: ${(navigation.domainLookupEnd - navigation.domainLookupStart).toFixed(2)}ms`);
                console.log(`  - TCP Connection: ${(navigation.connectEnd - navigation.connectStart).toFixed(2)}ms`);
                console.log(`  - Request: ${(navigation.responseStart - navigation.requestStart).toFixed(2)}ms`);
                console.log(`  - Response: ${(navigation.responseEnd - navigation.responseStart).toFixed(2)}ms`);
                console.log(`  - DOM Processing: ${(navigation.domComplete - navigation.domLoading).toFixed(2)}ms`);
            }
        }
    }

    measureResources() {
        if ('performance' in window && 'getEntriesByType' in performance) {
            const resources = performance.getEntriesByType('resource');
            this.metrics.resourcesLoaded = resources.length;
            
            let totalCSSTime = 0;
            let totalJSTime = 0;
            
            resources.forEach(resource => {
                const duration = resource.responseEnd - resource.startTime;
                
                if (resource.name.includes('.css')) {
                    totalCSSTime += duration;
                } else if (resource.name.includes('.js')) {
                    totalJSTime += duration;
                }
            });
            
            this.metrics.cssLoadTime = totalCSSTime;
            this.metrics.jsExecutionTime = totalJSTime;
            
            console.log(`📦 Resources Loaded: ${this.metrics.resourcesLoaded}`);
            console.log(`🎨 Total CSS Load Time: ${totalCSSTime.toFixed(2)}ms`);
            console.log(`⚡ Total JS Load Time: ${totalJSTime.toFixed(2)}ms`);
        }
    }

    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            metrics: this.metrics,
            performance: this.getPerformanceGrade(),
            recommendations: this.getRecommendations()
        };

        console.log('📊 RAPPORT DE PERFORMANCE DASHBOARD');
        console.log('=====================================');
        console.log(`🕐 Timestamp: ${report.timestamp}`);
        console.log(`🌐 URL: ${report.url}`);
        console.log(`📱 User Agent: ${navigator.userAgent.substring(0, 50)}...`);
        console.log('');
        console.log('📈 MÉTRIQUES:');
        console.log(`  - DOM Content Loaded: ${this.metrics.domContentLoaded.toFixed(2)}ms`);
        console.log(`  - Window Loaded: ${this.metrics.windowLoaded.toFixed(2)}ms`);
        console.log(`  - First Paint: ${this.metrics.firstPaint.toFixed(2)}ms`);
        console.log(`  - First Contentful Paint: ${this.metrics.firstContentfulPaint.toFixed(2)}ms`);
        console.log(`  - Largest Contentful Paint: ${this.metrics.largestContentfulPaint.toFixed(2)}ms`);
        console.log(`  - Cumulative Layout Shift: ${this.metrics.cumulativeLayoutShift.toFixed(4)}`);
        console.log(`  - Resources Loaded: ${this.metrics.resourcesLoaded}`);
        console.log('');
        console.log(`🏆 GRADE DE PERFORMANCE: ${report.performance.grade}`);
        console.log(`📝 Score: ${report.performance.score}/100`);
        console.log('');
        console.log('💡 RECOMMANDATIONS:');
        report.recommendations.forEach((rec, index) => {
            console.log(`  ${index + 1}. ${rec}`);
        });

        // Stocker le rapport pour usage ultérieur
        window.dashboardPerformanceReport = report;
        
        // Afficher une notification de performance
        this.showPerformanceNotification(report.performance);
    }

    getPerformanceGrade() {
        let score = 100;
        
        // Pénalités basées sur les métriques
        if (this.metrics.firstContentfulPaint > 1800) score -= 20;
        else if (this.metrics.firstContentfulPaint > 1000) score -= 10;
        
        if (this.metrics.largestContentfulPaint > 2500) score -= 25;
        else if (this.metrics.largestContentfulPaint > 1500) score -= 15;
        
        if (this.metrics.cumulativeLayoutShift > 0.25) score -= 20;
        else if (this.metrics.cumulativeLayoutShift > 0.1) score -= 10;
        
        if (this.metrics.windowLoaded > 3000) score -= 15;
        else if (this.metrics.windowLoaded > 2000) score -= 8;
        
        let grade = 'A';
        if (score < 90) grade = 'B';
        if (score < 80) grade = 'C';
        if (score < 70) grade = 'D';
        if (score < 60) grade = 'F';
        
        return { score: Math.max(0, score), grade };
    }

    getRecommendations() {
        const recommendations = [];
        
        if (this.metrics.firstContentfulPaint > 1800) {
            recommendations.push('Optimiser le First Contentful Paint (>1.8s détecté)');
        }
        
        if (this.metrics.largestContentfulPaint > 2500) {
            recommendations.push('Réduire le Largest Contentful Paint (>2.5s détecté)');
        }
        
        if (this.metrics.cumulativeLayoutShift > 0.1) {
            recommendations.push('Minimiser le Cumulative Layout Shift pour une meilleure stabilité visuelle');
        }
        
        if (this.metrics.resourcesLoaded > 50) {
            recommendations.push('Considérer la réduction du nombre de ressources chargées');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('Excellente performance ! Continuez le bon travail.');
        }
        
        return recommendations;
    }

    showPerformanceNotification(performance) {
        if (typeof showNotification === 'function') {
            const message = `Performance Dashboard: ${performance.grade} (${performance.score}/100)`;
            const type = performance.score >= 90 ? 'success' : performance.score >= 70 ? 'warning' : 'error';
            showNotification(message, type);
        }
    }
}

// Initialiser le test de performance automatiquement
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new DashboardPerformanceTester();
    });
} else {
    new DashboardPerformanceTester();
}

// Export pour usage global
window.DashboardPerformanceTester = DashboardPerformanceTester;
