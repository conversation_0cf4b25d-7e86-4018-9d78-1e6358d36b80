<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\CementOrder;
use App\Models\Product;
use App\Models\Truck;

class OrderController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:customer']);
    }

    public function index()
    {
        $user = Auth::user();
        
        $orders = CementOrder::where('customer_id', $user->id)
            ->with(['product', 'truck', 'driver'])
            ->latest()
            ->paginate(10);

        return view('customer.orders.index', compact('orders'));
    }

    public function show(CementOrder $order)
    {
        // Vérifier que la commande appartient au client connecté
        if ($order->customer_id !== Auth::id()) {
            abort(403, 'Accès non autorisé à cette commande.');
        }

        $order->load(['product', 'truck', 'driver', 'destination']);
        
        return view('customer.orders.show', compact('order'));
    }

    public function create()
    {
        $products = Product::where('is_active', true)->get();
        $trucks = Truck::with('capacity')->whereHas('driver')->get();
        
        return view('customer.orders.create', compact('products', 'trucks'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|numeric|min:1',
            'truck_id' => 'required|exists:trucks,id',
            'delivery_address' => 'required|string|max:500',
            'delivery_date' => 'required|date|after:today',
            'notes' => 'nullable|string|max:1000',
        ]);

        $validated['customer_id'] = Auth::id();
        $validated['status'] = 'pending';
        $validated['order_date'] = now();

        $order = CementOrder::create($validated);

        return redirect()->route('customer.orders.show', $order)
            ->with('success', 'Commande créée avec succès.');
    }
}
