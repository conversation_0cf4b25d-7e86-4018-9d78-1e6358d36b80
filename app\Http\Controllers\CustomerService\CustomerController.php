<?php

namespace App\Http\Controllers\CustomerService;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\CementOrder;
use App\Models\Ticket;
use Illuminate\Support\Facades\Hash;

class CustomerController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:customer_service']);
    }

    public function index()
    {
        $customers = User::role('customer')
            ->with(['orders' => function($query) {
                $query->latest()->take(3);
            }])
            ->withCount(['orders', 'tickets'])
            ->latest()
            ->paginate(15);

        return view('customer-service.customers.index', compact('customers'));
    }

    public function show(User $customer)
    {
        // Vérifier que l'utilisateur a le rôle customer
        if (!$customer->hasRole('customer')) {
            abort(404, 'Client non trouvé.');
        }

        $customer->load([
            'orders' => function($query) {
                $query->with(['product', 'truck'])->latest();
            },
            'tickets' => function($query) {
                $query->latest();
            }
        ]);

        $stats = [
            'total_orders' => $customer->orders->count(),
            'pending_orders' => $customer->orders->where('status', 'pending')->count(),
            'completed_orders' => $customer->orders->where('status', 'completed')->count(),
            'open_tickets' => $customer->tickets->where('status', 'open')->count(),
        ];

        return view('customer-service.customers.show', compact('customer', 'stats'));
    }

    public function create()
    {
        return view('customer-service.customers.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'required|string|max:255',
            'address' => 'nullable|string|max:500',
        ]);

        $validated['password'] = Hash::make('password123');
        $validated['is_active'] = true;

        $customer = User::create($validated);
        $customer->assignRole('customer');

        return redirect()->route('customer-service.customers.show', $customer)
            ->with('success', 'Client créé avec succès. Mot de passe par défaut: password123');
    }

    public function edit(User $customer)
    {
        if (!$customer->hasRole('customer')) {
            abort(404, 'Client non trouvé.');
        }

        return view('customer-service.customers.edit', compact('customer'));
    }

    public function update(Request $request, User $customer)
    {
        if (!$customer->hasRole('customer')) {
            abort(404, 'Client non trouvé.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $customer->id,
            'phone' => 'required|string|max:255',
            'address' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $customer->update($validated);

        return redirect()->route('customer-service.customers.show', $customer)
            ->with('success', 'Client mis à jour avec succès.');
    }
}
