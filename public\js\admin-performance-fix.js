/**
 * GRADIS Admin - Correctif de Performance
 * Script léger pour éliminer les blocages de 40 secondes
 * Version: 1.0 - Correctif immédiat
 */

console.log('🚀 GRADIS Admin - Correctif de performance chargé');

/**
 * Fonction principale pour éliminer les blocages
 */
function fixPerformanceIssues() {
    console.log('⚡ Application du correctif de performance...');
    
    // 1. Arrêter tous les intervals existants qui pourraient causer des blocages
    stopProblematicIntervals();
    
    // 2. Nettoyer les timeouts longs
    cleanupLongTimeouts();
    
    // 3. Optimiser les requêtes en cours
    optimizeOngoingRequests();
    
    // 4. Désactiver les auto-refresh problématiques
    disableProblematicAutoRefresh();
    
    console.log('✅ Correctif de performance appliqué avec succès');
}

/**
 * Arrêter les intervals problématiques
 */
function stopProblematicIntervals() {
    // Intercepter les nouveaux setInterval pour éviter les intervals courts
    const originalSetInterval = window.setInterval;
    window.setInterval = function(callback, delay, ...args) {
        // Bloquer seulement les intervals très courts (< 1000ms) qui peuvent causer des blocages
        if (delay < 1000) {
            console.warn('⚠️ setInterval court bloqué pour performance:', delay, 'ms');
            return null;
        }

        // Permettre les autres intervals (pour ApexCharts et autres fonctionnalités)
        return originalSetInterval.call(this, callback, delay, ...args);
    };

    console.log('❌ Intervals courts bloqués (graphiques préservés)');
}

/**
 * Nettoyer les timeouts longs
 */
function cleanupLongTimeouts() {
    // Intercepter les nouveaux setTimeout pour éviter les longs délais
    const originalSetTimeout = window.setTimeout;
    window.setTimeout = function(callback, delay, ...args) {
        // Permettre les timeouts courts pour les graphiques (< 5 secondes)
        if (delay <= 5000) {
            return originalSetTimeout.call(this, callback, delay, ...args);
        }

        // Limiter les délais très longs à maximum 10 secondes
        if (delay > 10000) {
            console.warn('⚠️ setTimeout long détecté et limité:', delay, 'ms → 10000ms');
            delay = 10000;
        }
        return originalSetTimeout.call(this, callback, delay, ...args);
    };

    console.log('❌ Timeouts longs nettoyés (graphiques préservés)');
}

/**
 * Optimiser les requêtes en cours
 */
function optimizeOngoingRequests() {
    // Intercepter fetch pour ajouter des timeouts
    if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            // Ajouter un timeout de 15 secondes maximum
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
                console.warn('⚠️ Requête annulée (timeout):', url);
            }, 15000);
            
            options.signal = controller.signal;
            
            return originalFetch(url, options)
                .finally(() => clearTimeout(timeoutId));
        };
    }
    
    console.log('❌ Requêtes optimisées avec timeout');
}

/**
 * Désactiver les auto-refresh problématiques
 */
function disableProblematicAutoRefresh() {
    // Désactiver les fonctions d'auto-refresh connues
    if (window.startStockAutoRefresh) {
        window.startStockAutoRefresh = function() {
            console.log('❌ Auto-refresh des stocks désactivé (correctif performance)');
            return false;
        };
    }
    
    if (window.refreshStockData) {
        const originalRefresh = window.refreshStockData;
        window.refreshStockData = function() {
            console.log('⚡ Refresh manuel des stocks (optimisé)');
            // Appeler la fonction originale mais sans auto-refresh
            if (originalRefresh && typeof originalRefresh === 'function') {
                try {
                    originalRefresh();
                } catch (e) {
                    console.warn('⚠️ Erreur lors du refresh manuel:', e.message);
                }
            }
        };
    }
    
    console.log('❌ Auto-refresh problématique désactivé');
}

/**
 * Surveiller et corriger les problèmes de performance en temps réel
 */
function startPerformanceMonitoring() {
    // Surveiller les performances toutes les 5 secondes
    setInterval(() => {
        // Vérifier s'il y a trop de timers actifs
        const testInterval = setInterval(() => {}, 1);
        clearInterval(testInterval);
        
        if (testInterval > 1000) {
            console.warn('⚠️ Trop de timers détectés, nettoyage en cours...');
            stopProblematicIntervals();
        }
    }, 5000);
    
    console.log('👁️ Surveillance de performance activée');
}

/**
 * Créer un bouton d'urgence pour forcer l'optimisation
 */
function createEmergencyOptimizeButton() {
    const button = document.createElement('button');
    button.innerHTML = '⚡ Optimiser';
    button.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    `;
    
    button.addEventListener('click', function() {
        fixPerformanceIssues();
        this.innerHTML = '✅ Optimisé';
        this.style.background = '#007bff';
        
        setTimeout(() => {
            this.style.opacity = '0.5';
        }, 2000);
    });
    
    document.body.appendChild(button);
    
    // Masquer le bouton après 30 secondes
    setTimeout(() => {
        if (button.parentNode) {
            button.style.transition = 'opacity 1s ease';
            button.style.opacity = '0.3';
        }
    }, 30000);
}

/**
 * Initialisation automatique
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initialisation du correctif de performance...');
    
    // Appliquer le correctif immédiatement
    setTimeout(() => {
        fixPerformanceIssues();
        startPerformanceMonitoring();
        createEmergencyOptimizeButton();
        
        console.log('🎉 Correctif de performance complètement activé!');
    }, 100);
});

// Appliquer le correctif même si le DOM n'est pas encore prêt
if (document.readyState !== 'loading') {
    fixPerformanceIssues();
}

// Export global pour utilisation manuelle
window.AdminPerformanceFix = {
    fix: fixPerformanceIssues,
    stopIntervals: stopProblematicIntervals,
    optimizeRequests: optimizeOngoingRequests
};

console.log('⚡ Script de correctif de performance prêt');
