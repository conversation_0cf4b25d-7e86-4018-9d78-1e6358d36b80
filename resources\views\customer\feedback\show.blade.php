@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- En-tête -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">📝 Détails du Feedback</h2>
                    <p class="text-muted mb-0">Feedback #{{ $feedback->id }}</p>
                </div>
                <a href="{{ route('customer.feedback.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>

            <!-- Informations principales -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{{ $feedback->subject }}</h5>
                        <div>
                            <span class="badge bg-{{ $feedback->status_badge }} me-2">
                                {{ ucfirst($feedback->status) }}
                            </span>
                            <span class="badge bg-{{ $feedback->priority_badge }}">
                                {{ ucfirst($feedback->priority) }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Type:</strong> 
                            <span class="badge bg-light text-dark">{{ ucfirst($feedback->type) }}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>Catégorie:</strong> 
                            <span class="badge bg-light text-dark">{{ ucfirst($feedback->category) }}</span>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Date de création:</strong> {{ $feedback->created_at->format('d/m/Y à H:i') }}
                        </div>
                        <div class="col-md-6">
                            <strong>Anonyme:</strong> {{ $feedback->is_anonymous ? 'Oui' : 'Non' }}
                        </div>
                    </div>

                    @if($feedback->cementOrderDetail)
                    <div class="mb-3">
                        <strong>Commande associée:</strong>
                        <div class="mt-2 p-3 bg-light rounded">
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>Numéro:</strong> #{{ $feedback->cementOrderDetail->id }}
                                </div>
                                <div class="col-md-4">
                                    <strong>Produit:</strong> {{ $feedback->cementOrderDetail->cementOrder->product->name ?? 'N/A' }}
                                </div>
                                <div class="col-md-4">
                                    <strong>Date:</strong> {{ $feedback->cementOrderDetail->created_at->format('d/m/Y') }}
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <div class="mb-3">
                        <strong>Message:</strong>
                        <div class="mt-2 p-3 bg-light rounded">
                            {{ $feedback->message }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Évaluations -->
            @if($feedback->overall_rating || $feedback->delivery_rating || $feedback->service_rating || $feedback->product_rating)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">⭐ Évaluations</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if($feedback->overall_rating)
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Note globale:</strong>
                                <div class="rating-display">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $feedback->overall_rating ? 'text-warning' : 'text-muted' }}"></i>
                                    @endfor
                                    <span class="ms-2">{{ $feedback->overall_rating }}/5</span>
                                </div>
                            </div>
                        </div>
                        @endif

                        @if($feedback->delivery_rating)
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Livraison:</strong>
                                <div class="rating-display">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $feedback->delivery_rating ? 'text-warning' : 'text-muted' }}"></i>
                                    @endfor
                                    <span class="ms-2">{{ $feedback->delivery_rating }}/5</span>
                                </div>
                            </div>
                        </div>
                        @endif

                        @if($feedback->service_rating)
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Service:</strong>
                                <div class="rating-display">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $feedback->service_rating ? 'text-warning' : 'text-muted' }}"></i>
                                    @endfor
                                    <span class="ms-2">{{ $feedback->service_rating }}/5</span>
                                </div>
                            </div>
                        </div>
                        @endif

                        @if($feedback->product_rating)
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Produit:</strong>
                                <div class="rating-display">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $feedback->product_rating ? 'text-warning' : 'text-muted' }}"></i>
                                    @endfor
                                    <span class="ms-2">{{ $feedback->product_rating }}/5</span>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif

            <!-- Informations de livraison -->
            @if($feedback->driver_name || $feedback->truck_number || $feedback->delivery_date)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">🚚 Informations de Livraison</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if($feedback->driver_name)
                        <div class="col-md-4 mb-2">
                            <strong>Chauffeur:</strong> {{ $feedback->driver_name }}
                        </div>
                        @endif
                        @if($feedback->truck_number)
                        <div class="col-md-4 mb-2">
                            <strong>Camion:</strong> {{ $feedback->truck_number }}
                        </div>
                        @endif
                        @if($feedback->delivery_date)
                        <div class="col-md-4 mb-2">
                            <strong>Date:</strong> {{ $feedback->delivery_date->format('d/m/Y') }}
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif

            <!-- Réponse de l'administration -->
            @if($feedback->admin_response)
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">💬 Réponse de notre équipe</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        {{ $feedback->admin_response }}
                    </div>
                    <div class="text-muted small">
                        <strong>Répondu par:</strong> {{ $feedback->respondedBy->name ?? 'Équipe GRADIS' }} 
                        le {{ $feedback->responded_at->format('d/m/Y à H:i') }}
                    </div>
                </div>
            </div>
            @else
            <div class="card mb-4">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-muted mb-3"></i>
                    <h6>En attente de réponse</h6>
                    <p class="text-muted mb-0">Notre équipe examine votre feedback et vous répondra dans les plus brefs délais.</p>
                </div>
            </div>
            @endif

            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Besoin d'ajouter des informations ?</h6>
                            <p class="text-muted mb-0 small">Vous pouvez créer un nouveau feedback pour compléter celui-ci.</p>
                        </div>
                        <a href="{{ route('customer.feedback.create', ['order_id' => $feedback->cement_order_detail_id]) }}" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>Nouveau Feedback
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .rating-display {
        display: inline-flex;
        align-items: center;
        gap: 2px;
    }
    .rating-display .fa-star {
        font-size: 16px;
    }
</style>
@endpush
@endsection
