<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="test-token">
    
    <!-- Permissions Policy pour éviter les erreurs en développement -->
    <meta http-equiv="Permissions-Policy" content="unload=*, payment=*, geolocation=*">
    
    <title>Test - Correction Méthode de Paiement Requise</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2196F3;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --danger-color: #F44336;
            --info-color: #00BCD4;
        }
        
        .payment-card {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .payment-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
        }
        
        .payment-card.selected {
            border-color: var(--primary-color);
            background: #e3f2fd;
        }
        
        .payment-card.border-danger {
            border-color: var(--danger-color) !important;
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .reference-field {
            margin-top: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
        }
        
        .payment-method-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .debug-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ok { background-color: var(--success-color); }
        .status-error { background-color: var(--danger-color); }
        .status-warning { background-color: var(--warning-color); }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-bug me-2"></i>Test - Correction "Payment Method Field Required"</h4>
                    </div>
                    <div class="card-body">
                        <form id="paymentForm" action="#" method="POST">
                            <input type="hidden" name="_token" value="test-token">
                            
                            <!-- Montant -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="amount" class="form-label">
                                        <i class="fas fa-coins text-primary me-2"></i>
                                        Montant à encaisser <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="amount" name="amount" 
                                               value="100000" min="1" required>
                                        <span class="input-group-text">FCFA</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="payment_date" class="form-label">
                                        <i class="fas fa-calendar text-primary me-2"></i>
                                        Date du paiement <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="payment_date" name="payment_date" 
                                           value="2025-01-03" required>
                                </div>
                            </div>
                            
                            <!-- Méthodes de paiement -->
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-credit-card text-primary me-2"></i>
                                    Méthode de paiement <span class="text-danger">*</span>
                                </label>
                                
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="payment-card text-center p-3 rounded selected" data-method="cash">
                                            <i class="fas fa-money-bill-wave payment-method-icon text-success"></i>
                                            <h6 class="mb-0 fw-medium">Espèces</h6>
                                            <input type="radio" name="payment_method" value="cash" class="d-none" checked>
                                        </div>
                                    </div>
                                    
                                    <div class="col-6">
                                        <div class="payment-card text-center p-3 rounded" data-method="bank_transfer">
                                            <i class="fas fa-university payment-method-icon text-primary"></i>
                                            <h6 class="mb-0 fw-medium">Virement</h6>
                                            <input type="radio" name="payment_method" value="bank_transfer" class="d-none">
                                        </div>
                                    </div>
                                    
                                    <div class="col-6">
                                        <div class="payment-card text-center p-3 rounded" data-method="check">
                                            <i class="fas fa-money-check-alt payment-method-icon text-info"></i>
                                            <h6 class="mb-0 fw-medium">Chèque</h6>
                                            <input type="radio" name="payment_method" value="check" class="d-none">
                                        </div>
                                    </div>
                                    
                                    <div class="col-6">
                                        <div class="payment-card text-center p-3 rounded" data-method="mobile_money">
                                            <i class="fas fa-mobile-alt payment-method-icon text-warning"></i>
                                            <h6 class="mb-0 fw-medium">Mobile Money</h6>
                                            <input type="radio" name="payment_method" value="mobile_money" class="d-none">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Informations sur la méthode de paiement -->
                                <div id="paymentMethodInfo" class="alert alert-info mt-3" style="border-radius: 12px; border-left: 4px solid var(--info-color);">
                                    <h6 class="alert-heading d-flex align-items-center">
                                        <i class="fas fa-money-bill-wave me-2"></i>
                                        <span>Paiement en espèces</span>
                                    </h6>
                                    <p class="mb-0">Assurez-vous de compter correctement les espèces et de délivrer un reçu au client.</p>
                                </div>
                            </div>
                            
                            <!-- Champ de référence -->
                            <div class="reference-field" style="display: none;">
                                <label for="reference_number" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>
                                    Numéro de référence *
                                </label>
                                <input type="text" class="form-control" id="reference_number" name="reference_number" 
                                       placeholder="Saisissez le numéro de référence">
                                <small class="form-text text-muted">
                                    Ce champ est requis pour les paiements par virement, chèque ou mobile money.
                                </small>
                            </div>
                            
                            <!-- Boutons d'action -->
                            <div class="mt-4">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-save me-2"></i>Enregistrer le paiement
                                </button>
                                <button type="button" class="btn btn-info" onclick="debugPaymentSelection()">
                                    <i class="fas fa-bug me-2"></i>Debug
                                </button>
                                <button type="button" class="btn btn-warning" onclick="simulateError()">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Simuler Erreur
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>Reset
                                </button>
                            </div>
                        </form>
                        
                        <!-- Panel de debug -->
                        <div class="debug-panel">
                            <h6><i class="fas fa-cogs me-2"></i>État du Formulaire</h6>
                            <div id="debugInfo">
                                <div><span class="status-indicator status-ok"></span>Formulaire initialisé</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Variables globales
        let debugInterval;
        
        // Gestionnaire d'erreurs global
        window.addEventListener('error', function(e) {
            console.warn('Erreur JavaScript interceptée:', e.error);
            return false;
        });
        
        document.addEventListener('DOMContentLoaded', function() {
            const paymentCards = document.querySelectorAll('.payment-card');
            const paymentMethodInfo = document.getElementById('paymentMethodInfo');
            
            // Fonction pour gérer l'affichage du champ de référence
            function toggleReferenceField(method) {
                const referenceField = document.querySelector('.reference-field');
                const referenceInput = document.getElementById('reference_number');
                
                if (method === 'cash') {
                    if (referenceField) {
                        referenceField.style.display = 'none';
                    }
                    if (referenceInput) {
                        referenceInput.removeAttribute('required');
                        referenceInput.value = '';
                    }
                } else {
                    if (referenceField) {
                        referenceField.style.display = 'block';
                    }
                    if (referenceInput) {
                        referenceInput.setAttribute('required', 'required');
                    }
                }
            }
            
            // Fonction pour mettre à jour les informations sur la méthode de paiement
            function updatePaymentMethodInfo(method) {
                let infoContent = '';
                let title = '';
                let iconClass = 'fas fa-info-circle';
                
                switch(method) {
                    case 'cash':
                        iconClass = 'fas fa-money-bill-wave';
                        title = 'Paiement en espèces';
                        infoContent = 'Assurez-vous de compter correctement les espèces et de délivrer un reçu au client.';
                        break;
                    case 'bank_transfer':
                        iconClass = 'fas fa-university';
                        title = 'Paiement par virement';
                        infoContent = 'Veuillez saisir le numéro de référence du virement dans le champ prévu à cet effet.';
                        break;
                    case 'check':
                        iconClass = 'fas fa-money-check-alt';
                        title = 'Paiement par chèque';
                        infoContent = 'N\'oubliez pas de noter le numéro du chèque dans le champ de référence.';
                        break;
                    case 'mobile_money':
                        iconClass = 'fas fa-mobile-alt';
                        title = 'Paiement par Mobile Money';
                        infoContent = 'Veuillez saisir le numéro de transaction Mobile Money dans le champ de référence.';
                        break;
                }
                
                paymentMethodInfo.innerHTML = `
                    <h6 class="alert-heading d-flex align-items-center">
                        <i class="${iconClass} me-2"></i>
                        <span>${title}</span>
                    </h6>
                    <p class="mb-0">${infoContent}</p>
                `;
            }
            
            // Attacher les gestionnaires d'événements
            paymentCards.forEach(card => {
                card.addEventListener('click', function() {
                    try {
                        console.log('Clic sur carte de paiement:', this.dataset.method);
                        
                        // Supprimer la classe selected de toutes les cartes
                        paymentCards.forEach(c => {
                            c.classList.remove('selected');
                            // Décocher tous les radio buttons
                            const radio = c.querySelector('input[type="radio"]');
                            if (radio) {
                                radio.checked = false;
                            }
                        });
                        
                        // Ajouter la classe selected à la carte cliquée
                        this.classList.add('selected');
                        
                        // Sélectionner le radio button correspondant
                        const radio = this.querySelector('input[type="radio"]');
                        if (radio) {
                            radio.checked = true;
                            console.log('Radio button sélectionné:', radio.value);
                            
                            // Déclencher l'événement change
                            radio.dispatchEvent(new Event('change', { bubbles: true }));
                        }
                        
                        // Mettre à jour l'info du mode de paiement
                        const method = this.dataset.method;
                        if (method) {
                            updatePaymentMethodInfo(method);
                            toggleReferenceField(method);
                            console.log('Méthode de paiement sélectionnée:', method);
                        }
                        
                        updateDebugInfo();
                    } catch (error) {
                        console.error('Erreur lors de la sélection du mode de paiement:', error);
                    }
                });
            });
            
            // Validation du formulaire
            document.getElementById('paymentForm').addEventListener('submit', function(e) {
                e.preventDefault(); // Empêcher la soumission pour le test
                
                // Supprimer les anciennes alertes
                document.querySelectorAll('.alert-danger').forEach(alert => alert.remove());
                
                let method = document.querySelector('input[name="payment_method"]:checked');
                let reference = document.getElementById('reference_number').value;
                let hasErrors = false;
                
                // Vérifier si une méthode de paiement est sélectionnée
                if (!method) {
                    hasErrors = true;
                    
                    let alertHtml = `
                        <div class="alert alert-danger" style="border-radius: 12px; border-left: 4px solid var(--danger-color);">
                            <h6 class="alert-heading d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span>Méthode de paiement requise</span>
                            </h6>
                            <p class="mb-0">Veuillez sélectionner une méthode de paiement.</p>
                        </div>
                    `;
                    
                    this.insertAdjacentHTML('beforebegin', alertHtml);
                    
                    // Mettre en évidence les cartes de paiement
                    paymentCards.forEach(card => card.classList.add('border-danger'));
                    setTimeout(() => {
                        paymentCards.forEach(card => card.classList.remove('border-danger'));
                    }, 3000);
                } else {
                    // Vérifier si une référence est requise
                    const methodValue = method.value;
                    if ((methodValue === 'bank_transfer' || methodValue === 'check' || methodValue === 'mobile_money') && reference === '') {
                        hasErrors = true;
                        
                        let alertHtml = `
                            <div class="alert alert-danger" style="border-radius: 12px; border-left: 4px solid var(--danger-color);">
                                <h6 class="alert-heading d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <span>Référence requise</span>
                                </h6>
                                <p class="mb-0">Veuillez saisir un numéro de référence pour ce mode de paiement.</p>
                            </div>
                        `;
                        
                        this.insertAdjacentHTML('beforebegin', alertHtml);
                        document.getElementById('reference_number').focus();
                    }
                }
                
                if (!hasErrors) {
                    let successHtml = `
                        <div class="alert alert-success" style="border-radius: 12px; border-left: 4px solid var(--success-color);">
                            <h6 class="alert-heading d-flex align-items-center">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>Validation réussie</span>
                            </h6>
                            <p class="mb-0">Le formulaire est valide et peut être soumis. Méthode: ${method.value}</p>
                        </div>
                    `;
                    
                    this.insertAdjacentHTML('beforebegin', successHtml);
                }
                
                // Supprimer les alertes après 5 secondes
                setTimeout(() => {
                    document.querySelectorAll('.alert-danger, .alert-success').forEach(alert => {
                        alert.style.opacity = '0';
                        setTimeout(() => alert.remove(), 300);
                    });
                }, 5000);
                
                updateDebugInfo();
            });
            
            // Initialiser le debug
            updateDebugInfo();
            
            // Démarrer le monitoring automatique
            debugInterval = setInterval(updateDebugInfo, 2000);
        });
        
        // Fonction de debug accessible depuis la console
        window.debugPaymentSelection = function() {
            const cards = document.querySelectorAll('.payment-card');
            const radios = document.querySelectorAll('input[name="payment_method"]');
            const checkedRadio = document.querySelector('input[name="payment_method"]:checked');
            const selectedCard = document.querySelector('.payment-card.selected');
            
            console.log('=== DEBUG PAYMENT SELECTION ===');
            console.log('Cartes trouvées:', cards.length);
            console.log('Radio buttons trouvés:', radios.length);
            console.log('Radio button coché:', checkedRadio ? checkedRadio.value : 'aucun');
            console.log('Carte sélectionnée:', selectedCard ? selectedCard.dataset.method : 'aucune');
            
            radios.forEach((radio, index) => {
                console.log(`Radio ${index}:`, {
                    value: radio.value,
                    checked: radio.checked,
                    visible: radio.style.display !== 'none'
                });
            });
            
            cards.forEach((card, index) => {
                console.log(`Carte ${index}:`, {
                    method: card.dataset.method,
                    selected: card.classList.contains('selected'),
                    hasRadio: card.querySelector('input[type="radio"]') ? true : false
                });
            });
        };
        
        // Fonction pour mettre à jour les informations de debug
        function updateDebugInfo() {
            const checkedRadio = document.querySelector('input[name="payment_method"]:checked');
            const selectedCard = document.querySelector('.payment-card.selected');
            const referenceField = document.querySelector('.reference-field');
            const debugInfo = document.getElementById('debugInfo');
            
            let html = '';
            
            // État de la sélection
            if (checkedRadio && selectedCard) {
                html += '<div><span class="status-indicator status-ok"></span>Méthode de paiement sélectionnée: ' + checkedRadio.value + '</div>';
            } else {
                html += '<div><span class="status-indicator status-error"></span>Aucune méthode de paiement sélectionnée</div>';
            }
            
            // État du champ de référence
            if (referenceField) {
                const isVisible = referenceField.style.display !== 'none';
                html += '<div><span class="status-indicator ' + (isVisible ? 'status-warning' : 'status-ok') + '"></span>Champ de référence: ' + (isVisible ? 'visible' : 'masqué') + '</div>';
            }
            
            // Timestamp
            html += '<div><span class="status-indicator status-ok"></span>Dernière mise à jour: ' + new Date().toLocaleTimeString() + '</div>';
            
            debugInfo.innerHTML = html;
        }
        
        // Fonction pour simuler une erreur
        function simulateError() {
            // Décocher tous les radio buttons
            document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
                radio.checked = false;
            });
            
            // Supprimer la classe selected
            document.querySelectorAll('.payment-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            updateDebugInfo();
            console.log('Erreur simulée - aucune méthode de paiement sélectionnée');
        }
        
        // Fonction pour reset le formulaire
        function resetForm() {
            // Sélectionner la première carte par défaut
            const firstCard = document.querySelector('.payment-card[data-method="cash"]');
            if (firstCard) {
                firstCard.click();
            }
            
            // Vider le champ de référence
            document.getElementById('reference_number').value = '';
            
            // Supprimer les alertes
            document.querySelectorAll('.alert-danger, .alert-success').forEach(alert => alert.remove());
            
            updateDebugInfo();
            console.log('Formulaire réinitialisé');
        }
    </script>
</body>
</html>
