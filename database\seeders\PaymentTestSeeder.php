<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Sale;
use App\Models\Payment;
use App\Models\User;
use Carbon\Carbon;

class PaymentTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔄 Création de paiements de test...');

        // Récupérer ou créer des utilisateurs de test
        $cashier = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Caissier Test',
                'password' => bcrypt('password'),
                'position' => 'Caissier'
            ]
        );

        // Assigner le rôle si l'utilisateur vient d'être créé
        if (!$cashier->hasRole('cashier')) {
            $cashier->assignRole('cashier');
        }

        $accountant = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Comptable Test',
                'password' => bcrypt('password'),
                'position' => 'Comptable'
            ]
        );

        // Assigner le rôle si l'utilisateur vient d'être créé
        if (!$accountant->hasRole('accountant')) {
            $accountant->assignRole('accountant');
        }

        // Récupérer ou créer une vente de test
        $sale = Sale::first();
        if (!$sale) {
            $sale = Sale::create([
                'supply_id' => 1,
                'city_id' => 1,
                'customer_name' => 'Client Test',
                'customer_phone' => '*********',
                'customer_email' => '<EMAIL>',
                'customer_address' => 'Adresse test',
                'quantity' => 10,
                'unit_price' => 15000,
                'total_amount' => 150000,
                'amount_paid' => 0,
                'payment_method' => 'credit',
                'payment_status' => 'unpaid',
                'status' => 'pending_payment',
                'created_by' => 1,
                'invoice_number' => 'INV-TEST-001'
            ]);
        }

        // Créer des paiements de test
        $payments = [
            [
                'reference' => 'RGMTCAISSIER000001',
                'sale_id' => $sale->id,
                'cashier_id' => $cashier->id,
                'position' => 'Caissier',
                'amount' => 50000,
                'payment_method' => 'cash',
                'reference_number' => null,
                'receipt_number' => 'REC-' . date('Ymd') . '-ABC123',
                'notes' => 'Paiement partiel en espèces par le caissier',
                'payment_date' => Carbon::now()->subDays(5),
                'status' => 'completed'
            ],
            [
                'reference' => 'RGMTCOMPTABLE000002',
                'sale_id' => $sale->id,
                'cashier_id' => $accountant->id,
                'position' => 'Comptable',
                'amount' => 75000,
                'payment_method' => 'bank_transfer',
                'reference_number' => 'VIR*********',
                'receipt_number' => 'REC-' . date('Ymd') . '-DEF456',
                'notes' => 'Paiement par virement bancaire traité par le comptable',
                'payment_date' => Carbon::now()->subDays(2),
                'status' => 'completed'
            ],
            [
                'reference' => 'RGMTCAISSIER000003',
                'sale_id' => $sale->id,
                'cashier_id' => $cashier->id,
                'position' => 'Caissier',
                'amount' => 25000,
                'payment_method' => 'mobile_money',
                'reference_number' => 'MM987654321',
                'receipt_number' => 'REC-' . date('Ymd') . '-GHI789',
                'notes' => 'Solde payé via Mobile Money',
                'payment_date' => Carbon::now()->subDays(1),
                'status' => 'completed'
            ]
        ];

        foreach ($payments as $paymentData) {
            $payment = Payment::create($paymentData);
            $this->command->info("✅ Paiement créé: {$payment->reference} - {$payment->amount} FCFA");
        }

        // Mettre à jour le montant payé de la vente
        $totalPaid = Payment::where('sale_id', $sale->id)->sum('amount');
        $sale->update([
            'amount_paid' => $totalPaid,
            'payment_status' => $totalPaid >= $sale->total_amount ? 'paid' : 'partial'
        ]);

        $this->command->info("✅ Vente mise à jour: {$sale->invoice_number} - Payé: {$totalPaid} FCFA / {$sale->total_amount} FCFA");
        $this->command->info('🎉 Paiements de test créés avec succès !');
    }
}
