/**
 * Tracker de performance ultra-léger pour le dashboard
 */
class DashboardPerformanceTracker {
    constructor() {
        this.startTime = performance.now();
        this.metrics = {
            domContentLoaded: null,
            windowLoaded: null,
            firstPaint: null,
            firstContentfulPaint: null,
            largestContentfulPaint: null,
            totalLoadTime: null
        };
        
        this.init();
    }

    init() {
        // Mesurer le DOM Content Loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.metrics.domContentLoaded = performance.now() - this.startTime;
                this.updateDisplay();
            });
        } else {
            this.metrics.domContentLoaded = 0;
        }

        // Mesurer le chargement complet
        window.addEventListener('load', () => {
            this.metrics.windowLoaded = performance.now() - this.startTime;
            this.metrics.totalLoadTime = this.metrics.windowLoaded;
            this.updateDisplay();
            this.measurePaintMetrics();
        });

        // Créer l'affichage des métriques
        this.createDisplay();
    }

    measurePaintMetrics() {
        // Utiliser l'API Performance Observer si disponible
        if ('PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.name === 'first-paint') {
                            this.metrics.firstPaint = entry.startTime;
                        } else if (entry.name === 'first-contentful-paint') {
                            this.metrics.firstContentfulPaint = entry.startTime;
                        } else if (entry.entryType === 'largest-contentful-paint') {
                            this.metrics.largestContentfulPaint = entry.startTime;
                        }
                    }
                    this.updateDisplay();
                });

                observer.observe({ entryTypes: ['paint', 'largest-contentful-paint'] });
            } catch (e) {
                console.log('Performance Observer non supporté');
            }
        }
    }

    createDisplay() {
        // Créer un petit widget de performance en bas à droite
        const widget = document.createElement('div');
        widget.id = 'performance-widget';
        widget.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            min-width: 200px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: opacity 0.3s ease;
        `;

        widget.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px; color: #4CAF50;">
                📊 Performance Dashboard
            </div>
            <div id="perf-metrics">
                <div>⏱️ Chargement: <span id="load-time">Mesure...</span></div>
                <div>🎨 Premier rendu: <span id="first-paint">Mesure...</span></div>
                <div>📄 Contenu: <span id="first-content">Mesure...</span></div>
                <div>🚀 Status: <span id="perf-status" style="color: #FFC107;">En cours...</span></div>
            </div>
            <div style="margin-top: 8px; font-size: 10px; opacity: 0.7;">
                Cliquez pour masquer
            </div>
        `;

        // Permettre de masquer le widget
        widget.addEventListener('click', () => {
            widget.style.opacity = widget.style.opacity === '0.3' ? '1' : '0.3';
        });

        document.body.appendChild(widget);
    }

    updateDisplay() {
        const loadTimeEl = document.getElementById('load-time');
        const firstPaintEl = document.getElementById('first-paint');
        const firstContentEl = document.getElementById('first-content');
        const statusEl = document.getElementById('perf-status');

        if (loadTimeEl && this.metrics.totalLoadTime !== null) {
            const loadTime = Math.round(this.metrics.totalLoadTime);
            loadTimeEl.textContent = `${loadTime}ms`;
            
            // Déterminer le statut de performance
            let status, color;
            if (loadTime < 1000) {
                status = 'Excellent 🚀';
                color = '#4CAF50';
            } else if (loadTime < 3000) {
                status = 'Bon ✅';
                color = '#8BC34A';
            } else if (loadTime < 5000) {
                status = 'Moyen ⚠️';
                color = '#FFC107';
            } else {
                status = 'Lent 🐌';
                color = '#F44336';
            }
            
            if (statusEl) {
                statusEl.textContent = status;
                statusEl.style.color = color;
            }
        }

        if (firstPaintEl && this.metrics.firstPaint !== null) {
            firstPaintEl.textContent = `${Math.round(this.metrics.firstPaint)}ms`;
        }

        if (firstContentEl && this.metrics.firstContentfulPaint !== null) {
            firstContentEl.textContent = `${Math.round(this.metrics.firstContentfulPaint)}ms`;
        }
    }

    // Méthode pour obtenir un rapport complet
    getReport() {
        return {
            ...this.metrics,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
    }

    // Méthode pour envoyer les métriques au serveur (optionnel)
    sendMetrics() {
        if (this.metrics.totalLoadTime !== null) {
            const report = this.getReport();
            console.log('📊 Rapport de performance:', report);
            
            // Optionnel: envoyer au serveur
            // fetch('/api/performance-metrics', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(report)
            // });
        }
    }
}

// Initialiser le tracker automatiquement
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.performanceTracker = new DashboardPerformanceTracker();
    });
} else {
    window.performanceTracker = new DashboardPerformanceTracker();
}

// Auto-suppression du widget après 10 secondes
setTimeout(() => {
    const widget = document.getElementById('performance-widget');
    if (widget) {
        widget.style.transition = 'opacity 1s ease';
        widget.style.opacity = '0.3';
    }
}, 10000);
