<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Supply;
use App\Models\Product;
use App\Models\Supplier;
use App\Services\StockService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class SupplyController extends Controller
{
    public function index()
    {
        try {
            // Créer la query de base avec les relations
            $supplies = Supply::with(['supplier', 'details.product', 'createdBy', 'cities'])
                ->latest()
                ->paginate(10);
            
            // Calculer le montant total réel pour chaque approvisionnement
            $supplies->each(function ($supply) {
                $supply->real_total_amount = $supply->cities->sum(function($city) {
                    return $city->quantity * $city->price;
                });
            });
            
            return view('admin.supplies.index', compact('supplies'));
        } catch (\Exception $e) {
            // OPTIMISATION: Log seulement les erreurs critiques
            // Erreur
        }
    }

    public function show(Supply $supply)
    {
        try {
            // Chargement des détails de l'approvisionnement
            
            $supply->load([
                'supplier',
                'details.product.category',
                'cities.city',
                'cities.vehicle.capacity',
                'cities.driver',
                'createdBy',
                'validator'
            ]);
            
            // Calculer le montant total réel
            $realTotalAmount = $supply->cities->sum(function($city) {
                return $city->quantity * $city->price;
            });
            $supply->real_total_amount = $realTotalAmount;
            
            // Relations chargées
            
            return view('admin.supplies.show', compact('supply'));
        } catch (\Exception $e) {
            // Erreur
            
            return back()->with('error', 'Une erreur est survenue lors du chargement des détails.');
        }
    }

    public function showValidateForm(Supply $supply)
    {
        try {
            if ($supply->status !== 'pending') {
                return redirect()
                    ->route('admin.supplies.index')
                    ->with('error', 'Cet approvisionnement ne peut plus être validé.');
            }

            return view('admin.supplies.validate', compact('supply'));
        } catch (\Exception $e) {
            // Erreur validation form
            return redirect()
                ->route('admin.supplies.index')
                ->with('error', 'Une erreur est survenue lors de l\'affichage du formulaire.');
        }
    }

    public function validateSupply(Supply $supply)
    {
        try {
            // Validation de l'approvisionnement

            if ($supply->status !== 'pending') {
                throw new \Exception('Cet approvisionnement ne peut plus être validé.');
            }

            // Mise à jour du statut de l'approvisionnement
            $supply->status = 'validated';
            $supply->validator_id = auth()->id();
            $supply->validated_at = now();
            $supply->save();
            // Statut mis à jour

            // Utiliser le StockService pour traiter la validation
            $stockService = new StockService();
            $stockUpdateSuccess = $stockService->processSupplyValidation($supply);

            if (!$stockUpdateSuccess) {
                throw new \Exception('Erreur lors de la mise à jour des stocks.');
            }

            // Validation terminée

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'L\'approvisionnement a été validé avec succès.'
                ]);
            }

            return redirect()
                ->route('admin.supplies.index')
                ->with('success', 'L\'approvisionnement a été validé avec succès.');
        } catch (\Exception $e) {
            DB::rollBack();
            // Erreur validation
            
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Une erreur est survenue lors de la validation : ' . $e->getMessage()
                ], 422);
            }
            
            return redirect()
                ->route('admin.supplies.index')
                ->with('error', 'Une erreur est survenue lors de la validation : ' . $e->getMessage());
        }
    }

    public function showRejectForm(Supply $supply)
    {
        try {
            if ($supply->status !== 'pending') {
                return redirect()
                    ->route('admin.supplies.index')
                    ->with('error', 'Cet approvisionnement ne peut plus être rejeté.');
            }

            return view('admin.supplies.reject', compact('supply'));
        } catch (\Exception $e) {
            // Erreur reject form
            return redirect()
                ->route('admin.supplies.index')
                ->with('error', 'Une erreur est survenue lors de l\'affichage du formulaire.');
        }
    }

    public function rejectSupply(Request $request, Supply $supply)
    {
        try {
            // Rejet de l'approvisionnement
            
            DB::beginTransaction();

            if ($supply->status !== 'pending') {
                throw new \Exception('Cet approvisionnement ne peut plus être rejeté.');
            }

            $request->validate([
                'rejection_reason' => 'required|string|max:255'
            ]);

            $supply->status = 'rejected';
            $supply->rejection_reason = $request->rejection_reason;
            $supply->validator_id = auth()->id();
            $supply->validated_at = now();
            $supply->save();

            DB::commit();
            // Rejet terminé

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'L\'approvisionnement a été rejeté avec succès.'
                ]);
            }

            return redirect()
                ->route('admin.supplies.index')
                ->with('success', 'L\'approvisionnement a été rejeté avec succès.');

        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Une erreur est survenue lors du rejet : ' . $e->getMessage()
                ], 422);
            }

            return redirect()
                ->route('admin.supplies.index')
                ->with('error', 'Une erreur est survenue lors du rejet : ' . $e->getMessage());
        }
    }
}
