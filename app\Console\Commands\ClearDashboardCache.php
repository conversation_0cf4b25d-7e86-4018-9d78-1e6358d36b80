<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearDashboardCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dashboard:clear-cache {--user= : ID de l\'utilisateur spécifique}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vider le cache du tableau de bord comptable pour améliorer les performances';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🗑️ Vidage du cache du tableau de bord...');
        
        $userId = $this->option('user');
        
        if ($userId) {
            // Vider le cache pour un utilisateur spécifique
            $this->clearUserCache($userId);
        } else {
            // Vider tout le cache du tableau de bord
            $this->clearAllDashboardCache();
        }
        
        $this->info('✅ Cache du tableau de bord vidé avec succès !');
        
        // Afficher des conseils d'optimisation
        $this->displayOptimizationTips();
    }
    
    /**
     * Vider le cache pour un utilisateur spécifique
     */
    private function clearUserCache($userId)
    {
        $cacheKey = "accountant_dashboard_professional_{$userId}";
        
        if (Cache::has($cacheKey)) {
            Cache::forget($cacheKey);
            $this->line("🧹 Cache vidé pour l'utilisateur {$userId}");
        } else {
            $this->warn("⚠️ Aucun cache trouvé pour l'utilisateur {$userId}");
        }
        
        // Vider aussi les caches liés
        $relatedKeys = [
            "dashboard_stats_{$userId}",
            "monthly_sales_{$userId}",
            "payment_stats_{$userId}",
            "supply_chart_{$userId}",
            "performance_metrics_{$userId}",
            "trend_analysis_{$userId}"
        ];
        
        foreach ($relatedKeys as $key) {
            if (Cache::has($key)) {
                Cache::forget($key);
                $this->line("🧹 Cache lié vidé: {$key}");
            }
        }
    }
    
    /**
     * Vider tout le cache du tableau de bord
     */
    private function clearAllDashboardCache()
    {
        // Pour simplifier, on vide tout le cache
        // Dans un environnement de production, on pourrait être plus sélectif
        Cache::flush();
        $this->line('🧹 Tout le cache a été vidé');
        
        // Alternative plus sélective (commentée pour référence)
        /*
        $patterns = [
            'accountant_dashboard_professional_*',
            'dashboard_stats_*',
            'monthly_sales_*',
            'payment_stats_*',
            'supply_chart_*',
            'performance_metrics_*',
            'trend_analysis_*'
        ];
        
        // Note: Laravel ne supporte pas nativement les patterns de cache
        // Il faudrait utiliser Redis ou une autre solution pour cela
        */
    }
    
    /**
     * Afficher des conseils d'optimisation
     */
    private function displayOptimizationTips()
    {
        $this->newLine();
        $this->info('💡 Conseils d\'optimisation:');
        $this->line('   • Le cache est maintenant configuré pour 5 minutes');
        $this->line('   • Utilisez --user=ID pour vider le cache d\'un utilisateur spécifique');
        $this->line('   • Surveillez les performances avec les outils intégrés');
        $this->line('   • Considérez l\'optimisation des requêtes de base de données');
        
        $this->newLine();
        $this->info('🔧 Commandes utiles:');
        $this->line('   php artisan dashboard:clear-cache --user=1');
        $this->line('   php artisan cache:clear (pour tout le cache)');
        $this->line('   php artisan config:cache (pour optimiser la config)');
        $this->line('   php artisan route:cache (pour optimiser les routes)');
        
        $this->newLine();
        $this->info('📊 Monitoring:');
        $this->line('   • Utilisez les outils de performance intégrés dans le navigateur');
        $this->line('   • Surveillez les logs Laravel pour les requêtes lentes');
        $this->line('   • Activez le mode debug pour plus d\'informations');
    }
}
