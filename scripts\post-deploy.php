<?php

/**
 * Script de post-déploiement pour Gradis
 * Exécute les tâches de maintenance après le déploiement
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PostDeploymentScript
{
    private $app;
    private $startTime;
    
    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->initializeLaravel();
    }
    
    /**
     * Initialise l'application Laravel
     */
    private function initializeLaravel()
    {
        $this->app = require_once __DIR__ . '/../bootstrap/app.php';
        $this->app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    }
    
    /**
     * Exécute toutes les tâches de post-déploiement
     */
    public function run()
    {
        $this->log("🚀 Début des tâches de post-déploiement");
        
        try {
            $this->checkEnvironment();
            $this->optimizeApplication();
            $this->setupDatabase();
            $this->clearCaches();
            $this->optimizeAssets();
            $this->setupPermissions();
            $this->runHealthChecks();
            $this->setupCronJobs();
            
            $this->log("✅ Toutes les tâches de post-déploiement terminées avec succès");
            $this->logExecutionTime();
            
        } catch (Exception $e) {
            $this->error("❌ Erreur lors du post-déploiement: " . $e->getMessage());
            exit(1);
        }
    }
    
    /**
     * Vérifie l'environnement
     */
    private function checkEnvironment()
    {
        $this->log("🔍 Vérification de l'environnement...");
        
        // Vérifier que nous sommes en production
        if (config('app.env') !== 'production') {
            $this->warning("⚠️  L'application n'est pas en mode production");
        }
        
        // Vérifier la clé d'application
        if (empty(config('app.key'))) {
            throw new Exception("La clé d'application n'est pas définie");
        }
        
        // Vérifier la connexion à la base de données
        try {
            DB::connection()->getPdo();
            $this->log("✅ Connexion à la base de données OK");
        } catch (Exception $e) {
            throw new Exception("Impossible de se connecter à la base de données: " . $e->getMessage());
        }
        
        // Vérifier les extensions PHP requises
        $requiredExtensions = ['pdo', 'mbstring', 'openssl', 'tokenizer', 'xml', 'ctype', 'json', 'bcmath'];
        foreach ($requiredExtensions as $extension) {
            if (!extension_loaded($extension)) {
                throw new Exception("Extension PHP manquante: {$extension}");
            }
        }
        
        $this->log("✅ Environnement vérifié");
    }
    
    /**
     * Optimise l'application Laravel
     */
    private function optimizeApplication()
    {
        $this->log("⚡ Optimisation de l'application...");
        
        // Optimiser l'autoloader
        $this->runCommand('composer dump-autoload --optimize --no-dev');
        
        // Cache de configuration
        Artisan::call('config:cache');
        $this->log("✅ Cache de configuration créé");
        
        // Cache des routes
        Artisan::call('route:cache');
        $this->log("✅ Cache des routes créé");
        
        // Cache des vues
        Artisan::call('view:cache');
        $this->log("✅ Cache des vues créé");
        
        // Cache des événements
        Artisan::call('event:cache');
        $this->log("✅ Cache des événements créé");
        
        $this->log("✅ Application optimisée");
    }
    
    /**
     * Configure la base de données
     */
    private function setupDatabase()
    {
        $this->log("🗄️  Configuration de la base de données...");
        
        // Exécuter les migrations
        Artisan::call('migrate', ['--force' => true]);
        $this->log("✅ Migrations exécutées");
        
        // Seeder de production si nécessaire
        try {
            Artisan::call('db:seed', ['--class' => 'ProductionSeeder', '--force' => true]);
            $this->log("✅ Seeders de production exécutés");
        } catch (Exception $e) {
            $this->warning("⚠️  Aucun seeder de production trouvé ou erreur: " . $e->getMessage());
        }
        
        $this->log("✅ Base de données configurée");
    }
    
    /**
     * Nettoie les caches
     */
    private function clearCaches()
    {
        $this->log("🧹 Nettoyage des caches...");
        
        // Nettoyer le cache de l'application
        Cache::flush();
        $this->log("✅ Cache de l'application nettoyé");
        
        // Nettoyer les caches de fichiers
        Artisan::call('cache:clear');
        Artisan::call('view:clear');
        Artisan::call('route:clear');
        Artisan::call('config:clear');
        
        $this->log("✅ Tous les caches nettoyés");
    }
    
    /**
     * Optimise les assets
     */
    private function optimizeAssets()
    {
        $this->log("🎨 Optimisation des assets...");
        
        // Vérifier que les assets ont été compilés
        $manifestPath = public_path('build/manifest.json');
        if (!file_exists($manifestPath)) {
            $this->warning("⚠️  Le fichier manifest.json n'existe pas. Assurez-vous d'avoir exécuté 'npm run build'");
        } else {
            $this->log("✅ Assets compilés détectés");
        }
        
        // Optimiser les images si le service est disponible
        try {
            $assetService = app(\App\Services\AssetOptimizationService::class);
            $results = $assetService->optimizeAllAssets();
            $this->log("✅ Images optimisées: {$results['images_optimized']}, WebP générées: {$results['webp_generated']}");
        } catch (Exception $e) {
            $this->warning("⚠️  Erreur lors de l'optimisation des images: " . $e->getMessage());
        }
        
        $this->log("✅ Assets optimisés");
    }
    
    /**
     * Configure les permissions
     */
    private function setupPermissions()
    {
        $this->log("🔐 Configuration des permissions...");
        
        $directories = [
            storage_path(),
            storage_path('app'),
            storage_path('framework'),
            storage_path('logs'),
            base_path('bootstrap/cache')
        ];
        
        foreach ($directories as $directory) {
            if (is_dir($directory)) {
                chmod($directory, 0775);
                $this->log("✅ Permissions configurées pour: {$directory}");
            }
        }
        
        $this->log("✅ Permissions configurées");
    }
    
    /**
     * Exécute les vérifications de santé
     */
    private function runHealthChecks()
    {
        $this->log("🏥 Vérifications de santé...");
        
        // Vérifier l'espace disque
        $freeSpace = disk_free_space(base_path());
        $totalSpace = disk_total_space(base_path());
        $usedPercentage = (($totalSpace - $freeSpace) / $totalSpace) * 100;
        
        if ($usedPercentage > 90) {
            $this->warning("⚠️  Espace disque faible: {$usedPercentage}% utilisé");
        } else {
            $this->log("✅ Espace disque OK: {$usedPercentage}% utilisé");
        }
        
        // Vérifier les logs
        $logPath = storage_path('logs/laravel.log');
        if (file_exists($logPath)) {
            $logSize = filesize($logPath);
            if ($logSize > 100 * 1024 * 1024) { // 100MB
                $this->warning("⚠️  Fichier de log volumineux: " . round($logSize / 1024 / 1024, 2) . "MB");
            }
        }
        
        // Tester la connexion Redis si configuré
        if (config('cache.default') === 'redis') {
            try {
                Cache::store('redis')->put('health_check', 'ok', 60);
                $this->log("✅ Connexion Redis OK");
            } catch (Exception $e) {
                $this->warning("⚠️  Problème avec Redis: " . $e->getMessage());
            }
        }
        
        $this->log("✅ Vérifications de santé terminées");
    }
    
    /**
     * Configure les tâches cron
     */
    private function setupCronJobs()
    {
        $this->log("⏰ Configuration des tâches cron...");
        
        // Créer un fichier de rappel pour les tâches cron
        $cronFile = base_path('cron-setup.txt');
        $cronContent = "# Ajoutez cette ligne à votre crontab:\n";
        $cronContent .= "* * * * * cd " . base_path() . " && php artisan schedule:run >> /dev/null 2>&1\n";
        $cronContent .= "\n# Ou pour plus de logs:\n";
        $cronContent .= "* * * * * cd " . base_path() . " && php artisan schedule:run >> " . storage_path('logs/cron.log') . " 2>&1\n";
        
        file_put_contents($cronFile, $cronContent);
        $this->log("✅ Instructions cron créées dans: {$cronFile}");
    }
    
    /**
     * Exécute une commande système
     */
    private function runCommand($command)
    {
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception("Erreur lors de l'exécution de: {$command}");
        }
        
        return $output;
    }
    
    /**
     * Log un message
     */
    private function log($message)
    {
        echo $message . "\n";
        Log::info($message);
    }
    
    /**
     * Log un avertissement
     */
    private function warning($message)
    {
        echo $message . "\n";
        Log::warning($message);
    }
    
    /**
     * Log une erreur
     */
    private function error($message)
    {
        echo $message . "\n";
        Log::error($message);
    }
    
    /**
     * Log le temps d'exécution
     */
    private function logExecutionTime()
    {
        $executionTime = round(microtime(true) - $this->startTime, 2);
        $this->log("⏱️  Temps d'exécution: {$executionTime} secondes");
    }
}

// Exécuter le script
if (php_sapi_name() === 'cli') {
    $script = new PostDeploymentScript();
    $script->run();
} else {
    echo "Ce script doit être exécuté en ligne de commande.\n";
    exit(1);
}
