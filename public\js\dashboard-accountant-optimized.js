/**
 * Dashboard Comptable - Version Optimisée pour les Performances
 * Chargement intelligent et différé des ressources
 */

// Configuration globale d'optimisation
window.DASHBOARD_CONFIG = {
    lazyLoadThreshold: 0.1,
    chartLoadDelay: 100,
    exportLoadDelay: 200,
    cacheEnabled: true,
    debugMode: false
};

// Utilitaire de chargement de scripts optimisé
const ScriptLoader = {
    cache: new Map(),
    
    load(src, options = {}) {
        if (this.cache.has(src)) {
            return this.cache.get(src);
        }
        
        const promise = new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            script.defer = true;
            
            if (options.integrity) {
                script.integrity = options.integrity;
                script.crossOrigin = 'anonymous';
            }
            
            script.onload = () => {
                console.log(`✅ Script chargé: ${src}`);
                resolve();
            };
            
            script.onerror = () => {
                console.error(`❌ Erreur de chargement: ${src}`);
                reject(new Error(`Failed to load script: ${src}`));
            };
            
            document.head.appendChild(script);
        });
        
        this.cache.set(src, promise);
        return promise;
    }
};

// Gestionnaire de lazy loading optimisé
const LazyLoader = {
    observers: new Map(),
    
    observe(selector, callback, options = {}) {
        const elements = document.querySelectorAll(selector);
        if (elements.length === 0) return;
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    callback(entry.target);
                    if (options.once !== false) {
                        observer.unobserve(entry.target);
                    }
                }
            });
        }, {
            threshold: options.threshold || window.DASHBOARD_CONFIG.lazyLoadThreshold,
            rootMargin: options.rootMargin || '50px'
        });
        
        elements.forEach(el => observer.observe(el));
        this.observers.set(selector, observer);
    },
    
    disconnect(selector) {
        const observer = this.observers.get(selector);
        if (observer) {
            observer.disconnect();
            this.observers.delete(selector);
        }
    }
};

// Gestionnaire de graphiques optimisé
const ChartManager = {
    loaded: false,
    charts: new Map(),
    
    async loadChartJS() {
        if (this.loaded || window.Chart) {
            return Promise.resolve();
        }
        
        try {
            await ScriptLoader.load('https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js');
            this.loaded = true;
            console.log('📊 Chart.js chargé avec succès');
        } catch (error) {
            console.error('❌ Erreur lors du chargement de Chart.js:', error);
            throw error;
        }
    },
    
    async initChart(canvas, config) {
        await this.loadChartJS();
        
        if (!window.Chart) {
            throw new Error('Chart.js non disponible');
        }
        
        const chart = new Chart(canvas, config);
        this.charts.set(canvas.id, chart);
        return chart;
    },
    
    destroyChart(canvasId) {
        const chart = this.charts.get(canvasId);
        if (chart) {
            chart.destroy();
            this.charts.delete(canvasId);
        }
    }
};

// Gestionnaire d'export optimisé
const ExportManager = {
    loaded: false,
    
    async loadLibraries() {
        if (this.loaded) {
            return Promise.resolve();
        }
        
        // Afficher un indicateur de chargement léger
        const loader = this.showLoader();
        
        try {
            await Promise.all([
                ScriptLoader.load('https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js'),
                ScriptLoader.load('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js')
            ]);
            
            this.loaded = true;
            console.log('📦 Bibliothèques d\'export chargées');
        } catch (error) {
            console.error('❌ Erreur lors du chargement des bibliothèques d\'export:', error);
            throw error;
        } finally {
            this.hideLoader(loader);
        }
    },
    
    showLoader() {
        const loader = document.createElement('div');
        loader.className = 'export-loader';
        loader.innerHTML = `
            <div style="
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px;
                border-radius: 8px;
                z-index: 9999;
                font-family: Arial, sans-serif;
            ">
                <div style="text-align: center;">
                    <div style="margin-bottom: 10px;">📦</div>
                    <div>Chargement des outils d'export...</div>
                </div>
            </div>
        `;
        document.body.appendChild(loader);
        return loader;
    },
    
    hideLoader(loader) {
        if (loader && loader.parentNode) {
            loader.parentNode.removeChild(loader);
        }
    }
};

// Optimisations de performance
const PerformanceOptimizer = {
    init() {
        // Désactiver les animations pendant le chargement initial
        this.disableAnimationsDuringLoad();
        
        // Optimiser les images
        this.optimizeImages();
        
        // Précharger les ressources critiques
        this.preloadCriticalResources();
    },
    
    disableAnimationsDuringLoad() {
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-delay: 0.01ms !important;
                transition-duration: 0.01ms !important;
                transition-delay: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
        
        // Réactiver les animations après le chargement
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.head.removeChild(style);
            }, 100);
        });
    },
    
    optimizeImages() {
        // Lazy loading pour les images
        LazyLoader.observe('img[data-src]', (img) => {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        });
    },
    
    preloadCriticalResources() {
        // Précharger les CSS critiques
        const criticalCSS = [
            '/css/dashboard-professional-optimized.css'
        ];
        
        criticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = href;
            document.head.appendChild(link);
        });
    }
};

// Initialisation du dashboard
const DashboardInit = {
    async init() {
        console.log('🚀 Initialisation du dashboard comptable optimisé');
        
        // Optimisations de performance
        PerformanceOptimizer.init();
        
        // Initialiser le lazy loading pour les graphiques
        this.initLazyCharts();
        
        // Initialiser les boutons d'export
        this.initExportButtons();
        
        // Initialiser les sections lazy
        this.initLazySections();
        
        console.log('✅ Dashboard comptable initialisé');
    },
    
    initLazyCharts() {
        LazyLoader.observe('[id*="chart"], [class*="chart"]', async (element) => {
            try {
                await ChartManager.loadChartJS();
                // Initialiser le graphique spécifique
                if (window.initCharts && typeof window.initCharts === 'function') {
                    window.initCharts();
                }
            } catch (error) {
                console.error('Erreur lors de l\'initialisation du graphique:', error);
            }
        });
    },
    
    initExportButtons() {
        document.addEventListener('click', async (e) => {
            if (e.target.closest('.export-btn, [data-export]')) {
                e.preventDefault();
                
                try {
                    await ExportManager.loadLibraries();
                    // Exécuter la fonction d'export
                    const exportType = e.target.dataset.export || 'excel';
                    if (window.exportData && typeof window.exportData === 'function') {
                        window.exportData(exportType);
                    }
                } catch (error) {
                    console.error('Erreur lors de l\'export:', error);
                    alert('Erreur lors de l\'export. Veuillez réessayer.');
                }
            }
        });
    },
    
    initLazySections() {
        LazyLoader.observe('[data-lazy-section]', (section) => {
            section.classList.add('lazy-loaded');
            // Déclencher l'initialisation de la section si nécessaire
            const sectionType = section.dataset.lazySection;
            if (window[`init${sectionType.charAt(0).toUpperCase() + sectionType.slice(1)}`]) {
                window[`init${sectionType.charAt(0).toUpperCase() + sectionType.slice(1)}`]();
            }
        });
    }
};

// Auto-initialisation
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => DashboardInit.init());
} else {
    DashboardInit.init();
}

// Exposer les utilitaires globalement
window.DashboardOptimized = {
    ScriptLoader,
    LazyLoader,
    ChartManager,
    ExportManager,
    PerformanceOptimizer
};
