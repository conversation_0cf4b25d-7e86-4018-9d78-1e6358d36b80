/**
 * Script de diagnostic pour vérifier le chargement des assets
 * Aide à identifier les problèmes de chargement jQuery et autres dépendances
 */

(function() {
    'use strict';
    
    // Attendre que le DOM soit prêt
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔍 Diagnostic des assets - Début');
        
        // Vérifier jQuery
        checkJQuery();
        
        // Vérifier Bootstrap
        checkBootstrap();
        
        // Vérifier SweetAlert2
        checkSweetAlert();
        
        // Vérifier les meta tags
        checkMetaTags();
        
        // Vérifier les permissions
        checkPermissions();
        
        // Résumé final
        setTimeout(function() {
            console.log('🔍 Diagnostic des assets - Terminé');
        }, 1000);
    });
    
    /**
     * Vérifier jQuery
     */
    function checkJQuery() {
        if (typeof $ !== 'undefined' && typeof jQuery !== 'undefined') {
            console.log('✅ jQuery chargé - Version:', $.fn.jquery);
            
            // Tester une fonction jQuery basique
            try {
                $('body').length;
                console.log('✅ jQuery fonctionne correctement');
            } catch (e) {
                console.error('❌ jQuery chargé mais ne fonctionne pas:', e.message);
            }
        } else {
            console.error('❌ jQuery non chargé');
            
            // Suggérer des solutions
            console.log('💡 Solutions possibles:');
            console.log('   1. Vérifier la connexion internet');
            console.log('   2. Vérifier les headers CSP');
            console.log('   3. Vérifier les permissions policy');
        }
    }
    
    /**
     * Vérifier Bootstrap
     */
    function checkBootstrap() {
        if (typeof bootstrap !== 'undefined') {
            console.log('✅ Bootstrap chargé');
            
            // Tester les composants Bootstrap
            try {
                new bootstrap.Modal(document.createElement('div'));
                console.log('✅ Bootstrap Modal fonctionne');
            } catch (e) {
                console.warn('⚠️ Bootstrap Modal ne fonctionne pas:', e.message);
            }
        } else {
            console.error('❌ Bootstrap non chargé');
        }
    }
    
    /**
     * Vérifier SweetAlert2
     */
    function checkSweetAlert() {
        if (typeof Swal !== 'undefined') {
            console.log('✅ SweetAlert2 chargé');
        } else {
            console.error('❌ SweetAlert2 non chargé');
        }
    }
    
    /**
     * Vérifier les meta tags
     */
    function checkMetaTags() {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            console.log('✅ CSRF token présent:', csrfToken.getAttribute('content').substring(0, 10) + '...');
        } else {
            console.error('❌ CSRF token manquant');
        }
        
        const appUrl = document.querySelector('meta[name="app-url"]');
        if (appUrl) {
            console.log('✅ App URL présente:', appUrl.getAttribute('content'));
        } else {
            console.warn('⚠️ App URL manquante');
        }
    }
    
    /**
     * Vérifier les permissions
     */
    function checkPermissions() {
        // Vérifier les permissions policy
        const permissionsPolicy = document.querySelector('meta[http-equiv="Permissions-Policy"]');
        if (permissionsPolicy) {
            console.log('✅ Permissions Policy configurée:', permissionsPolicy.getAttribute('content'));
        }
        
        // Vérifier les erreurs de permissions dans la console
        const originalError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('Permissions policy violation')) {
                console.log('🚨 Violation de permissions policy détectée:', message);
                console.log('💡 Solution: Ajuster les headers de sécurité');
            }
            originalError.apply(console, args);
        };
    }
    
    /**
     * Fonction utilitaire pour tester les requêtes AJAX
     */
    window.testAjax = function() {
        if (typeof $ === 'undefined') {
            console.error('❌ jQuery non disponible pour le test AJAX');
            return;
        }
        
        console.log('🔄 Test AJAX en cours...');
        
        $.ajax({
            url: window.location.href,
            method: 'GET',
            timeout: 5000,
            success: function() {
                console.log('✅ Test AJAX réussi');
            },
            error: function(xhr, status, error) {
                console.error('❌ Test AJAX échoué:', status, error);
                
                if (status === 'timeout') {
                    console.log('💡 Timeout - vérifier la connexion réseau');
                } else if (xhr.status === 0) {
                    console.log('💡 Erreur réseau - vérifier les CORS ou CSP');
                }
            }
        });
    };
    
    /**
     * Fonction pour tester les formulaires
     */
    window.testFormSubmission = function() {
        console.log('🔄 Test de soumission de formulaire...');
        
        const forms = document.querySelectorAll('form');
        if (forms.length === 0) {
            console.log('ℹ️ Aucun formulaire trouvé sur cette page');
            return;
        }
        
        forms.forEach(function(form, index) {
            console.log(`📝 Formulaire ${index + 1}:`, {
                action: form.action,
                method: form.method,
                hasCSRF: !!form.querySelector('input[name="_token"]'),
                fieldCount: form.querySelectorAll('input, select, textarea').length
            });
        });
    };
    
    /**
     * Fonction pour vérifier les événements
     */
    window.checkEvents = function() {
        console.log('🔄 Vérification des événements...');
        
        // Tester les événements de clic
        const buttons = document.querySelectorAll('button, .btn');
        console.log(`🔘 ${buttons.length} boutons trouvés`);
        
        // Tester les événements de formulaire
        const forms = document.querySelectorAll('form');
        console.log(`📝 ${forms.length} formulaires trouvés`);
        
        // Tester les événements jQuery si disponible
        if (typeof $ !== 'undefined') {
            const jqueryEvents = $._data(document, 'events');
            if (jqueryEvents) {
                console.log('🎯 Événements jQuery attachés:', Object.keys(jqueryEvents));
            } else {
                console.log('ℹ️ Aucun événement jQuery global trouvé');
            }
        }
    };
    
    // Exposer les fonctions de test globalement
    window.diagnostic = {
        testAjax: window.testAjax,
        testFormSubmission: window.testFormSubmission,
        checkEvents: window.checkEvents
    };
    
    console.log('🛠️ Fonctions de diagnostic disponibles:');
    console.log('   - diagnostic.testAjax()');
    console.log('   - diagnostic.testFormSubmission()');
    console.log('   - diagnostic.checkEvents()');
    
})();
