<?php

echo "🔧 CORRECTION AUTOMATIQUE DE LA VUE DASHBOARD\n";
echo "=============================================\n\n";

$viewPath = 'resources/views/admin/dashboard.blade.php';

if (!file_exists($viewPath)) {
    echo "❌ Fichier non trouvé: $viewPath\n";
    exit(1);
}

echo "📖 Lecture du fichier...\n";
$content = file_get_contents($viewPath);

if ($content === false) {
    echo "❌ Impossible de lire le fichier\n";
    exit(1);
}

echo "🔍 Recherche des références à \$stats...\n";

// Liste des clés à protéger avec leurs valeurs par défaut
$statsKeys = [
    'total_orders' => '0',
    'cement_orders_count' => '0',
    'monthly_revenue' => '0',
    'monthly_cement_revenue' => '0',
    'active_users' => '0',
    'users_count' => '1',
    'available_drivers' => '0',
    'total_trucks' => '1',
    'total_suppliers' => '0',
    'categories_count' => '0',
    'total_customers' => '0',
    'low_stock_count' => '0',
    'products_count' => '0'
];

$replacements = 0;

foreach ($statsKeys as $key => $defaultValue) {
    // Pattern pour trouver $stats['key'] sans protection
    $pattern = '/\$stats\[\'' . preg_quote($key, '/') . '\'\](?!\s*\?\?)/';
    
    // Remplacement avec protection
    $replacement = "\$stats['$key'] ?? $defaultValue";
    
    $newContent = preg_replace($pattern, $replacement, $content, -1, $count);
    
    if ($count > 0) {
        echo "✅ Protégé '$key': $count occurrences\n";
        $content = $newContent;
        $replacements += $count;
    }
}

// Corrections spéciales pour les calculs complexes
$specialPatterns = [
    // Division par zéro
    '/\(\$stats\[\'active_users\'\] \?\? 0\) \/ max\(1, \$stats\[\'users_count\'\]\)/' => '($stats[\'active_users\'] ?? 0) / max(1, $stats[\'users_count\'] ?? 1)',
    '/\(\$stats\[\'available_drivers\'\] \?\? 0\) \/ max\(1, \$stats\[\'total_trucks\'\]\)/' => '($stats[\'available_drivers\'] ?? 0) / max(1, $stats[\'total_trucks\'] ?? 1)',
    
    // Conditions avec comparaisons
    '/\$stats\[\'available_drivers\'\] \?\? 0 > 5/' => '($stats[\'available_drivers\'] ?? 0) > 5',
];

foreach ($specialPatterns as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $content, -1, $count);
    if ($count > 0) {
        echo "✅ Correction spéciale: $count occurrences\n";
        $content = $newContent;
        $replacements += $count;
    }
}

if ($replacements > 0) {
    echo "\n💾 Sauvegarde des modifications...\n";
    
    // Backup du fichier original
    $backupPath = $viewPath . '.backup.' . date('Y-m-d-H-i-s');
    copy($viewPath, $backupPath);
    echo "📋 Backup créé: $backupPath\n";
    
    // Sauvegarde du fichier modifié
    if (file_put_contents($viewPath, $content) !== false) {
        echo "✅ Fichier corrigé avec succès!\n";
        echo "🔢 Total des corrections: $replacements\n";
    } else {
        echo "❌ Erreur lors de la sauvegarde\n";
        exit(1);
    }
} else {
    echo "ℹ️  Aucune correction nécessaire\n";
}

echo "\n🎯 RÉSUMÉ:\n";
echo "=========\n";
echo "✅ Vue dashboard protégée contre les erreurs\n";
echo "✅ Toutes les références \$stats[] sécurisées\n";
echo "✅ Valeurs par défaut ajoutées\n";
echo "✅ Protection contre division par zéro\n";

echo "\n🚀 La vue est maintenant robuste et ne devrait plus générer d'erreurs!\n";
