<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class AssetOptimizationService
{
    /**
     * Optimise les images uploadées
     */
    public function optimizeImage(string $imagePath, int $quality = 85): bool
    {
        if (!File::exists($imagePath)) {
            return false;
        }

        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return false;
        }

        $mimeType = $imageInfo['mime'];
        
        switch ($mimeType) {
            case 'image/jpeg':
                return $this->optimizeJpeg($imagePath, $quality);
            case 'image/png':
                return $this->optimizePng($imagePath);
            case 'image/webp':
                return $this->optimizeWebp($imagePath, $quality);
            default:
                return false;
        }
    }

    /**
     * Optimise une image JPEG
     */
    private function optimizeJpeg(string $path, int $quality): bool
    {
        $image = imagecreatefromjpeg($path);
        if (!$image) return false;

        $result = imagejpeg($image, $path, $quality);
        imagedestroy($image);
        
        return $result;
    }

    /**
     * Optimise une image PNG
     */
    private function optimizePng(string $path): bool
    {
        $image = imagecreatefrompng($path);
        if (!$image) return false;

        // Activer la compression PNG
        imagesavealpha($image, true);
        $result = imagepng($image, $path, 6); // Niveau de compression 6
        imagedestroy($image);
        
        return $result;
    }

    /**
     * Optimise une image WebP
     */
    private function optimizeWebp(string $path, int $quality): bool
    {
        $image = imagecreatefromwebp($path);
        if (!$image) return false;

        $result = imagewebp($image, $path, $quality);
        imagedestroy($image);
        
        return $result;
    }

    /**
     * Redimensionne une image si elle est trop grande
     */
    public function resizeIfNeeded(string $imagePath, int $maxWidth = 1920, int $maxHeight = 1080): bool
    {
        if (!File::exists($imagePath)) {
            return false;
        }

        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return false;
        }

        [$width, $height] = $imageInfo;
        
        // Si l'image est déjà dans les bonnes dimensions
        if ($width <= $maxWidth && $height <= $maxHeight) {
            return true;
        }

        // Calculer les nouvelles dimensions en gardant le ratio
        $ratio = min($maxWidth / $width, $maxHeight / $height);
        $newWidth = intval($width * $ratio);
        $newHeight = intval($height * $ratio);

        return $this->resizeImage($imagePath, $newWidth, $newHeight);
    }

    /**
     * Redimensionne une image
     */
    private function resizeImage(string $path, int $newWidth, int $newHeight): bool
    {
        $imageInfo = getimagesize($path);
        $mimeType = $imageInfo['mime'];

        // Créer l'image source
        switch ($mimeType) {
            case 'image/jpeg':
                $source = imagecreatefromjpeg($path);
                break;
            case 'image/png':
                $source = imagecreatefrompng($path);
                break;
            case 'image/webp':
                $source = imagecreatefromwebp($path);
                break;
            default:
                return false;
        }

        if (!$source) return false;

        // Créer la nouvelle image
        $destination = imagecreatetruecolor($newWidth, $newHeight);
        
        // Préserver la transparence pour PNG
        if ($mimeType === 'image/png') {
            imagealphablending($destination, false);
            imagesavealpha($destination, true);
            $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
            imagefilledrectangle($destination, 0, 0, $newWidth, $newHeight, $transparent);
        }

        // Redimensionner
        imagecopyresampled(
            $destination, $source,
            0, 0, 0, 0,
            $newWidth, $newHeight,
            imagesx($source), imagesy($source)
        );

        // Sauvegarder
        $result = false;
        switch ($mimeType) {
            case 'image/jpeg':
                $result = imagejpeg($destination, $path, 85);
                break;
            case 'image/png':
                $result = imagepng($destination, $path, 6);
                break;
            case 'image/webp':
                $result = imagewebp($destination, $path, 85);
                break;
        }

        imagedestroy($source);
        imagedestroy($destination);

        return $result;
    }

    /**
     * Génère des versions WebP des images
     */
    public function generateWebpVersion(string $imagePath): ?string
    {
        if (!File::exists($imagePath)) {
            return null;
        }

        $pathInfo = pathinfo($imagePath);
        $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';

        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return null;
        }

        $mimeType = $imageInfo['mime'];
        
        // Créer l'image source
        switch ($mimeType) {
            case 'image/jpeg':
                $source = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $source = imagecreatefrompng($imagePath);
                break;
            default:
                return null;
        }

        if (!$source) return null;

        // Convertir en WebP
        $result = imagewebp($source, $webpPath, 85);
        imagedestroy($source);

        return $result ? $webpPath : null;
    }

    /**
     * Nettoie les anciens fichiers temporaires
     */
    public function cleanupTempFiles(int $olderThanHours = 24): int
    {
        $tempPath = storage_path('app/temp');
        if (!File::exists($tempPath)) {
            return 0;
        }

        $cutoffTime = now()->subHours($olderThanHours);
        $deletedCount = 0;

        $files = File::allFiles($tempPath);
        foreach ($files as $file) {
            if (File::lastModified($file->getPathname()) < $cutoffTime->timestamp) {
                File::delete($file->getPathname());
                $deletedCount++;
            }
        }

        return $deletedCount;
    }

    /**
     * Optimise tous les assets du projet
     */
    public function optimizeAllAssets(): array
    {
        $results = [
            'images_optimized' => 0,
            'webp_generated' => 0,
            'errors' => []
        ];

        // Optimiser les images dans public/images
        $imagesPath = public_path('images');
        if (File::exists($imagesPath)) {
            $images = File::allFiles($imagesPath);
            
            foreach ($images as $image) {
                $imagePath = $image->getPathname();
                
                try {
                    if ($this->optimizeImage($imagePath)) {
                        $results['images_optimized']++;
                        
                        // Générer version WebP
                        if ($this->generateWebpVersion($imagePath)) {
                            $results['webp_generated']++;
                        }
                    }
                } catch (\Exception $e) {
                    $results['errors'][] = "Erreur avec {$imagePath}: " . $e->getMessage();
                }
            }
        }

        return $results;
    }

    /**
     * Vérifie la taille des fichiers uploadés
     */
    public function validateFileSize(string $filePath, int $maxSizeMB = 10): bool
    {
        if (!File::exists($filePath)) {
            return false;
        }

        $fileSizeBytes = File::size($filePath);
        $maxSizeBytes = $maxSizeMB * 1024 * 1024;

        return $fileSizeBytes <= $maxSizeBytes;
    }

    /**
     * Vérifie le type de fichier
     */
    public function validateFileType(string $filePath, array $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf']): bool
    {
        if (!File::exists($filePath)) {
            return false;
        }

        $extension = strtolower(File::extension($filePath));
        return in_array($extension, $allowedTypes);
    }
}
