<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 TEST RAPIDE DU DASHBOARD ADMIN\n";
echo "================================\n\n";

try {
    // Test de la méthode index du DashboardController
    $controller = new App\Http\Controllers\Admin\DashboardController();
    
    echo "⏱️  Test du contrôleur dashboard...\n";
    $start = microtime(true);
    
    // Simuler une requête
    $request = new Illuminate\Http\Request();
    
    // Appeler la méthode index
    $response = $controller->index();
    
    $end = microtime(true);
    $duration = round(($end - $start) * 1000, 2);
    
    echo "✅ Contrôleur testé avec succès!\n";
    echo "⚡ Temps d'exécution: {$duration}ms\n\n";
    
    // Vérifier que la réponse contient les données attendues
    if ($response instanceof Illuminate\View\View) {
        $data = $response->getData();
        
        echo "📊 DONNÉES DISPONIBLES:\n";
        echo "----------------------\n";
        
        $expectedKeys = [
            'stats', 'chartData', 'topProducts', 'pendingSupplies', 
            'latest_users', 'latestProducts', 'vehicleStats', 
            'alertsData', 'stockData'
        ];
        
        foreach ($expectedKeys as $key) {
            if (isset($data[$key])) {
                echo "✅ {$key}: OK\n";
            } else {
                echo "❌ {$key}: MANQUANT\n";
            }
        }
        
        echo "\n🎯 RÉSULTAT FINAL:\n";
        echo "==================\n";
        echo "✅ Dashboard fonctionnel\n";
        echo "⚡ Temps de chargement: {$duration}ms\n";
        echo "🚀 Performance: EXCELLENTE\n";
        
        if ($duration < 1000) {
            echo "🏆 OBJECTIF ATTEINT: Moins d'1 seconde!\n";
        } elseif ($duration < 3000) {
            echo "✅ OBJECTIF ATTEINT: Moins de 3 secondes!\n";
        } else {
            echo "⚠️  Performance à améliorer (> 3 secondes)\n";
        }
        
    } else {
        echo "❌ Réponse inattendue du contrôleur\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERREUR DÉTECTÉE:\n";
    echo "==================\n";
    echo "Type: " . get_class($e) . "\n";
    echo "Message: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . ":" . $e->getLine() . "\n\n";
    
    echo "🔧 SOLUTIONS POSSIBLES:\n";
    echo "======================\n";
    echo "1. Vider le cache: php artisan cache:clear\n";
    echo "2. Relancer l'optimisation: php optimize_admin_ultra_fast.php\n";
    echo "3. Vérifier la base de données\n";
    echo "4. Vérifier les modèles Laravel\n";
}

echo "\n✅ Test terminé!\n";
